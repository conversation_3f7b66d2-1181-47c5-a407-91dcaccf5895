# FrontEndMain

##Description
This package contains the assets for frontend. This contains both iOS and Android assets.
Follow the instructions given here to start up the package on a simulator:

- [iOS](https://reactnative.dev/docs/environment-setup):

  1. Select the `React Native CLI Quickstart` tab
  2. Choose `macOS` for development OS and `iOS` for target OS
  3. Install the dependencies
  4. Update you Xcode to newest version if possible
  5. Install `cocaPods`

     ```
     sudo gem install cocoapods
     ```

  6. Switch to `iOS` directory in the package and run

     ```
     pod install
     ```

  7. Skip the creating new application part
  8. Follow the steps described for running React Native Application

- [Android](https://reactnative.dev/docs/environment-setup)
  1. Select the `React Native CLI Quickstart` tab
  2. Choose your laptop/pc OS for development OS and `Android` for target OS
  3. Install the required dependencies
  4. Follow steps described in android development evnironment, skip any step if already completed or installed before
  5. Skip the creating new application part
  6. Follow the steps given in preparing android devices, strong suggestion to use a virtual device.
  7. Follow the steps described for running React Native Application

## Naming conventions

- Folder structure
  1. src:
     This folder contains all the source code of the project
  2. src/apis:
     - This folder will contain all the apis required the application.
     - These apis will be divided based on the [API Doc]()
     - All the file name should follow this convention `nameShouldBeLikeThis.ts`
  3. src/components:
     - This folder will contain all the renderable screens of the app.
     - Each screen will be a subfolder in the `src` folder and will contain all the files needed for the screen,
     - Keep each file as small as possible
     - All the file name should follow this convention `NameShouldBeLikeThis.tsx`
  4. src/config:
     - All the file name should follow this convention `nameShouldBeLikeThis.ts`
     -
  5. src/static:
     - This folder contains all the static files
     - Each folder will be divied based on the screens and will contain the respective static assets
     - Static assets will include the images, static data for each screens until the respective backend
     - Folder names will be `NameShouldBeLikeThis` and file name will follow `nameShouldBeLikeThis`
  6. src/styles:
     - This folder will contain all the styles associated with each screens and components
     - This will again be divided into sub folders based on the components and each `Style.js` file in the folder will contain style for the component
  7. src/types:
     - This folder will contain custom types required for all the files
     - These will be divided based on the types, example:
       - JSON.ts: will contain types you define that you think are necessary for dealing with JSON type objects
     - All the file name should follow this convention `NameShouldBeLikeThis.ts / .d.ts`
  8. src/utils:
     - This folder will contain all the helper functions that will be used across different files
     - All the file name should follow this convention `nameShouldBeLikeThis.ts`

// Testing Vasants changes
