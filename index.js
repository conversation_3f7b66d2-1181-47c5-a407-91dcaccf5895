/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';

const originalWarn = console.warn;
console.warn = (...args) => {
  const ignoreWarnings = ['ViewPortDetector: Invalid measurements received'];
  if (!ignoreWarnings.some(entry => args[0]?.includes(entry))) {
    originalWarn(...args);
  }
};

AppRegistry.registerComponent(appName, () => App);
