{"name": "AwesomeProject", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "postinstall": "patch-package && react-native setup-ios-permissions && pod-install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.11", "@react-native-camera-roll/camera-roll": "^5.7.2", "@react-native-firebase/analytics": "^21.7.1", "@react-native-firebase/app": "^21.7.1", "@react-native-firebase/crashlytics": "^21.7.1", "@react-native-firebase/messaging": "^21.7.1", "@react-native-picker/picker": "github:react-native-picker/picker", "@react-navigation/bottom-tabs": "^6.5.3", "@react-navigation/drawer": "^6.5.7", "@react-navigation/material-bottom-tabs": "^6.2.11", "@react-navigation/material-top-tabs": "^6.5.2", "@react-navigation/native": "^6.1.2", "@react-navigation/native-stack": "^6.9.8", "@react-navigation/stack": "^6.3.11", "@reduxjs/toolkit": "^1.9.3", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.7", "@shopify/flash-list": "^1.7.1", "@shopify/react-native-skia": "^1.12.3", "@tanstack/react-query": "^4.22.0", "axios": "^1.2.6", "buffer": "^6.0.3", "ffmpeg-kit-react-native": "^6.0.2", "install": "^0.13.0", "moment-timezone": "^0.5.45", "npm": "^10.9.0", "react": "18.0.0", "react-native": "0.71.16", "react-native-calendars": "1.1275.0", "react-native-collapsible-tab-view": "^6.2.1", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "^5.0.7", "react-native-fast-image": "^8.6.3", "react-native-file-access": "^3.1.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.9.0", "react-native-health": "^1.19.0", "react-native-health-connect": "^3.3.3", "react-native-image-crop-picker": "^0.42.0", "react-native-image-picker": "7.1.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-modal": "^13.0.1", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^6.2.0", "react-native-paper": "^5.1.3", "react-native-permissions": "^3.7.3", "react-native-picker-select": "^9.0.1", "react-native-popover-view": "^5.1.9", "react-native-reanimated": "^2.14.1", "react-native-safe-area-context": "^4.4.1", "react-native-screens": "^3.18.2", "react-native-shimmer-placeholder": "^2.0.9", "react-native-simple-toast": "^1.1.4", "react-native-swiper-flatlist": "^3.2.5", "react-native-tab-view": "^3.3.4", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^9.2.0", "react-native-video": "5.2.1", "react-native-video-cache": "^2.7.4", "react-native-viewport-detector": "1.0.7", "react-native-vision-camera": "4.2.0", "react-native-webview": "^13.12.5", "react-query": "^3.39.3", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.3", "@types/jest": "^29.2.5", "@types/react": "^18.0.26", "@types/react-native": "^0.71.0", "@types/react-native-video": "^5.0.20", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^26.6.3", "eslint": "^7.32.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "0.73.10", "patch-package": "^8.0.0", "pod-install": "^0.1.38", "postinstall-postinstall": "^2.1.0", "react-test-renderer": "18.0.0", "typescript": "^4.9.4"}, "jest": {"preset": "react-native"}, "reactNativePermissionsIOS": ["Calendars", "Camera", "LocationAccuracy", "LocationWhenInUse", "Microphone", "Notifications", "PhotoLibrary", "PhotoLibraryAddOnly"]}