diff --git a/node_modules/ffmpeg-kit-react-native/ffmpeg-kit-react-native.podspec b/node_modules/ffmpeg-kit-react-native/ffmpeg-kit-react-native.podspec
index 889d3e8..359df3c 100644
--- a/node_modules/ffmpeg-kit-react-native/ffmpeg-kit-react-native.podspec
+++ b/node_modules/ffmpeg-kit-react-native/ffmpeg-kit-react-native.podspec
@@ -20,116 +20,9 @@ Pod::Spec.new do |s|
 
   s.dependency "React-Core"
 
-  s.subspec 'min' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-min', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'min-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-min', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
-  s.subspec 'min-gpl' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-min-gpl', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'min-gpl-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-min-gpl', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
   s.subspec 'https' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-https', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'https-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-https', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
-  s.subspec 'https-gpl' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-https-gpl', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'https-gpl-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-https-gpl', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
-  s.subspec 'audio' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-audio', "6.0"
-      ss.ios.deployment_target = '12.1'
+    ss.source_files = '**/FFmpegKitReactNativeModule.m', '**/FFmpegKitReactNativeModule.h'
+    ss.dependency 'hpsurani-ffmpeg-kit-ios-https', "6.0.2"
+    ss.ios.deployment_target = '12.1'
   end
-
-  s.subspec 'audio-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-audio', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
-  s.subspec 'video' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-video', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'video-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-video', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
-  s.subspec 'full' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-full', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'full-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-full', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
-  s.subspec 'full-gpl' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-full-gpl', "6.0"
-      ss.ios.deployment_target = '12.1'
-  end
-
-  s.subspec 'full-gpl-lts' do |ss|
-      ss.source_files      = '**/FFmpegKitReactNativeModule.m',
-                             '**/FFmpegKitReactNativeModule.h'
-      ss.dependency 'ffmpeg-kit-ios-full-gpl', "6.0.LTS"
-      ss.ios.deployment_target = '10'
-  end
-
 end
