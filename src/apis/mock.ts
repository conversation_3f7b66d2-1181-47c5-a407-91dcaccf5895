function mockSuccess(value: object) {
    return new Promise((resolve) => {
      setTimeout(() => resolve(value), 2000);
    });
};
  
function mockFailure(value: object) {
    return new Promise((resolve, reject) => {
        setTimeout(() => reject(value), 2000);
    });
};

export function login( email: string, password: string, isSuccess?: boolean){
    if (!isSuccess) {
        return mockFailure({ error: 500, message: 'Something went wrong!' });
    }

    return mockSuccess({ auth_token: 'successToken' });
};

export function createAccount(fName:string, email: string, password: string, isSuccess?: boolean) {
    if (!isSuccess) {
      return mockFailure({ error: 500, message: 'Something went wrong!' });
    }
  
    return mockSuccess({ auth_token: 'successToken' });
};

function getAuthToken(isSuccess?: boolean) {
    if(!isSuccess) {
        return null;
    }
    return 'successToken';
}

export function getHomeFeed(isSuccess?: boolean){
    const token = getAuthToken(isSuccess);

    if(token){
        return mockSuccess({
            feed: [{
                userName: "<PERSON>",
                postType: "Image",
                comments: 5,
                isLiked: true
            },
            {
                userName: "<PERSON>",
                postType: "Video",
                comments: 4,
                isLiked: false
            },
            {
                user<PERSON>ame: "<PERSON>",
                postType: "Image",
                comments: 5,
                isLiked: true
            }]
        });
    };

    return mockFailure({
        error: "403",
        message: "Access denied"
    })
}

