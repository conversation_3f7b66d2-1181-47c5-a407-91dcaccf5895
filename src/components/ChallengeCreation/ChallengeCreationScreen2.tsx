import React, {useState, useEffect, useRef} from 'react';
import {
  Text,
  View,
  FlatList,
  TouchableOpacity,
  TouchableHighlight,
  StyleSheet,
  TextInput,
  ScrollView,
  Button,
  Image,
  Platform,
  Dimensions,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Alert,
} from 'react-native';
import {useRoute,useFocusEffect} from '@react-navigation/native';
import Video from 'react-native-video';
import moment from 'moment-timezone';

// import Icon from 'react-native-vector-icons/Feather';

import FonteAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import Modal from 'react-native-modal';
import TagFriendsScreen from '../TagFriends/TagFriendsScreen';
import VisibilitySettingsScreen from '../Common/Visibilitysettings';
import DifficultyRatingComponent from '../Common/DifficultyRatingComponent';
import {useDispatch, useSelector} from 'react-redux';
import {
  addChallengeMedia,
  createNewChallenge,
  updateChallenge,
} from '../../redux/Challenge/ChallengeAction';
import {
  resetAddMediaSuccess,
  resetCreateSuccess,
  resetUpdateSuccess,
} from '../../redux/Challenge/ChallengeSlice';
import acrossAllScreens from '../../styles/acrossAllScreens';
import CommonFontstyles from '../../styles/CommonFontStyles';
import SignUpStyle from '../../styles/SignUp';
import DatePicker from 'react-native-date-picker';
import PointsSourceScreen from '../Common/PointsSource';
import PointSortScreen from '../Common/PointSortScreen';
import {muteVideo} from '../../utils/media';
import {useAppSelector} from '../../redux/Store';
import Toast from 'react-native-toast-message';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import analytics from "@react-native-firebase/analytics";
import LeaderboardTypeScreen from '../Common/LeaderboardTypeScreen';
import ImportPointDataScreen from '../Common/ImportPointDataScreen';
import AiGenerationModal, {AIGeneratedData} from '../Common/AiGenerationModal';

const {width} = Dimensions.get('window');
export function ChallengeCreationScreen2({navigation, route}: any) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [pointsTitle, setPointsTitle] = useState('');
  const [pointsDetail, setPointsDetail] = useState('');
  const [points, setPoints] = useState('');

  const [startDate, setStartDate] = useState('TBD');
  const [endDate, setEndDate] = useState('TBD');
  const [friends, setFriends] = useState('TBD');
  const [visibility, setVisibility] = useState('PUBLIC');
  const [leaderboardType, setLeaderboardType] = useState('Accumulation');
  const [pointScoringType, setPointScoringType] = useState('1 Point Per Post');
  const [externalApp, setExternalApp] = useState('');
  const [isLeaderboardModalVisible, setLeaderboardModalVisible] = useState(false);
  const [isLeaderboardToggled, setIsLeaderboardToggled] = useState(false);
  const [isLeaderboardSettingsToggled, setIsLeaderboardSettingsToggled] = useState(false);
  const [pointScoringToggle, setPointScoringToggle] = useState(false);
  const [isCountAsPostToggled, setIsCountAsPostToggled] = useState(false);
  const [isOptionsToggled, setIsOptionsToggled] = useState(false);
  const [pointSort, setPointSort] = useState('Descending');
  const [isPointSortToggled, setIsPointSortToggled] = useState(true);
  const [selectedTags, setSelectedTags] = useState([]); 
  const [importDataType, setImportDataType] = useState('');
  const [isImportDataModalVisible, setImportDataModalVisible] = useState(false);

  const [rating, setRating] = useState(3); // Initial rating state

  const inputRef = useRef<TextInput>(null);
  const [maxRemaianDescLength, setMaxRemainDescLength] = useState(200);
  const [height, setHeight] = useState(48);
  const [isFriendsModalVisible, setFriendsModalVisible] = useState(false);
  const [openTimePicker, setOpenTimePicker] = useState(false);
  const [time, setTime] = useState(new Date());
  const [selectedMedia, setSelectedMedia] = useState([]);
  const [loadingItems, setLoadingItems] = useState<{[key: string]: boolean}>(
    {},
  );
  const [playingIndex, setPlayingIndex] = useState<number | null>(null);
  const [titleError, setTitleError] = useState('');
  const [pointTitleError, setPointTitleError] = useState('');
  const [pointDescError, setPointDescError] = useState('');
  const [descError, setDescError] = useState('');
  const [mediaError, setMediaError] = useState('');
  const [loading, setLoading] = useState(false);
  const challengeStoreState = useAppSelector(state => state.challenge);
  const authState = useAppSelector(state => state.auth);
  const {userId} = authState;
  const {challengeDraft, isAddMediaSuccess, isUpdateSuccess} =
    challengeStoreState;
  const [isAiGenerateOpen, setAiGenerateModal] = useState(false);

  const onAiGeneratedConfirm = (data: AIGeneratedData) => {
    setTitle(data.title);
    setDescription(data.description);
    setPointsTitle(data.pointTitle);
    setAiGenerateModal(false);
  };

  useEffect(() => {
    analytics().logScreenView({
      screen_name: 'ChallengeCreationScreen',
      screen_class: 'ChallengeCreationScreen',
    });
  }, []);

  const handleVideoTap = (index: number) => {
    setPlayingIndex(prevIndex => (prevIndex === index ? null : index)); // Toggles play/pause
  };

  const toggleLeaderboardModal = () => {
    setLeaderboardModalVisible(!isLeaderboardModalVisible);
  };

  const updateLeaderboardType = (newLeaderboardType: any) => {
    setLeaderboardType(newLeaderboardType);
    console.log('Selected leaderboardType:', leaderboardType);
  };
  const toggleImportDataModal = () => {
    setImportDataModalVisible(!isImportDataModalVisible);
  };
  const updateImportData = (newImportData: any) => {
    setImportDataType(newImportData);
    console.log('Selected Import Data:', pointSort);
  };

  const pointSortTogglePress = () => {
    const nextToggleState = !isPointSortToggled;
    setIsPointSortToggled(nextToggleState);
  setPointSort(nextToggleState ? 'Descending' : 'Ascending');
  console.log('Selected point Sort:', nextToggleState ? 'Descending' : 'Ascending');
  };
 

  const toggleFriendsModal = () => {
    setFriendsModalVisible(!isFriendsModalVisible);
  };
  const updateFriends = (newFriends: any) => {
    // Join the array of friend names with a space followed by '@'
    const friendsString = newFriends.map((name: any) => `@${name}`).join(' ');
    setFriends(friendsString);
  };
 
  useEffect(() => {
    if (route.params?.date && route.params?.type === 'start') {
      setStartDate(route.params.date);
    } else if (route.params?.date && route.params?.type === 'end') {
      setEndDate(route.params.date);
    }
  }, [route.params]);
  let challengeValue: any;
  const dispatch = useDispatch();
  const sdate = new Date();
  const edate = new Date(endDate);
  console.log(sdate, typeof sdate, edate);

  const imagePath = route.params?.imagePath || null;
  // const imageURI = imagePath
  //   ? Platform.OS === 'android'
  //     ? `file://${imagePath}`
  //     : imagePath
  //   : null;
  // console.log('imagePath', imagePath, route.params?.imagePath);

  const handleTextChange = (newText: any) => {
    setDescription(newText);
    setMaxRemainDescLength(200 - newText.length);
  };
  const handleContentSizeChange = (event: any) => {
    const newHeight = event.nativeEvent.contentSize.height;
    if (newHeight > 48) {
      // Ensures the height is not less than 48
      setHeight(newHeight);
    } else {
      setHeight(48); // Sets the minimum height
    }
  };
  const onLayoutHandler = (Name: any) => (event: any) => {
    const {x, y, width, height} = event.nativeEvent.layout;
    console.log(`Name: ${Name},Width: ${width}, Height: ${height}`);
  };
  
  
  const leaderboardTogglePress = () => {
    setIsLeaderboardToggled(!isLeaderboardToggled);
  };

  const leaderboardSettingsTogglePress = () => {
    setIsLeaderboardSettingsToggled(!isLeaderboardSettingsToggled);
  };
  const optionsTogglePress = () => {
    setIsOptionsToggled(!isOptionsToggled);
  };
  const countAsPostTogglePress = () => {
    setIsCountAsPostToggled(!isCountAsPostToggled);
  };
  const pointScoringMethod = (type: '1 Point Per Post' | 'Manual Scoring' | 'Integrated App/Device') => {
    setPointScoringType(type);
  
    if (type === 'Integrated App/Device') {
      navigation.navigate('IntegrationsScreen',{
        destinationScreen: 'ChallengeCreationScreen2',
        destinationStack: 'ChallengeCreationScreenStack',
       externalApp : externalApp}); // 👈 Make sure this is your actual route name
    console.log('Navigate to Integration Screen');
    }
  };
  const pointScoringTogglePress = () => {
    setPointScoringToggle(!pointScoringToggle);
  };
  const handleRatingChange = (newRating: any) => {
    setRating(newRating);
    console.log('Selected Difficulty Rating:', newRating);
  };
  const challengeState = useAppSelector(state => state.challenge);
  const {isCreateSuccess} = challengeState;

  useEffect(() => {
    if (route.params?.selectedMedia) {
      setSelectedMedia(route.params.selectedMedia);
      console.log('selected Media deatails', selectedMedia);
    }
  }, [route.params?.selectedMedia]);
  const handleDelete = (uri: any) => {
    setSelectedMedia(prevMedia =>
      prevMedia.filter((media: any) => media.uri !== uri),
    );
  };

  const isAnyItemLoading = () => {
    return Object.values(loadingItems).some(isLoading => isLoading);
  };

  const handleMuteUnmute = async (uri: string) => {
    const selectedItem: any = selectedMedia.find(
      (item: any) => item.uri === uri,
    );

    if (!selectedItem?.muteUrl) {
      setLoadingItems(prev => ({...prev, [uri]: true}));
      const updatedItems: any = await Promise.all(
        selectedMedia.map(async (item: any) => {
          if (item.uri === uri) {
            if (item.muteUrlStore) {
              item.uri = item.muteUrlStore;
              item.muteUrl = item.muteUrlStore;
              item.originalUri = uri;
            } else {
              const muteUrl = `${uri}_mute.mp4`;
              await muteVideo(uri, muteUrl); // Wait for compression
              item.muteUrl = muteUrl;
              item.uri = muteUrl;
              item.muteUrlStore = muteUrl;
              item.originalUri = uri;
            }
          }
          return item;
        }),
      );
      setLoadingItems(prev => ({...prev, [uri]: false}));
      setSelectedMedia([...updatedItems] as any);
    } else {
      const updatedItems: any = selectedMedia.map((item: any) => {
        if (item.uri === uri) {
          item.uri = item.originalUri;
          item.muteUrlStore = item.muteUrl;
          delete item.muteUrl;
        }
        return item;
      });
      setSelectedMedia([...updatedItems] as any);
    }
  };

  const renderMediaItem = ({item, index}: any) => {
    const imageURI = item.uri
      ? Platform.OS === 'android'
        ? `file://${item.uri}`
        : item.uri
      : null;

    return (
      <TouchableOpacity activeOpacity={1} style={styles.mediaItem}>
        {item.type.includes('image') ? (
          <Image source={{uri: imageURI}} style={styles.mediaImage} />
        ) : (
          <TouchableWithoutFeedback onPress={() => handleVideoTap(index)}>
            <Video
              source={{uri: item.uri}}
              paused={playingIndex !== index}
              resizeMode={'cover'}
              style={styles.mediaImage}
              useTextureView={false} 
              controls={false}
              ignoreSilentSwitch="ignore"
            />
          </TouchableWithoutFeedback>
        )}

        {/* Delete icon */}
        <TouchableOpacity
          style={styles.deleteIconContainer}
          onPress={() => handleDelete(item.uri)}>
          <MaterialCommunityIcons name="delete" size={24} color="red" />
        </TouchableOpacity>

        {item.type.includes('video') && (
          <TouchableOpacity
            style={styles.muteIconContainer}
            onPress={() => handleMuteUnmute(item.uri)}>
            {loadingItems[item.uri] ? (
              <ActivityIndicator size="small" color="red" /> // Loader component
            ) : (
              <MaterialCommunityIcons
                name={item.muteUrl ? 'volume-off' : 'volume-high'}
                size={24}
                color="red"
              />
            )}
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  const createChallengeApiCall = async (challengeValue: any) => {
    try {
      await analytics().logEvent('button_click', {
        button_name: 'create_challenge',
        screen: 'ChallengeCreationScreen',
        user_id: userId,
      });

      setLoading(true);
      // Step 1: Call Create Challenge API
      const createResult = await dispatch(createNewChallenge(challengeValue)).unwrap();
      console.log('Challenge created successfully!', createResult);
      if (createResult.code !== 200) {
        Toast.show({
          type: 'error',
          text1: 'Failed to create challenge.',
          text2: 'Something went wrong.',
        });
        setLoading(false);
        return;
      }

      const challengeId = createResult.data.challengeId; // Assuming the response contains challengeId

      // Step 2: Call Add Media API if media is present
      if (selectedMedia.length > 0) {
        const mediaFile = selectedMedia.map((image: any) => ({
          uri: image.uri,
          name: image.fileName,
          type: image.type,
        }));

        const mediaUploadResult = await dispatch(
          addChallengeMedia({ challengeId, mediaFile })
        ).unwrap();
        console.log('Challenge media uploaded successfully!', mediaUploadResult);
        if (mediaUploadResult.code !== 200) {
          Toast.show({
            type: 'error',
            text1: 'Failed to upload media.',
            text2: 'Something went wrong.',
          });
          setLoading(false);
          return;
        }
      }

      // Step 3: Call Update Challenge API with only challengeId
      const updateResult = await dispatch(updateChallenge({ challengeId })).unwrap();
      console.log('Challenge updated successfully!', updateResult);
      if (updateResult.code !== 200) {
        Toast.show({
          type: 'error',
          text1: 'Failed to finalize challenge.',
          text2: 'Something went wrong.',
        });
        setLoading(false);
        return;
      }

      // On success, navigate to Challenges screen
      navigation.reset({
        index: 0,
        routes: [
          { name: 'BottomTabsNavigator', params: { screen: 'Challenges' } },
        ],
      });
      dispatch(resetUpdateSuccess());
      dispatch(resetCreateSuccess());
      dispatch(resetAddMediaSuccess());
      setLoading(false);
    } catch (error) {
      setLoading(false);
      Toast.show({
        type: 'error',
        text1: 'Failed to create challenge.',
        text2: 'Something went wrong.',
      });
      console.error('Failed to create challenge:', error);
    }
  };

  const validateAllFields = () => {
    const isTitleValid = validateTitle();
    const isDescValid = validateDesc();
    const isMediaValid = validateMedia();
    const isValidatePointTitle = validatePointTitle();
    // const isValidatePointRule = validatePointRule();
    return (
      isTitleValid &&
      isDescValid &&
      isMediaValid &&
      isValidatePointTitle
      // isValidatePointRule
    );
  };
  const isFieldEmpty = (field: any) => field?.trim() === '';

  const validatePointRule = () => {
    if (pointScoringType != 'Automated' && isFieldEmpty(pointsDetail)) {
      setPointDescError('*Point Rules is required.');
      //  setIsLeaderboardSettingsToggled(true);
      return false;
    }
    setPointDescError('');
    return true;
  };

  const validatePointTitle = () => {
    if (isLeaderboardToggled && isFieldEmpty(pointsTitle)) {
      setPointTitleError('*Point Title is required.');
      return false;
    }
    setPointTitleError('');
    return true;
  };

  const validateTitle = () => {
    if (isFieldEmpty(title)) {
      setTitleError('*Title is required.');
      return false;
    }
    setTitleError('');
    return true;
  };
  const validateDesc = () => {
    if (isFieldEmpty(description)) {
      setDescError('*Description is required.');
      return false;
    }
    setDescError('');
    return true;
  };
  const validateMedia = () => {
    if (selectedMedia.length == 0) {
      setMediaError('*Media is required.');
      return false;
    }
    setMediaError('');
    return true;
  };

  const onCreateChallenge = () => {
    edate.setUTCHours(time.getUTCHours(), time.getUTCMinutes(), 0, 0);
    if (
      sdate &&
      edate &&
      edate > sdate &&
      isLeaderboardToggled &&
      validateAllFields()
    ) {
      console.log('Posting Challenge with Leaderboard');
      challengeValue = {
        title: title,
        description: description,
        visibility: visibility,
        difficultyRating: rating,
        startDate: sdate,
        endDate: edate,
        isLeaderboardCreated: isLeaderboardToggled,
        pointsTitle: pointsTitle,
        pointsDescription: pointsDetail,
        // isPointsAutomated: pointSource == 'Automated' ? true : false,
        isPointsAscending: pointSort == 'Ascending' ? true : false,
      };
      console.log(challengeValue);
      createChallengeApiCall(challengeValue);
    } else if (sdate && edate && edate > sdate && validateAllFields()) {
      console.log('Posting Challenge without Leaderboard');
      challengeValue = {
        title: title,
        description: description,
        visibility: visibility,
        difficultyRating: rating,
        startDate: sdate,
        endDate: edate,
        isLeaderboardCreated: isLeaderboardToggled,
      };
      console.log(challengeValue);
      createChallengeApiCall(challengeValue);
    } else if (validateAllFields()) {
      Alert.alert(' Please set a date atleast 1 day from today.');
    } else Alert.alert(' Please upload media and fill all necessary details*.');
  };

  useEffect(() => {
    return () => {
      dispatch(resetUpdateSuccess());
      dispatch(resetCreateSuccess());
    };
  }, []);

  useEffect(() => {
    if (isAddMediaSuccess) {
      challengeValue = {
        challengeId: challengeDraft.challengeId,
      };
      console.log(challengeValue);
      dispatch(updateChallenge(challengeValue));
      dispatch(resetAddMediaSuccess());
    } else {
      console.log('2 Media add failed or user working on creating new post');
    }
  }, [isAddMediaSuccess]);

  const onCameraScreen = () => {
    challengeValue = {
      title: title,
      description: description,
      visibility: visibility,
      difficultyRating: rating,
      startDate: sdate,
      endDate: edate,
      isLeaderboardCreated: isLeaderboardToggled,
      pointsTitle: pointsTitle,
      pointsDescription: pointsDetail,
    //   isPointsAutomated: pointSource == 'Automated' ? true : false,
      isPointsAscending: pointSort == 'Ascending' ? true : false,
    };
    console.log(challengeValue);
    navigation.navigate('CameraScreen', {
      destinationScreen: 'ChallengeCreationScreen2',
      destinationStack: 'ChallengeCreationScreenStack',
      selectedMedia: selectedMedia,
    });
  };
  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.tags) {
        setSelectedTags(route.params.tags);
      }

  if (route.params && 'externalApp' in route.params) {
    setExternalApp(route.params.externalApp ?? '');
    console.log('External App from Integration Screen', route.params.externalApp);
  }
    }, [route.params?.tags, route.params?.externalApp])
  );

  return (
    // <View style={styles.container}>
    <View style={acrossAllScreens.ScreenBackground}>

        <View style={[acrossAllScreens.ScreenBorders]}>
          <View
            style={[styles.HeaderStyle]}
            onLayout={onLayoutHandler('HeaderViewTag')}>
            <TouchableOpacity
              style={[
                acrossAllScreens.backImageContainer,
                styles.HeaderbackButton,
              ]}
              disabled={isAnyItemLoading() || loading}
              onPress={() => navigation.goBack()}>
              <Image
                style={acrossAllScreens.backImage}
                source={require('../../assets/images/back.png')}
              />
            </TouchableOpacity>
            <View style={styles.HeaderHorizontalPosition}>
              {/* <Feather name="award" size={20} color="black" /> */}
              <Text style={[acrossAllScreens.ScreenHeaderText]}>
                Create Challenge
              </Text>
            </View>
          </View>
      <KeyboardAwareScrollView contentContainerStyle={styles.scrollViewContent}>
          <View
            style={{marginTop: 4,flexDirection: 'row',alignItems: 'center',justifyContent: 'space-between'}}
            onLayout={onLayoutHandler('Title TextInputViewTag')}>
            <TextInput
              onLayout={onLayoutHandler('Title TextInputTag')}
              style={[
                acrossAllScreens.InputTextLarge,
                {
                  minHeight: 52.7, // Ensures the height is not less than 48
                  textAlignVertical: 'center',
                  flex:1
                },
              ]}
              value={title}
              onChangeText={newtext => setTitle(newtext)}
              placeholder="Challenge Title"
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
              maxLength={50}
              multiline={true}
            />
            <TouchableOpacity
                          onPress={() => {
                            console.log('Napoz AI button pressed for title');
                            setAiGenerateModal(true);
                          }}>
            <Image source={require('../../assets/images/NapozAI.png') } style={[styles.napozAIImage,{marginLeft:2.5}]}/>
            </TouchableOpacity>
          </View>
          {titleError ? (
            <View style={{marginTop: 10}}>
              <Text style={acrossAllScreens.ErrorText}>{titleError}</Text>
            </View>
          ) : null}
          {/* {imageURI ? (
            <Image style={styles.cardImage} source={{uri: imageURI}} />
          ) : //
          null} */}
          {/* Show capture or selected media */}
          {selectedMedia.length > 0 && (
            <View style={styles.mediaContainer}>
              <FlatList
                data={selectedMedia}
                horizontal
                renderItem={renderMediaItem}
                keyExtractor={(item: any) => item.uri}
                showsHorizontalScrollIndicator={false}
              />
            </View>
          )}
          <View
            // style={styles.PartitionStyle}
            onLayout={onLayoutHandler('Desc. TextInputViewTag')}>
            {/* Character count disbale for now  */}
            {/* <Text onLayout={onLayoutHandler('Avail char TextTag')}>
              Available Characters: {maxRemaianDescLength}
            </Text> */}

            <TextInput
              onLayout={onLayoutHandler('Desc. TextInputTag')}
              ref={inputRef}
              style={[
                acrossAllScreens.InputTextRegular,
                {
                  height: Math.max(48, height), // Ensures the height is not less than 48
                  textAlignVertical: 'top',
                },
              ]}
              value={description}
              onChangeText={handleTextChange}
              onContentSizeChange={handleContentSizeChange}
              placeholder="Enter Challenge Description "
              maxLength={400}
              multiline={true}
              onEndEditing={() => inputRef.current?.blur()}
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
            />
            {descError ? (
              <View style={{marginTop: 10,marginBottom: 10}}>
                <Text style={acrossAllScreens.ErrorText}>{descError}</Text>
              </View>
            ) : null}
          </View>
          <TouchableOpacity
            style={styles.napozAIButton}
            onPress={() => {
              console.log('Napoz AI button pressed for description');
              setAiGenerateModal(true);
            }}>
            <Image
              source={require('../../assets/images/NapozAI.png')}
              style={styles.napozAIImage}
            />
            <Text style={styles.tagText}>Napoz AI</Text>
          </TouchableOpacity>

          {/* Friends tagging privacy selection */}
          {/* <View style={[styles.PartitionStyle]}> */}
          {/* <TouchableOpacity
              // onPress={() => navigation.push('TagFriends')}
              onPress={toggleFriendsModal}>
              <View style={[styles.ModalButton]}>
                <Text style={[acrossAllScreens.H2]}>Private Tag Friends</Text>
                <AntDesign name="right" size={20} color="black" />
              </View>
            </TouchableOpacity>
            <Text
              style={[acrossAllScreens.H3, {marginBottom: 6, marginTop: 7}]}>
              {friends}
            </Text>
            <Modal
              isVisible={isFriendsModalVisible}
              onBackdropPress={toggleFriendsModal}
              onBackButtonPress={toggleFriendsModal}
              style={{margin: 0, justifyContent: 'flex-end'}}
              backdropOpacity={0.9}>
              <View>
                <TagFriendsScreen onFriendSelect={updateFriends} />
              </View>
            </Modal> */}
          {/* <View style={[styles.spacerTop, styles.spacerBottom]}>
              <TouchableOpacity onPress={toggleModal}>
                <View style={[styles.ModalButton]}>
                  <Text style={[acrossAllScreens.H2]}>
                    Choose Visbility Settings
                  </Text>
                  <View style={styles.HorizontalStyle}>
                    <Text style={[acrossAllScreens.H3, {marginLeft: 6}]}>
                      {visibility}
                    </Text>
                    <AntDesign name="right" size={20} color="black" />
                  </View>
                </View>
              </TouchableOpacity>
              <Modal
                isVisible={isModalVisible}
                onBackdropPress={toggleModal}
                onBackButtonPress={toggleModal}
                style={{margin: 0, justifyContent: 'flex-end'}}
                backdropOpacity={0.9}>
                <View>
                  <VisibilitySettingsScreen
                    onVisibilitySelect={updateVisibility}
                    closeModal={toggleModal}
                  />
                </View>
              </Modal>
            </View> */}
          {/* </View> */}

          <View style={[styles.PartitionStyle, {borderBottomWidth: 0.2}]}>
            <Text style={[acrossAllScreens.H2]}>Challenge Expiry </Text>
            <View style={[{flexDirection: 'row'}, styles.spacerBottom]}>
              <TouchableOpacity
                style={[styles.HorizontalStyle, {marginTop: 7}]}
                disabled={isAnyItemLoading() || loading}
                onPress={() => setOpenTimePicker(true)}>
                <Feather name="clock" size={20} color="black" />
                <DatePicker
                  modal
                  title={'Challenge End Time :'}
                  open={openTimePicker}
                  date={time}
                  mode="time"
                  onConfirm={selectedTime => {
                    console.log(
                      'selected Time',
                      typeof selectedTime,
                      selectedTime,
                    );
                    setOpenTimePicker(false);
                    setTime(selectedTime);
                  }}
                  onCancel={() => {
                    setOpenTimePicker(false);
                  }}
                />
                <Text style={[acrossAllScreens.H3, , {marginLeft: 6}]}>
                  {moment(time).format('hh:mm A')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate('Calendar', {
                    type: 'end',
                    screen: 'ChallengeCreationScreen2',
                  })
                }
                disabled={isAnyItemLoading() || loading}
                style={[
                  styles.HorizontalStyle,
                  
                  {marginTop: 7, marginLeft: 35},
                ]}>
                <AntDesign
                  name="calendar"
                  size={20}
                  color={isAnyItemLoading() || loading ? 'gray' : 'black'}
                />

                <Text style={[acrossAllScreens.H3, , {marginLeft: 6}]}>
                  {endDate}
                </Text>
              </TouchableOpacity>
            
            </View>
          </View>

            <View style={[styles.PartitionStyle, {borderBottomWidth: 0.2}]}> 
                <TouchableOpacity
                onPress={() => navigation.push('TagScreen', { existingTags: selectedTags })}>
                <View style={[styles.ModalButton]}>
                    <Text style={[acrossAllScreens.H3]}>Tags</Text>
                    <AntDesign name="right" size={20} color="black" />
                </View>
                <Text style={[acrossAllScreens.H2,]}>Associating a tag lets users easily find your challenge in the explore page if it matches hier interests.</Text>
                </TouchableOpacity>
                <View style={[ {marginBottom:11}]}>
                 {selectedTags.length?( <View style={[styles.tagContainer]}>
                    {selectedTags.map((tag) => (
                    <View key={tag} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                    </View>
                     ))}
                     </View>):null}
                </View> 
                                
            </View>

          <View
            style={[
              styles.HorizontalStyle,
              // styles.spacerBottom,
              {justifyContent: 'space-between'},
            ]}>
            <Text style={[acrossAllScreens.H2]}>Create Leaderboard</Text>
            <TouchableOpacity
              onPress={leaderboardTogglePress}
              disabled={isAnyItemLoading() || loading}>
              <FonteAwesome
                name={isLeaderboardToggled ? 'toggle-on' : 'toggle-off'}
                size={30}
                color="#c3e7f5"
              />
            </TouchableOpacity>
          </View>
          {isLeaderboardToggled ? (
            <View>
                    <View style={[styles.PartitionStyle, {borderBottomWidth: 0.2}]}>
                      <TouchableOpacity
                              onPress={pointScoringTogglePress} >
                              <View style={[styles.ModalButton]}>
                                  <Text style={[acrossAllScreens.H3]}>How Points Are Earned</Text>
                                  <AntDesign name={ pointScoringToggle ? "up" :"down"} 
                                  size={20} color="black" />
                              </View>
                              </TouchableOpacity> 
                    {pointScoringToggle ?(<View >
                    <View >
                           
                            
                            <TouchableOpacity
                            onPress={() => pointScoringMethod('1 Point Per Post')}
                            disabled={isAnyItemLoading() || loading}>
                            <View style={
                                pointScoringType === '1 Point Per Post'
                                    ? styles.pointsContainerChange
                                    : styles.pointsContainer
                                }>
                                <Image style={styles.iconStyle}
                                    source={require('../../assets/images/AutoInc.png')}/>
                                <View style={{ flex: 1 }}>
                                    <Text style={[acrossAllScreens.H2]}>1 Point per Post </Text>
                                    <Text style={[acrossAllScreens.H2]}>Napoz will automatically increment the user's points
                                        by 1 for each post they make in the challenge.
                                    </Text>
                                </View>
                            </View>
                            </TouchableOpacity>
                            <TouchableOpacity
                            onPress={() => pointScoringMethod('Manual Scoring')}
                            disabled={isAnyItemLoading() || loading}>
                            <View style={
                                pointScoringType === 'Manual Scoring'
                                    ? styles.pointsContainerChange
                                    : styles.pointsContainer
                                }>
                            <Image style={styles.iconStyle}
                                    source={require('../../assets/images/ManInc.png')}/>
                                <View style={{ flex: 1 }}>
                                    <Text style={[acrossAllScreens.H2]}>Manual Scoring </Text>
                                    <Text style={[acrossAllScreens.H2]}>Every individual who participates in the challenge
                                        will have to enter their own points while making a post per the challenge rules.
                                    </Text>
                                </View>
                            </View>
                            </TouchableOpacity>
                            <TouchableOpacity
                            onPress={() => pointScoringMethod('Integrated App/Device')}
                            disabled={isAnyItemLoading() || loading}>
                            <View style={
                                pointScoringType === 'Integrated App/Device'
                                    ? styles.pointsContainerChange
                                    : styles.pointsContainer
                                }>
                                <Image style={styles.iconStyle}
                                        source={require('../../assets/images/IntApp.png')}/>
                                <View style={{ flex: 1 }}>
                                    <View style={{ flex: 1 ,flexDirection: 'row', alignItems: 'center'}}>
                                      <Text style={[acrossAllScreens.H2]}>Integrated App / Device </Text>
                                      {externalApp?<Text> :</Text>:null}
                                      {  (pointScoringType === 'Integrated App/Device') && <Text style={[acrossAllScreens.H3, {marginLeft: 5}]}> {externalApp} </Text>}
                                    </View>
                                    <Text style={[acrossAllScreens.H2]}>Napoz reads data directly from an integrated app or Device
                                        to determine the user's points.
                                    </Text>
                                </View>
                            </View>
                            </TouchableOpacity>
                    </View>
                    {(pointScoringType === 'Integrated App/Device')&&externalApp?<View style={[{marginTop:12}]}>
                    <TouchableOpacity
                            onPress={toggleImportDataModal}
                            disabled={isAnyItemLoading() || loading}>
                            <View style={[styles.ModalButton]}>
                                <Text style={[acrossAllScreens.H2]}>
                                Import Data Type
                                </Text>
                                <View style={styles.HorizontalStyle}>
                                <Text style={[acrossAllScreens.H3, {marginLeft: 6}]}>
                                    {importDataType}
                                </Text>
                                <AntDesign name="right" size={20} color="black" />
                                </View>
                            </View>
                            </TouchableOpacity>
                            <Modal
                            isVisible={isImportDataModalVisible}
                            onBackdropPress={toggleImportDataModal}
                            onBackButtonPress={toggleImportDataModal}
                            style={{margin: 0, justifyContent: 'flex-end'}}
                            backdropOpacity={0.9}>
                            <View>
                                <ImportPointDataScreen
                                onOptionSelect={updateImportData}
                                closeModal={toggleImportDataModal}
                                ExternalApp={externalApp}
                                />
                            </View>
                            </Modal>
                    </View>:null}
                        {/* <Text style={[acrossAllScreens.H2]}>Leaderboard Points Column Title </Text> */}
                        
                  </View>) : (
                  <View>
                    <Text style={[acrossAllScreens.H2]}>
                      {pointScoringType}{' '}
                      {pointScoringType == 'Integrated App/Device' &&
                      externalApp ? (
                        <Text style={acrossAllScreens.H3}>
                          {' '}
                          : {externalApp}
                        </Text>
                      ) : null}
                    </Text>
                  </View>
                )}
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}>
                    <TextInput
                      style={[
                        acrossAllScreens.H3,
                        {minWidth: 60, marginBottom: 5},
                      ]}
                      onLayout={onLayoutHandler('Points Title TextInputTag')}
                      value={pointsTitle}
                      onChangeText={newtext => setPointsTitle(newtext)}
                      placeholder="Leaderboard Points Column Title: Ex: Score etc.  "
                      placeholderTextColor="grey"
                      underlineColorAndroid="transparent"
                      maxLength={18}
                      multiline={false}
                    />
                    <TouchableOpacity
                      onPress={() => {
                        console.log('Napoz AI button pressed for points title');
                        setAiGenerateModal(true);
                      }}>
                      <Image
                        source={require('../../assets/images/NapozAI.png')}
                        style={[styles.napozAIImage, {marginLeft: 2.5}]}
                      />
                    </TouchableOpacity>
                  </View>
                  {pointTitleError ? (
                    <View style={{marginBottom: 5}}>
                      <Text
                        style={[
                          acrossAllScreens.ErrorText,
                          {position: 'relative'},
                        ]}>
                        {pointTitleError}
                      </Text>
                    </View>
                  ) : null}
                </View>
              </View>
              <View
                style={[
                  styles.PartitionStyle,
                  {borderBottomWidth: 0.2, paddingBottom: 16},
                ]}>
                <TouchableOpacity onPress={leaderboardSettingsTogglePress}>
                  <View style={[styles.ModalButton]}>
                    <Text style={[acrossAllScreens.H3]}>
                      Advanced Settings
                    </Text>
                    <AntDesign
                      name={isLeaderboardSettingsToggled ? 'up' : 'down'}
                      size={20}
                      color="black"
                    />
                  </View>
                </TouchableOpacity>
                {isLeaderboardSettingsToggled ? (
                  <View>
                    <TextInput
                        style={[acrossAllScreens.H3,{marginBottom:5}]}
                        onLayout={onLayoutHandler('Points Desc. TextInputTag')}
                        value={pointsDetail}
                        onChangeText={newtext => setPointsDetail(newtext)}
                        placeholder="Enter Point Rules"
                        placeholderTextColor="grey"
                        underlineColorAndroid="transparent"
                        maxLength={50}
                        multiline={true}
                    />
                    <Text style={[acrossAllScreens.H2]}>( Describe to your participants how they can earn points in this challenge. )</Text>
                                    
                    {pointDescError ? (
                        <View style={{marginTop: 5}}>
                        <Text
                            style={[
                            acrossAllScreens.ErrorText,
                            {position: 'relative' },
                            ]}>
                            {pointDescError}
                        </Text>
                        </View>
                    ) : null}
                    
                         <View style={[ pointScoringType !== '1 Point Per Post' && styles.spacerBottom]}>
                            <View
                                style={[
                                styles.HorizontalStyle,
                                // styles.spacerBottom,
                                {justifyContent: 'space-between'},
                                ]}>
                                <Text style={[acrossAllScreens.H2]}>Descending points sort order</Text>
                                <TouchableOpacity
                                onPress={pointSortTogglePress}
                                disabled={isAnyItemLoading() || loading}>
                                <FonteAwesome
                                    name={isPointSortToggled ? 'toggle-on' : 'toggle-off'}
                                    size={30}
                                    color="#c3e7f5"
                                />
                                </TouchableOpacity>
                            </View>
                            <Text style={[acrossAllScreens.H2]}>Napoz will rank users in {pointSort} order of points obtained in this challenge</Text>
                                        
                            
                    </View>
                    {pointScoringType !== '1 Point Per Post' && (
                    <View >
                    <TouchableOpacity
                            disabled={isAnyItemLoading() || loading}
                            onPress={toggleLeaderboardModal}>
                            <View style={[styles.ModalButton]}>
                                <Text style={[acrossAllScreens.H2]}>
                                How Scores Are Tracked
                                </Text>
                                <View style={styles.HorizontalStyle}>
                                <Text style={[acrossAllScreens.H3, {marginLeft: 6}]}>
                                    {leaderboardType}
                                </Text>
                                <AntDesign name="right" size={20} color="black" />
                                </View>
                            </View>
                            </TouchableOpacity>
                            <Modal
                            isVisible={isLeaderboardModalVisible}
                            onBackdropPress={toggleLeaderboardModal}
                            onBackButtonPress={toggleLeaderboardModal}
                            style={{margin: 0, justifyContent: 'flex-end'}}
                            backdropOpacity={0.9}>
                            <View>
                                <LeaderboardTypeScreen
                                onOptionSelect={updateLeaderboardType}
                                closeModal={toggleLeaderboardModal}
                                pointScoringType={pointScoringType}

                                />
                            </View>
                            </Modal>
                    </View>
                    )}
                    </View>
                    ) :null}
                    </View>
                    <View >
                        <TouchableOpacity
                            onPress={optionsTogglePress}>
                            <View style={[styles.ModalButton]}>
                                <Text style={[acrossAllScreens.H3]}>Post Options</Text>
                                <AntDesign name={ isOptionsToggled ? "up" :"down"}
                                size={20} color="black" />
                            </View>
                        </TouchableOpacity>
                        {isOptionsToggled ?(
                        <View>
                        <View
                            style={[
                            styles.HorizontalStyle,
                            // styles.spacerBottom,
                            {justifyContent: 'space-between'},
                            ]}>
                            <Text style={[acrossAllScreens.H2]}>Count challenge media as first post for leaderboard</Text>
                            <TouchableOpacity
                            onPress={countAsPostTogglePress}
                            disabled={isAnyItemLoading() || loading}>
                            <FonteAwesome
                                name={isCountAsPostToggled ? 'toggle-on' : 'toggle-off'}
                                size={30}
                                color="#c3e7f5"
                            />
                            </TouchableOpacity>
                            
                        </View>
                        {isCountAsPostToggled && (
                            <View>
                            <Text style={[acrossAllScreens.H2]}>
                            Adds the current media entry as the first post in the challenge
                            </Text> 
                            
                            <TextInput
                        style={[
                            acrossAllScreens.H3,
                            {minWidth: 60, paddingBottom:1}
                        ]} 
                        onLayout={onLayoutHandler('Points  TextInputTag')}
                        value={points}
                        onChangeText={newtext => setPoints(newtext)}
                        placeholder={"Enter "+pointsTitle+ " value"}
                        placeholderTextColor="grey"
                        underlineColorAndroid="transparent"
                        maxLength={15}
                        multiline={false}
                        />
                        
                        </View>) } 
                        </View>   
                     ) :null}
                                 
                       
                       
                    </View>
                </View>
          ) : null}

          <View
            style={[
              styles.HorizontalStyle,
              {justifyContent: 'space-between'},
              styles.spacerTop,
            ]}>
            <Text style={[acrossAllScreens.H2]}>Difficulty rating</Text>

            <DifficultyRatingComponent
              onRatingChange={handleRatingChange}
              rating={rating}
            />
          </View>

          <View style={[styles.CameraRowStyle]}>
            <TouchableOpacity
              disabled={isAnyItemLoading() || loading}
              onPress={onCameraScreen}>
              <FonteAwesome
                name="camera"
                size={75}
                color={isAnyItemLoading() || loading ? 'gray' : 'black'}
              />
            </TouchableOpacity>

            <TouchableHighlight
              style={[
                SignUpStyle.signupButton,
                (isAnyItemLoading() || loading) && {backgroundColor: 'gray'},
              ]}
              disabled={isAnyItemLoading() || loading}
              onPress={onCreateChallenge}>
              {isAnyItemLoading() ? (
                <ActivityIndicator />
              ) : (
                <Text style={SignUpStyle.signUpText}>Create Challenge</Text>
              )}
            </TouchableHighlight>
          </View>
        
        <AiGenerationModal
          visible={isAiGenerateOpen}
          onClose={() => setAiGenerateModal(false)}
          onConfirm={onAiGeneratedConfirm}
        />

      </KeyboardAwareScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  muteIconContainer: {
    position: 'absolute',
    top: 40,
    right: 5,
    padding: 5,
    borderRadius: 20,
  },
  // container: {
  //   flex: 1,
  //   backgroundColor: 'lightblue', // Your desired background color
  // },

  scrollViewContent: {
    flexGrow: 1, // Ensures the content grows to fill the available space
  },
  ColorStyle: {
    color: 'black',
  },
  PartitionStyle: {
    // borderBottomWidth: 0.2,
    paddingBottom: 0,
    marginBottom: 16,
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  ModalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  TitleStyle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  DescriptionStyle: {
    textAlignVertical: 'top',
  },
  HorizontalStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  HashtagStyle: {
    fontSize: 18,
    fontWeight: 'bold',
    borderWidth: 1,
    alignSelf: 'flex-start',
    padding: 3,
    marginTop: 5,
    marginBottom: 15,
  },
  SubTitleStyle: {
    fontSize: 18,
  },
  ViewSubTitleStyle: {
    marginTop: 0,
  },
  CameraRowStyle: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  PreviewButtonStyle: {
    fontSize: 18,
    fontWeight: 'bold',
    borderWidth: 1,
    padding: 5,
    borderRadius: 10,
  },
  cardImage: {
    width: '100%',
    height: 300,
    //
  },
  spacerTop: {
    marginTop: 10,
  },
  spacerBottom: {
    marginBottom: 16,
  },
  mediaContainer: {
    height: 150,
    marginBottom: 5,
  },
  mediaItem: {
    marginRight: 10,
  },
  mediaImage: {
    width: 150,
    height: 150,
    borderRadius: 8,
    backgroundColor: '#ccc',
  },
  deleteIconContainer: {
    position: 'absolute',
    top: 5,
    right: 5,
    padding: 5,
    borderRadius: 20,
  }, tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  tag: {
    backgroundColor: '#C3E7F5',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
     margin: 5,
  },
  tagText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  iconStyle:{
    width: 33,
    height: 33,
    resizeMode: 'contain',
    marginRight: 10,
  },
  pointsContainer: {
    flexDirection: 'row',
    // flexWrap: 'wrap',
    marginTop: 10,
    alignItems: 'center',
    
  },
  pointsContainerChange: {
    flexDirection: 'row',
    // flexWrap: 'wrap',
    marginTop: 10,
    alignItems: 'center',
    backgroundColor:'#C3E7F5',
    paddingTop: 5,
    paddingBottom:5,
    borderRadius: 10,
    
  },
  napozAIButton:{backgroundColor: '#C3E7F5',
    
    height: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
    width: 115,
    borderRadius: 15,},
  napozAIImage:{
    width: 18,
    height: 18,
    resizeMode: 'contain',marginHorizontal:5},
});

export default ChallengeCreationScreen2;