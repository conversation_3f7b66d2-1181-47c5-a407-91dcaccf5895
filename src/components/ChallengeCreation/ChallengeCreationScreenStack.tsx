import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {Button} from 'react-native';
import {TabRouter} from '@react-navigation/routers';
import {ChallengeCreationScreen} from './ChallengeCreationScreen';
import {ChallengeCreationScreen2} from './ChallengeCreationScreen2';
import HashtagScreen from '../Common/HashtagScreen';
import CalendarScreen from '../Common/CalendarScreen';
import TagFriendsScreen from '../TagFriends/TagFriendsScreen';
import VisibilitySettingsScreen from '../Common/Visibilitysettings';
import PointsSourceScreen from '../Common/PointsSource';
import TagScreen from '../Interest/TagScreen';
import IntegrationsScreen from '../Common/IntegrationsScreen';

const challengeCreationStack = createStackNavigator();

export function ChallengeCreationScreenStack() {
  return (
    <challengeCreationStack.Navigator>
      <challengeCreationStack.Screen
        name="ChallengeCreationScreen"
        component={ChallengeCreationScreen}
        options={{
          title: 'Create Challenge',
          headerShown: false,
        }}
      />
      <challengeCreationStack.Screen
        name="Hash"
        component={HashtagScreen}
        options={{
          title: 'Hashtag',
          headerTitleAlign: 'center',
          headerLeft: () => null,
          // headerRight: () => (
          //   <Button title="Done" onPress={() => console.log('done')} />
          // ),
        }}
      />
      <challengeCreationStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{headerShown: false}}
      />
      <challengeCreationStack.Screen
        name="TagFriends"
        component={TagFriendsScreen}
        // options={{headerShown: false}}
      />
      <challengeCreationStack.Screen
        name="VisibilityScreen"
        component={VisibilitySettingsScreen}
        // options={{headerShown: false}}
      />
      <challengeCreationStack.Screen
        name="PointsSourceScreen"
        component={PointsSourceScreen}
        // options={{headerShown: false}}
      />
        <challengeCreationStack.Screen
        name="TagScreen"
        component={TagScreen}
        options={{headerShown: false}}
      />
      <challengeCreationStack.Screen
        name="IntegrationsScreen"
        component={IntegrationsScreen}
        options={{headerShown: false}}
      />
    </challengeCreationStack.Navigator>
  );
}

export default ChallengeCreationScreenStack;
