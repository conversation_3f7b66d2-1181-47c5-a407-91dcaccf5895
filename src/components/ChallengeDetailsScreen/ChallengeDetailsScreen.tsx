import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Alert,
  ActivityIndicator,
  AppState,
} from 'react-native';
import {useDispatch} from 'react-redux';
import Video from 'react-native-video';
import {resetDeleteSuccess} from '../../redux/Challenge/ChallengeSlice';

import {SignUpStyle} from '../../styles/SignUp';
import {ChallengeTrendSetters} from '../ChallengeDetailsScreen/ChallengeTrendSetters';
import {useAppSelector} from '../../redux/Store';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import FastImage from 'react-native-fast-image';
import moment from 'moment-timezone';
const {width} = Dimensions.get('window');
import {
  deleteChallenge,
  exitChallenge,
  joinChallenge,
  getChallenge,
  getPostLinkedToChallenge,
  getParticipantsOfChallenge,
} from '../../redux/Challenge/ChallengeAction';
import Toast from 'react-native-toast-message';
import Popover from 'react-native-popover-view';
import {Post} from './ChallengeDetailsFromHomeScreen';
import {Dots} from '../HomeScreen/HomeFeedScreen';
import {
  deletePublicFeedChallenge,
  likeUnlikeChallengeFromPost,
  removeLikeUnlikeChallengeFromPost,
} from '../../redux/Post/PostAction';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Icon from 'react-native-vector-icons/Ionicons';
import {muteUnMuteVideo} from '../../redux/Post/PostSlice';
import convertToProxyURL from 'react-native-video-cache';
import acrossAllScreens from '../../styles/acrossAllScreens';
import DebounceTouchable from '../HomeScreen/DebounceTouchable';
import {User} from './ChallengeParticipant';

let bottomLoad = false;
export function ChallengeDetailsScreen({route, navigation}: any) {
  const dispatch = useDispatch();
  const challengeState = useAppSelector(state => state.challenge);
  const {status, challenges, isDeleteSuccess} = challengeState as any;
  const [dataLoading, setDataLoading] = useState(false);

  const authState = useAppSelector(state => state.auth);
  const {userId, token} = authState;

  useEffect(() => {
    if (route.params?.challengeId) {
      setDataLoading(true);
      dispatch(getChallenge(route.params?.challengeId)).finally(() => {
        setDataLoading(false);
      });
    }
  }, [route.params?.challengeId]);

  const currentDate = new Date();

  const currentDateTimeUTC = new Date(currentDate.toISOString());

  const timeEnd = new Date(challenges.endDate);
  const timeStart = new Date(challenges?.startDate);
  const isActiveChallenge = timeEnd > currentDate;

  const timeDisplayJoined = moment.utc(challenges?.createdAt).fromNow();
  const timeJoinedJoined = moment.utc(challenges?.userJoinDate).fromNow();
  const timeDisplayEnd = moment.utc(challenges?.endDate).fromNow();
  const screenWidth = Dimensions.get('window').width;
  const [activeIndex, setActiveIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const popoverRef = useRef<Popover>(null);
  const isOwnPost = userId == challenges?.user?.userId;
  const [postList, setPostList] = useState<Post[]>([]);
  const [hasNext, setHasNext] = useState(false);
  const [nextPageToken, setNextPageToken] = useState('');
  const [loadingBototm, setLoadingBottom] = useState(false);
  const mute = useAppSelector(state => state.post.mute);
  const [isMuted, setIsMuted] = useState(mute);
  const [appState, setAppState] = useState('active');
  const [isLiked, setIsLiked] = useState(challenges.isLiked);
  const [likes, setLikes] = useState(challenges.likes || 0);
  const [isChallengeDetailsToggled, setIsChallengeDetailsToggled] =
    useState(false);
  const [isLeaderboardDetailsToggled, setIsLeaderboardDetailsToggled] =
    useState(false);
  const [userList, setUserList] = useState<User[]>([]);

  useEffect(() => {
    setIsLiked(challenges.isLiked);
    setLikes(challenges.likes);
  }, [challenges.isLiked, challenges.likes]);

  useEffect(() => {
    setIsMuted(mute);
  }, [mute]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const getParticipantsOfChallengeA = async (challengeId: string) => {
    setLoading(true);
    const result = await dispatch(
      getParticipantsOfChallenge(challengeId),
    ).unwrap();
    setLoading(false);
    if (result.data) {
      setUserList(result.data);
    }
  };

  useEffect(() => {
    if (route.params?.challengeId) {
      getParticipantsOfChallengeA(route.params?.challengeId);
    }
  }, [route.params?.challengeId]);

  const toggleMute = () => {
    dispatch(muteUnMuteVideo(!isMuted));
  };

  const challengeDetailsTogglePress = () => {
    setIsChallengeDetailsToggled(!isChallengeDetailsToggled);
  };

  const leaderboardDetailsTogglePress = () => {
    setIsLeaderboardDetailsToggled(!isLeaderboardDetailsToggled);
  };

  const getPointType = () => {
    if (challenges?.config?.pointsScoringType == 'POINT_PER_POST') {
      console.log('1 Point Per Post', challenges?.config?.pointScoringType);
      return '1 Point Per Post';
    }
    if (challenges?.config?.pointsScoringType == 'MANUAL_ENTRY') {
      console.log('Manual Scoring', challenges?.config?.pointScoringType);
      return 'Manual Scoring';
    }
    if (challenges?.config?.pointsScoringType == 'EXTERNAL_APP_DEVICE') {
      return 'Integrated App/Device';
    }
    return challenges?.config?.pointScoringType;
  };

  const getLeaderboardType = () => {
    if (challenges?.config?.leaderboardType == 'ACCUMULATION') {
      return '(Your score will accumulate on leaderboard for each post)';
    }
    if (challenges?.config?.leaderboardType == 'PERSONAL_RECORD') {
      return '(Your Personal Best Score will be recorded on leaderboard)';
    }
    if (challenges?.config?.leaderboardType == 'THRESHOLD') {
      return '(You will receive 1Pt for meeting challenge Threshold and score will accumulate on leaderboard)';
    }
    return challenges?.config?.leaderboardType;
  };

  const getParticipantsOfChallengeMethod = async (challengeId: string) => {
    setNextPageToken('');
    const result = await dispatch(
      getPostLinkedToChallenge({challengeId: challengeId}),
    ).unwrap();
    if (result.data) {
      setPostList(result.data);
      setHasNext(result.data != 0);
      setNextPageToken(result.nextPageToken);
    }
  };

  useEffect(() => {
    if (route.params?.challengeId) {
      getParticipantsOfChallengeMethod(route.params?.challengeId);
    }
  }, [route.params?.challengeId]);

  useEffect(() => {
    if (isDeleteSuccess) {
      console.log('Challenge Deleted and navigating to Challenge Home screen');

      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTabsNavigator', params: {screen: 'Challenges'}}],
      });
      dispatch(resetDeleteSuccess());
    } else {
      console.log('Delete challenge failed', isDeleteSuccess);
    }
  }, [isDeleteSuccess]);

  const confirmEndChallenge = () => {
    const title = 'Delete Challenge';
    const message = 'Are you sure you want to Delete the challenge?';

    Alert.alert(title, message, [
      {
        text: 'Cancel',
        onPress: () => console.log(`${title} canceled`),
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: () => {
          onDeleteChallenge();
        },
      },
    ]);
  };

  const confirmExitChallenge = () => {
    const title = 'Exi Challenge';
    const message = 'Are you sure you want to Exit the challenge?';

    Alert.alert(title, message, [
      {
        text: 'Cancel',
        onPress: () => console.log(`${title} canceled`),
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: () => {
          onExitChallenge();
        },
      },
    ]);
  };

  const onExitChallenge = async () => {
    setLoading(true);
    const result = await dispatch(
      exitChallenge({challengeId: route.params?.challengeId, userId: userId}),
    );
    if (exitChallenge.fulfilled.match(result)) {
      if (result.payload.code == 200) {
        Toast.show({
          type: 'success',
          text1: 'Exit success',
          text2: 'Challenge exited successfully!',
        });
        navigation.goBack();
      } else {
        Alert.alert('Failed to exit challenge.');
      }
    } else if (exitChallenge.rejected.match(result)) {
      Alert.alert('Failed to exit challenge.');
    }
    setLoading(false);
  };

  const onDeleteChallenge = async () => {
    setLoading(true);
    const result = await dispatch(deleteChallenge(route.params?.challengeId));
    if (deleteChallenge.fulfilled.match(result)) {
      if (result.payload.code == 200) {
        Toast.show({
          type: 'success',
          text1: 'Delete success',
          text2: 'Challenge deleted successfully!',
        });
        navigation.goBack();
      } else {
        Alert.alert('Failed to delete challenge.');
      }
    } else if (deleteChallenge.rejected.match(result)) {
      Alert.alert('Failed to delete challenge.');
    }
    setLoading(false);
  };

  const onJoinChallenge = async () => {
    setLoading(true);
    const result = await dispatch(
      joinChallenge({challengeId: route.params?.challengeId, userId: userId}),
    );
    if (joinChallenge.fulfilled.match(result)) {
      if (result.payload.challengeId) {
        Toast.show({
          type: 'success',
          text1: 'Join success',
          text2: 'Challenge joined successfully!',
        });
        dispatch(getChallenge(route.params?.challengeId));
      } else {
        Alert.alert('Failed to join challenge.');
      }
    } else if (joinChallenge.rejected.match(result)) {
      Alert.alert('Failed to join challenge.');
    }
    setLoading(false);
  };

  const onBackPress = () => {
    navigation.goBack();
  };
  const handleScroll = (event: any) => {
    const position = event.nativeEvent.contentOffset.x;
    const activeIndex = Math.round(position / (screenWidth - 50));
    setActiveIndex(activeIndex);
  };

  const onEditClick = () => {
    popoverRef.current?.requestClose();
    navigation.navigate('ChallengeUpdate', {
      title: challenges?.title,
      challengeId: route.params?.challengeId,
      description: challenges?.description,
      startDate: challenges?.startDate,
      endDate: challenges?.endDate,
      isLeaderboardCreated: challenges?.isLeaderboardCreated,
      pointsTitle: challenges?.pointsTitle,
      pointsDescription: challenges?.pointsDescription,
      isPointsAutomated: challenges?.isPointsAutomated,
      isPointsAscending: challenges?.isPointsAscending,
      difficultyRating: challenges?.difficultyRating,
      visibility: challenges?.visibility,
      mediaRefs: challenges?.mediaRefs,
      loggedInUser: userId,
      tags: challenges?.tags,
    });
  };

  const onDeleteClick = () => {
    popoverRef.current?.requestClose();
    Alert.alert(
      'Delete Challenge',
      'Are you sure you want to delete this challenge?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            setLoading(true);
            const result = await dispatch(
              deleteChallenge(route.params?.challengeId),
            );
            deletePublicFeedChallenge(route.params?.challengeId);
            if (result.payload.code == 200) {
            } else {
              Toast.show({
                type: 'error',
                text1: 'Failed to delete challenge.',
                text2: 'Something went wrong.',
              });
            }
            setLoading(false);
          },
          style: 'destructive',
        },
      ],
    );
  };

  const onReportClick = () => {
    popoverRef.current?.requestClose();
  };

  const onEndTrenSetterReached = useCallback(async () => {
    if (postList.length > 0 && !loading && !bottomLoad && hasNext) {
      bottomLoad = true;
      setLoadingBottom(true);
      const result = await dispatch(
        getPostLinkedToChallenge({
          challengeId: route.params?.challengeId,
          nextPageToken: nextPageToken,
        }),
      ).unwrap();
      if (result.data) {
        setPostList([...postList, ...result.data]);
        setHasNext(result.data != 0);
        setNextPageToken(result.nextPageToken);
      }
      setLoadingBottom(false);
      bottomLoad = false;
    }
  }, [dispatch, postList, nextPageToken, loading, hasNext]);

  const onLeaderBoardPress = () => {
    navigation.navigate('LeaderboardDetailsScreen', {
      name: challenges?.pointsTitle,
      creator: challenges?.user.userId,
      challengeId: challenges?.challengeId,
    });
  };

  if (dataLoading) {
    return (
      <View style={styles.loaderContain}>
        <ActivityIndicator size={'large'} />
      </View>
    );
  }

  const onLikeClick = () => {
    if (isLiked) {
      dispatch(
        removeLikeUnlikeChallengeFromPost({
          actionType: 'LIKES',
          interaction: 'POSITIVE',
          contentId: challenges?.challengeId,
        }),
      );
      setIsLiked(false);
      setLikes(likes - 1);
    } else {
      dispatch(
        likeUnlikeChallengeFromPost({
          actionType: 'LIKES',
          interaction: 'POSITIVE',
          contentId: challenges?.challengeId,
        }),
      );
      setIsLiked(true);
      setLikes(likes + 1);
    }
  };

  const onLikeCount = () => {
    navigation.navigate('ChallengeLikeUser', {
      challengeId: challenges?.challengeId,
    });
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <View style={acrossAllScreens.ScreenBorders}>
        <View style={styles.headerView}>
          <TouchableOpacity
            style={acrossAllScreens.backImageContainer}
            onPress={onBackPress}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Challenge</Text>
          <Popover
            ref={popoverRef}
            from={
              <TouchableOpacity
                style={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                }}>
                <Image
                  source={require('../../assets/images/more.png')}
                  resizeMode="contain"
                  style={styles.moreIcon}
                />
              </TouchableOpacity>
            }
            popoverStyle={styles.popoverStyle}
            backgroundStyle={{opacity: 0}}>
            {isOwnPost ? (
              <>
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={onEditClick}>
                  <Text>Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={onDeleteClick}>
                  <Text>Delete</Text>
                </TouchableOpacity>
              </>
            ) : (
              <TouchableOpacity
                style={styles.optionItem}
                onPress={onReportClick}>
                <Text>Report</Text>
              </TouchableOpacity>
            )}
          </Popover>
        </View>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text style={styles.headerStyle}>{challenges.title}</Text>
          <View style={[styles.postcard]}>
            <FlatList
              data={challenges.mediaRefs || []}
              keyExtractor={item => item.id}
              horizontal={true}
              pagingEnabled={true}
              onScroll={handleScroll}
              snapToInterval={width - 20}
              decelerationRate={0.9}
              showsHorizontalScrollIndicator={false}
              renderItem={({item, index}) => (
                <View style={styles.imageContainer}>
                  {isVideoLink(item) ? (
                    <View style={styles.videoWrapper}>
                      <Video
                        source={{
                          uri: convertToProxyURL(item),
                          headers: {
                            Authorization: `Bearer ${token}`,
                          },
                        }}
                        repeat
                        paused={activeIndex !== index}
                        useTextureView={false}
                        muted={isMuted || appState != 'active'}
                        ignoreSilentSwitch="ignore"
                        resizeMode={'contain'}
                        style={styles.imageContainer}
                        bufferConfig={{
                          minBufferMs: 2500,
                          maxBufferMs: 50000,
                          bufferForPlaybackMs: 2500,
                          bufferForPlaybackAfterRebufferMs: 2500,
                        }}
                      />
                      <TouchableOpacity
                        onPress={toggleMute}
                        style={styles.muteButton}>
                        <Icon
                          name={isMuted ? 'volume-mute' : 'volume-high'} // Use icons for mute/unmute
                          size={20}
                          color="white"
                        />
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <FastImage
                      source={{
                        uri: item,
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }}
                      resizeMode={'contain'}
                      style={styles.imageContainer}
                    />
                  )}
                </View>
              )}
            />
          </View>

          {challenges.mediaRefs && challenges.mediaRefs.length > 1 && (
            <Dots
              length={challenges.mediaRefs.length}
              activeIndex={activeIndex}
            />
          )}

          <View style={[styles.userDetailsView, {alignItems: 'center'}]}>
            <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
              {/* <Text style={styles.userDetailsText}>By</Text> */}
              <FastImage
                source={
                  challenges?.user?.imageReference &&
                  challenges?.user?.imageReference !== 'imageReference'
                    ? {
                        uri: challenges?.user?.imageReference,
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }
                    : require('../../static/Images/user.png')
                }
                style={styles.userImg}
              />
              <TouchableOpacity
                onPress={() => {
                  if (challenges?.user?.userId === userId) {
                    navigation.navigate('Profile');
                  } else {
                    navigation.navigate('OtherUserProfileScreen', {
                      userId: challenges?.user?.userId,
                    });
                  }
                }}>
                <Text
                  style={[
                    styles.userDetailsText,
                    styles.userDetailMarginleft,
                    styles.userDetail,
                  ]}>
                  {`${challenges?.user?.firstName} ${challenges?.user?.lastName}`}
                </Text>
              </TouchableOpacity>
            </View>

            <DebounceTouchable
              delay={5000}
              style={styles.likeButton}
              onPress={onLikeClick}>
              <Image
                style={styles.likeImage}
                source={
                  isLiked
                    ? require('../../assets/images/like.png')
                    : require('../../assets/images/unlike.png')
                }
              />
              {isOwnPost && (
                <TouchableOpacity onPress={onLikeCount}>
                  <Text style={styles.likeCount}>{likes}</Text>
                </TouchableOpacity>
              )}
            </DebounceTouchable>
          </View>
          <View
            style={[styles.userDetailsView, {marginBottom: 0, marginTop: 0}]}>
            <Text style={styles.userDetailsText}>
              {challenges?.description}
            </Text>
          </View>

          {/* userId === challenges?.user?.userId && (
          <View style={[styles.userDetailsView, {marginTop: 16}]}>
            <Text style={styles.userDetailsText}>
              You created the challenge
              <Text style={{fontWeight: 'bold'}}>
                {' ' + timeDisplayJoined}
              </Text>
            </Text>
          </View>
        )}
        {userId != challenges?.user?.userId && challenges.isUserJoined && (
          <View style={[styles.userDetailsView, {marginTop: 16}]}>
            <Text style={styles.userDetailsText}>
              You joined the challenge
              <Text style={{fontWeight: 'bold'}}>{' ' + timeJoinedJoined}</Text>
            </Text>
          </View>
        )}

        {currentDateTimeUTC.getTime() - timeEnd.getTime() > 0 ? (
          <View style={[styles.userDetailsView, {marginTop: 8}]}>
            <Text style={styles.userDetailsText}>Challenge expired</Text>

            <Text
              style={[
                styles.userDetailsText,
                styles.userDetailMarginleft,
                {fontWeight: 'bold'},
              ]}>
              {timeDisplayEnd}
            </Text>
          </View>
        ) : (
          <View style={[styles.userDetailsView, {marginTop: 8}]}>
            <Text style={styles.userDetailsText}>Challenge expires</Text>

            <Text
              style={[
                styles.userDetailsText,
                styles.userDetailMarginleft,
                {fontWeight: 'bold'},
              ]}>
              {timeDisplayEnd}
            </Text>
          </View>
        )}
        <View style={[styles.userDetailsView, {marginTop: 8}]}>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('ChallengeParticipant', {
                challengeId: route.params.challengeId,
              });
            }}>
            <Text style={styles.userDetailsText}>
              Challenge pursued by
              <Text style={{fontWeight: 'bold'}}>
                <Text
                  style={[
                    styles.userDetailsText,
                    styles.userDetailMarginleft,
                    {fontWeight: 'bold'},
                  ]}>
                  {` ${challenges?.participantCount} `}
                </Text>
                others
              </Text>
            </Text>
          </TouchableOpacity>
        </View>

        {challenges?.isLeaderboardCreated && (
          <View style={[styles.leaderboardview]}>
            <TouchableOpacity onPress={onLeaderBoardPress}>
              <Text style={styles.leaderboardText}>Leaderboard</Text>
            </TouchableOpacity>
            {false && (
              <Text style={[styles.userDetailsText, styles.margintopText]}>
                Your points: 561
              </Text>
            )}
            {challenges.pointsDescription && (
              <Text style={[styles.userDetailsText, styles.margintopText]}>
                Leaderboard Points Description:{' '}
                <Text style={{fontWeight: 'bold'}}>
                  {challenges.pointsDescription}
                </Text>
              </Text>
            )}
            <Text
              style={[
                styles.userDetailsText,
                styles.margintopText,
                {marginTop: 8},
              ]}>
              Points Scoring Type:{' '}
              <Text style={{fontWeight: 'bold'}}>
                {challenges.isPointsAutomated ? 'Automated' : 'Manual'}
              </Text>
            </Text>
          </View>
        ) */}
          <ChallengeTrendSetters
            postList={postList}
            onEndPostReached={onEndTrenSetterReached}
            loading={loadingBototm}
          />
          <View>
            <TouchableOpacity onPress={challengeDetailsTogglePress}>
              <View style={[styles.DropDownButton]}>
                <Text style={[acrossAllScreens.SectionHeader]}>
                  Challenge Details{' '}
                </Text>
                <AntDesign
                  name={isChallengeDetailsToggled ? 'down' : 'up'}
                  size={20}
                  color="black"
                />
              </View>
            </TouchableOpacity>
            {!isChallengeDetailsToggled && (
              <View>
                <View style={[styles.challengeDetailsView, {marginTop: 16}]}>
                  <Text style={acrossAllScreens.H2}>Start Date</Text>
                  <Text
                    style={[
                      acrossAllScreens.H2,
                      {fontWeight: 'bold'},
                      styles.margintopText,
                    ]}>
                    {'' + moment(timeStart).format('MMMM D, YYYY')}
                  </Text>
                </View>

                {userId === challenges?.user?.userId && (
                  <View style={[styles.challengeDetailsView, {marginTop: 9}]}>
                    <Text style={acrossAllScreens.H2}>
                      You created the challenge
                    </Text>
                    <Text
                      style={[
                        acrossAllScreens.H2,
                        {fontWeight: 'bold'},
                        styles.margintopText,
                      ]}>
                      {'' + timeDisplayJoined}
                    </Text>
                  </View>
                )}
                {userId != challenges?.user?.userId &&
                  challenges?.isUserJoined && (
                    <View style={[styles.challengeDetailsView, {marginTop: 9}]}>
                      <Text style={acrossAllScreens.H2}>
                        You joined the challenge
                      </Text>
                      <Text
                        style={[
                          acrossAllScreens.H2,
                          {fontWeight: 'bold'},
                          styles.margintopText,
                        ]}>
                        {'' + timeJoinedJoined}
                      </Text>
                    </View>
                  )}
                {currentDateTimeUTC.getTime() - timeEnd.getTime() > 0 ? (
                  <View style={[styles.challengeDetailsView, {marginTop: 8}]}>
                    <Text style={acrossAllScreens.H2}>Challenge expired</Text>

                    <Text
                      style={[
                        acrossAllScreens.H2,
                        {fontWeight: 'bold'},
                        styles.margintopText,
                      ]}>
                      {' ' + timeDisplayEnd}
                    </Text>
                  </View>
                ) : (
                  <View style={[styles.challengeDetailsView, {marginTop: 8}]}>
                    <Text style={acrossAllScreens.H2}>Challenge expires</Text>

                    <Text
                      style={[
                        styles.userDetailsText,
                        {fontWeight: 'bold'},
                        styles.margintopText,
                      ]}>
                      {' ' + timeDisplayEnd}
                    </Text>
                  </View>
                )}
                <View style={[styles.challengeDetailsView, {marginTop: 8}]}>
                  <TouchableOpacity
                    onPress={() => {
                      navigation.navigate('ChallengeParticipant', {
                        challengeId: route.params.challengeId,
                      });
                    }}>
                    <Text style={acrossAllScreens.H2}>
                      Challenge pursued by
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 8
                      }}>
                      {userList.slice(0, 3).map((user, index) => (
                        <FastImage
                          key={user.userId}
                          source={
                            user.imageReference &&
                            user.imageReference !== 'imageReference'
                              ? {
                                  uri: user.imageReference,
                                  headers: {
                                    Authorization: `Bearer ${token}`,
                                  },
                                }
                              : require('../../static/Images/user.png')
                          }
                          resizeMode={'cover'}
                          style={[
                            styles.userImg1,
                            {
                              marginLeft: index > 0 ? -8 : 0,
                              zIndex: 100 - index,
                            },
                          ]}
                        />
                      ))}
                      {/* Display "+X" for remaining participants */}
                      {userList.length > 3 && (
                        <Text
                          style={{
                            color: 'black',
                            fontSize: 14,
                            fontWeight: '700',
                            fontFamily: 'Helvetica Neue',
                            marginLeft: 5,
                          }}>
                          +{userList.length - 3}
                        </Text>
                      )}
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
          {challenges?.isLeaderboardCreated && (
            <View style={[styles.leaderboardview]}>
              <TouchableOpacity onPress={leaderboardDetailsTogglePress}>
                <View style={[styles.DropDownButton]}>
                  <Text style={[acrossAllScreens.SectionHeader]}>
                    Leaderboard{' '}
                  </Text>
                  <AntDesign
                    name={isLeaderboardDetailsToggled ? 'down' : 'up'}
                    size={20}
                    color="black"
                  />
                </View>
              </TouchableOpacity>

              {!isLeaderboardDetailsToggled && (
                <View>
                  <View style={[styles.challengeDetailsView, {marginTop: 16}]}>
                    <Text style={[acrossAllScreens.H2]}>
                      How Points Are Earned:
                    </Text>
                    <Text style={[acrossAllScreens.H3, {marginTop: 8}]}>
                      {getPointType() == '1 Point Per Post' ||
                      challenges?.config?.externalAppType ||
                      challenges?.isPointsAutomated
                        ? 'Automated'
                        : 'Manual'}
                    </Text>
                  </View>
                 
                  {(challenges?.pointsDescription || challenges?.config?.leaderboardType) && (
                    <View
                      style={[
                        styles.challengeDetailsView,
                        styles.margintopText,
                      ]}>
                      <Text style={[acrossAllScreens.H2]}>
                        Leaderboard Points Description:{' '}
                      </Text>

                      <Text style={[acrossAllScreens.H3, styles.margintopText]}>
                      {challenges?.pointsDescription ? challenges.pointsDescription + ' ' : ''}
                        {getLeaderboardType()}
                      </Text>
                    </View>
                  )}

                  {true && (
                    <TouchableOpacity
                      onPress={onLeaderBoardPress}
                      style={styles.DropDownButton}>
                      <View>
                        <Text
                          style={[acrossAllScreens.H2, styles.margintopText]}>
                          Your Points
                        </Text>
                        <Text
                          style={[acrossAllScreens.H3, styles.margintopText]}>
                          {(challenges?.points && challenges?.points > 0)
                            ? challenges?.points 
                            : 'No Points Yet'}
                        </Text>
                      </View>
                      <AntDesign name={'right'} size={20} color="black" />
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          )}
          {userId === challenges?.user?.userId && isActiveChallenge && (
            <View
              style={[
                SignUpStyle.signupButton,
                {alignSelf: 'center', marginTop: 20},
              ]}>
              <TouchableOpacity onPress={confirmEndChallenge}>
                {loading ? (
                  <ActivityIndicator />
                ) : (
                  <Text style={SignUpStyle.signUpText}>End Challenge</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
          {userId != challenges?.user?.userId && isActiveChallenge && (
            <View
              style={[
                SignUpStyle.signupButton,
                {alignSelf: 'center', marginTop: 20},
              ]}>
              {loading ? (
                <ActivityIndicator />
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    if (challenges.isUserJoined) {
                      confirmExitChallenge();
                    } else {
                      onJoinChallenge();
                    }
                  }}>
                  <Text style={SignUpStyle.signUpText}>
                    {challenges.isUserJoined
                      ? 'Exit Challenge'
                      : 'Join Challenge'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  userImg1: {
    width: 24,
    height: 24,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'black',
  },
  loaderContain: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  likeCount: {
    fontSize: 17,
    color: '#000000',
    fontWeight: '400',
    fontFamily: 'Helvetica Neue',
    marginLeft: 2,
  },
  likeImage: {
    width: 28,
    height: 28,
    top: 2,
  },
  likeButton: {
    marginTop: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  videoWrapper: {
    position: 'relative',
  },
  muteButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 5,
  },
  muteIcon: {
    width: 20,
    height: 20,
    tintColor: 'white',
  },
  popoverStyle: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
    paddingVertical: 5,
  },
  optionItem: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    minWidth:40,
  },
  moreIcon: {
    width: 18,
    height: 18,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  backImage: {
    width: 21,
    height: 21,
  },
  headerView: {
    flexDirection: 'row',
    // paddingHorizontal: 16,
    justifyContent: 'space-between',
    // paddingVertical: 16,
  },
  container: {backgroundColor: 'white', flex: 1},
  margintopText: {
    marginTop: 8,
  },
  leaderboardText: {
    fontSize: 20,
    fontWeight: '500',
    lineHeight: 24.5,
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  headerStyle: {
    fontSize: 30,
    fontWeight: 'bold',
    // marginLeft: 10,
    marginBottom: 8,
    color: 'black',
  },
  imageStyle: {
    width: '100%',
    height: 300,
    marginBottom: 8,
  },
  listTextStyle: {
    fontSize: 15,
    marginRight: 3,
    color: 'black',
  },
  userDetailsView: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // marginLeft: 10,
    // marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  userImg: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'gray',
  },
  challengeDetailsView: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginTop: 5,
  },
  leaderboardview: {
    // marginLeft: 10,
    // marginRight: 10,
    marginBottom: 5,
    marginTop: 20,
  },
  userDetailsText: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Helvetica Neue',
    fontWeight: '300',
  },
  userDetailMarginleft: {
    marginLeft: 12,
  },
  userDetail: {
    fontWeight: 'bold',
    color: 'black',
  },
  postcard: {
    // paddingLeft: 10,
    // paddingRight: 10,
    height: width - 20,
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    width: width - 32,
    height: width - 32,
    resizeMode: 'cover',
    backgroundColor: '#000000',
  },
  image: {
    width: '100%',
    height: '100%',
  },

  overlay: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 10,
    padding: 5,
  },
  overlayText: {
    color: 'white',
    fontSize: 16,
  },
  DropDownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});

export default ChallengeDetailsScreen;
