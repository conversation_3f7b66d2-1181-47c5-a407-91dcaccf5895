import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Post} from './ChallengeDetailsFromHomeScreen';
import Video from 'react-native-video';
import FastImage from 'react-native-fast-image';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import {useAppSelector} from '../../redux/Store';
import FullscreenMediaViewer from './FullscreenMediaViewer';
import acrossAllScreens from '../../styles/acrossAllScreens';

export function ChallengeTrendSetters({
  postList,
  onEndPostReached,
  loading,
}: {
  loading: boolean;
  postList: Post[];
  onEndPostReached: () => void;
}) {
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  const [isViewerVisible, setViewerVisible] = useState<boolean>(false);
  const [clickIndex, setClickIndex] = useState<number>(0);

  function extractMediaRefs(data: Post[]) {
    if (!data) {
      return [];
    }

    return data.flatMap(item =>
      (item.mediaRefs || []).map(mediaRef => ({
        mediaRef,
        userName: item.user?.firstName + ' ' + item.user?.lastName,
        mUserId: item.user?.userId,
      })),
    );
  }
  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };
  return (
    <View style={styles.cardStyle}>
      <View style={styles.leaderboardview}>
        <Text style={acrossAllScreens.SectionHeader}>Trendsetters</Text>
      </View>
      <FlatList
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        keyExtractor={key => key.mediaRef}
        onEndReachedThreshold={0.1}
        onEndReached={onEndPostReached}
        ListEmptyComponent={
          <View style={{ alignItems: 'center', paddingVertical: 5 }}>
            <Text style={acrossAllScreens.H3}>Awaiting the first trendsetters</Text>
          </View>
        }
        ListFooterComponent={renderFooter}
        data={extractMediaRefs(postList)}
        renderItem={({item, index}) => {
          return (
            <TouchableOpacity
              onPress={() => {
                setClickIndex(index);
                setTimeout(() => {
                  setViewerVisible(true);
                }, 500);
              }}>
              {isVideoLink(item.mediaRef) ? (
                <Video
                  source={{
                    uri: item.mediaRef,
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  repeat
                  paused={true}
                  useTextureView={false} 
                  resizeMode={'cover'}
                  style={styles.imageTile}
                  bufferConfig={{
                    minBufferMs: 2500,
                    maxBufferMs: 50000,
                    bufferForPlaybackMs: 2500,
                    bufferForPlaybackAfterRebufferMs: 2500,
                  }}
                />
              ) : (
                <FastImage
                  source={{
                    uri: item.mediaRef,
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  resizeMode="cover"
                  style={styles.imageTile}
                />
              )}
            </TouchableOpacity>
          );
        }}
      />
      <FullscreenMediaViewer
        mediaRefs={extractMediaRefs(postList)}
        isVisible={isViewerVisible}
        initialIndex={clickIndex}
        onClose={() => setViewerVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  leaderboardview: {
    marginRight: 10,
    marginBottom: 5,
    marginTop: 10,
  },
  leaderboardText: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  imageTile: {
    height: 120,
    width: Dimensions.get('window').width * 0.255,
    marginRight: 10,
  },
  headerTextStyle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginRight: 10,
    color: 'black',
    marginBottom: 5,
  },
  cardStyle: {
    // marginLeft: 10,
    marginBottom: 10,
  },
});

export default ChallengeTrendSetters;
