import React, {useEffect, useRef, useState} from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ActivityIndicator,
  AppState,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Video from 'react-native-video';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import Icon from 'react-native-vector-icons/Ionicons';
import {muteUnMuteVideo} from '../../redux/Post/PostSlice';
import convertToProxyURL from 'react-native-video-cache';
import acrossAllScreens from '../../styles/acrossAllScreens';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/types';

type NavigationProps = StackNavigationProp<RootStackParamList, 'OtherUserProfileScreen'>;

const {width, height} = Dimensions.get('window');


interface FullscreenMediaViewerProps {
  mediaRefs: {
    mediaRef: string;
    userName: string;
  }[];
  isVisible: boolean;
  onClose: () => void;
  initialIndex: number;
}

const MediaItem = ({
  item,
  index,
  currentIndex,
  onClose,
  mediaRefs,
  handlePrev,
  handleNext,
  appState
}: {
  item: {
    mediaRef: string;
    userName: string;
    mUserId?: string; // Optional, if userId is not always present
  };
  index: number;
  currentIndex: number;
  onClose: () => void;
  mediaRefs: {
    mediaRef: string;
    userName: string;
  }[];
  handlePrev: () => void;
  handleNext: () => void;
  appState: string;
}) => {
  const navigation = useNavigation<NavigationProps>();
  const userState = useAppSelector(state => state.auth);
  const authState = useAppSelector(state => state.auth);
  const {userId} = authState;
  const {token} = userState;
  const isVideo = isVideoLink(item.mediaRef);
  const [loading, setLoading] = useState<boolean>(true);
  const [buffering, setBuffering] = useState<boolean>(false);
  const [overlayVisible, setOverlayVisible] = useState<boolean>(false);
  const timeoutRef = useRef<any>(null);
  const mute = useAppSelector(state => state.post.mute);
  const [isMuted, setIsMuted] = useState(mute);
  const dispatch = useAppDispatch();

  useEffect(() => {
    setIsMuted(mute);
  }, [mute]);

  const toggleMute = () => {
    dispatch(muteUnMuteVideo(!isMuted));
  };

  useEffect(() => {
    if (index === currentIndex) {
      toggleOverlay();
    }
  }, [currentIndex]);

  const toggleOverlay = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setOverlayVisible(true);
    timeoutRef.current = setTimeout(() => setOverlayVisible(false), 5000);
  };

  return (
    <TouchableOpacity
      activeOpacity={1}
      style={styles.mediaContainer}
      onPress={toggleOverlay}>
      {isVideo ? (
        <View style={styles.mediaContainer}>
          <Video
            source={{
              uri: convertToProxyURL(item.mediaRef),
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }}
            repeat
            style={styles.media}
            useTextureView={false}
            bufferConfig={{
              minBufferMs: 2500,
              maxBufferMs: 50000,
              bufferForPlaybackMs: 2500,
              bufferForPlaybackAfterRebufferMs: 2500,
            }}
            muted={isMuted || appState != 'active'}
            ignoreSilentSwitch="ignore"
            paused={currentIndex != index}
            controls={false}
            resizeMode="contain"
            onLoad={() => setLoading(false)}
            onBuffer={({isBuffering}) => setBuffering(isBuffering)}
          />
          <TouchableOpacity onPress={toggleMute} style={styles.muteButton}>
            <Icon
              name={isMuted ? 'volume-mute' : 'volume-high'} // Use icons for mute/unmute
              size={20}
              color="white"
            />
          </TouchableOpacity>
        </View>
      ) : (
        <FastImage
          source={{
            uri: item.mediaRef,
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }}
          style={styles.media}
          resizeMode="contain"
          onLoadEnd={() => setLoading(false)}
        />
      )}
      {(loading || buffering) && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size={'large'} />
        </View>
      )}
      {overlayVisible && (
        <TouchableOpacity
          onPress={() => setOverlayVisible(false)}
          style={styles.overlay}>
            
         <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Image
              style={styles.closeIcon}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.centeredUserName}  onPress={() => {
            onClose();
                  if (item?.mUserId === userId) {
                    console.log('User is the current user', item?.mUserId);
                    navigation.navigate('Profile');
                  } else {
                    console.log('User is another user', item?.mUserId);
                    navigation.navigate('OtherUserProfileScreen', {
                      userId: item?.mUserId,
                    });
                  }
                }}>
            <Text style={styles.closeText}>{item.userName}</Text>
          </TouchableOpacity>
           
          {currentIndex > 0 && (
            <TouchableOpacity style={styles.leftArrow} onPress={handlePrev}>
              <Text style={styles.arrowText}>◀</Text>
            </TouchableOpacity>
          )}
          {currentIndex < mediaRefs.length - 1 && (
            <TouchableOpacity style={styles.rightArrow} onPress={handleNext}>
              <Text style={styles.arrowText}>▶</Text>
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const FullscreenMediaViewer: React.FC<FullscreenMediaViewerProps> = ({
  mediaRefs,
  isVisible,
  onClose,
  initialIndex
}) => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const flatListRef = useRef<SwiperFlatList>(null);
  const [appState, setAppState] = useState('active');


  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      setAppState(nextAppState);
    };
  
    const subscription = AppState.addEventListener('change', handleAppStateChange);
  
    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    flatListRef.current?.scrollToIndex({index: initialIndex, animated: false});
    setCurrentIndex(initialIndex);
  }, [initialIndex]);

  const handleNext = () => {
    if (currentIndex < mediaRefs.length - 1) {
      setCurrentIndex(prevIndex => {
        const nextIndex = prevIndex + 1;
        flatListRef.current?.scrollToIndex({index: nextIndex, animated: true});
        return nextIndex;
      });
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prevIndex => {
        const prevIndexValue = prevIndex - 1;
        flatListRef.current?.scrollToIndex({
          index: prevIndexValue,
          animated: true,
        });
        return prevIndexValue;
      });
    }
  };

  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const index = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentIndex(index);
  };

  const renderItem = ({
    item,
    index,
  }: {
    item: {
      mediaRef: string;
      userName: string;
    };
    index: number;
  }) => {
    return (
      <MediaItem
        item={item}
        currentIndex={currentIndex}
        index={index}
        onClose={onClose}
        mediaRefs={mediaRefs}
        handlePrev={handlePrev}
        handleNext={handleNext}
        appState={appState}
      />
    );
  };

  return (
    <Modal visible={isVisible} transparent onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <SwiperFlatList
          data={mediaRefs}
          horizontal
          ref={flatListRef}
          index={initialIndex}
          pagingEnabled
          keyExtractor={(item, index) => `${item}-${index}`}
          renderItem={renderItem}
          onScroll={onScroll}
          initialNumToRender={1}
          windowSize={1}
          maxToRenderPerBatch={1}
          showsHorizontalScrollIndicator={false}
          onScrollToIndexFailed={info => {
            setTimeout(() => {
              flatListRef.current?.scrollToIndex({
                index: info.index,
                animated: false,
              });
            }, 500);
          }}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  muteButton: {
    position: 'absolute',
    bottom: Platform.OS == 'android' ? 15 : 35,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 5,
  },
  muteIcon: {
    width: 20,
    height: 20,
    tintColor: 'white',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  closeIcon: {
    width: 21,
    height: 18,
    padding: 10,
    tintColor: 'white',
  },
  loaderContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#676464',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaContainer: {
    width,
    height,
    justifyContent: 'center',
    alignItems: 'center',
  },
  media: {
    width: '100%',
    height: '100%',
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS == 'ios' ? 60 : 16,
    left: 16,
    flexDirection: 'row',
    zIndex: 2,
    alignItems: 'center',
  },
  closeText: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '300',
    color: '#FFFFFF',
    marginLeft: 15,
    fontFamily: 'Helvetica Neue',
  },
  centeredUserName: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 16,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1,
  },
  leftArrow: {
    position: 'absolute',
    top: height / 2,
    left: 20,
    zIndex: 2,
  },
  rightArrow: {
    position: 'absolute',
    top: height / 2,
    right: 20,
    zIndex: 2,
  },
  arrowText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  }, HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },

  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default FullscreenMediaViewer;
