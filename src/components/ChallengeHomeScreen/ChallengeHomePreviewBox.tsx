import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import Video from 'react-native-video';
import FastImage from 'react-native-fast-image';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import {useAppSelector} from '../../redux/Store';

const screenWidth = Dimensions.get('window').width;
export function ChallengeHomePreviewBox(props: any) {
  const loggedInUser = props.loggedInUser;
  const currentDate = new Date();
  const currentDateTimeUTC = new Date(currentDate.toISOString());
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  let timeJoined;
  if (loggedInUser === props.userId) {
    timeJoined = new Date(props?.createdAt);
  } else {
    timeJoined = new Date(props?.joinDate);
  }
  const [activeIndex, setActiveIndex] = useState(0);

  const handleScroll = (event: any) => {
    const position = event.nativeEvent.contentOffset.x;
    const activeIndex = Math.round(position / (screenWidth - 50));
    setActiveIndex(activeIndex);
  };

  const Dots = ({length, activeIndex}: any) => (
    <View style={styles.dotsContainer}>
      {new Array(length).fill(0).map((_, idx) => (
        <View
          key={idx}
          style={[
            styles.dot,
            idx === activeIndex ? styles.activeDot : styles.inactiveDot,
          ]}
        />
      ))}
    </View>
  );
  return (
    <View style={styles.card}>
      {props.mediaRefs && (
        <FlatList
          data={props.mediaRefs}
          horizontal
          showsHorizontalScrollIndicator={false}
          pagingEnabled
          onScroll={handleScroll}
          nestedScrollEnabled
          scrollEventThrottle={16}
          keyExtractor={(img, idx) => idx.toString()}
          renderItem={({item: imageSrc, index}) => (
            <TouchableOpacity
              activeOpacity={1}
              onPress={props.onItemClick}
              style={styles.imageContainer}>
              {isVideoLink(imageSrc) ? (
                <Video
                  source={{
                    uri: imageSrc,
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  repeat
                  paused={true}
                  useTextureView={false} 
                  resizeMode={'contain'}
                  style={styles.imageContainer}
                  bufferConfig={{
                    minBufferMs: 2500,
                    maxBufferMs: 50000,
                    bufferForPlaybackMs: 2500,
                    bufferForPlaybackAfterRebufferMs: 2500,
                  }}
                />
              ) : (
                <FastImage
                  source={{
                    uri: imageSrc,
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  resizeMode={'contain'}
                  style={styles.imageContainer}
                />
              )}
            </TouchableOpacity>
          )}
        />
      )}
      {props.mediaRefs && props.mediaRefs.length > 1 && (
        <Dots length={props.mediaRefs.length} activeIndex={activeIndex} />
      )}
      <View style={styles.cardText}>
        <Text style={styles.cardUserDetail}>{`${props.title || 'Title'}`}</Text>
      </View>
      <Text style={styles.descText}>{`${
        props.description || 'Description'
      }`}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  descText: {
    fontSize: 14,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  likeImage: {
    width: 28,
    height: 28,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 5,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
  activeDot: {
    backgroundColor: 'black',
  },
  inactiveDot: {
    backgroundColor: 'gray',
  },
  card: {
    marginTop: 5,
    marginBottom: 5,
    marginStart: 10,
    marginEnd: 10,
  },
  cardHeader: {
    fontSize: 22,
    fontWeight: 'bold',
    marginLeft: 5,
    marginBottom: 8,
    color: 'black',
  },
  cardHeaderExpiry: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 5,
    marginBottom: 8,
    color: 'black',
    textDecorationLine: 'underline',
  },
  cardBottom: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 5,
    color: 'black',
  },
  imageContainer: {
    width: screenWidth - 20,
    height: screenWidth - 20,
    marginBottom: 8,
    backgroundColor: '#dedede'
  },
  cardImage: {
    // alignSelf: 'center',
    width: '100%',
    height: '100%',
  },
  cardText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardPassiveText: {fontSize: 16, color: 'black'},
  cardUserDetail: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
  },
  overlay: {
    position: 'relative', // Position this relative to the container
    backgroundColor: 'rgba(0, 0, 0, 0.2)', // Semi-transparent overlay
    width: '100%',
    height: '80%',
    justifyContent: 'center', // Center the content vertically
    alignItems: 'center', // Center the content horizontally
  },
});

export default ChallengeHomePreviewBox;
