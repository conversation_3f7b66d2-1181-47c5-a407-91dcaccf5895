import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  FlatList,
  Alert,
} from 'react-native';
import ChallengeHomePreviewBox from './ChallengeHomePreviewBox';
import {useDispatch} from 'react-redux';
import {
  deleteChallenge,
  getChallengeCompletedFeed,
  getChallengeFeed,
} from '../../redux/Challenge/ChallengeAction';
import {useAppSelector} from '../../redux/Store';
import FastImage from 'react-native-fast-image';
import moment from 'moment-timezone';
import Popover from 'react-native-popover-view';
import {MaterialTabBar, Tabs} from 'react-native-collapsible-tab-view';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {Platform} from 'react-native';
import Toast from 'react-native-toast-message';
import {useFocusEffect} from '@react-navigation/native';

const ChallengeItem = ({
  navigation,
  userId,
  item,
  expired,
}: {
  navigation: any;
  userId: string;
  item: any;
  expired: boolean;
}) => {
  const user = useAppSelector((state: any) => state.auth.user);
  const popoverRef = useRef<Popover>(null);
  const isOwnPost = item.user.userId == user.userId;
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  const currentUserId = useAppSelector(state => state.auth.userId);
  const dispatch = useDispatch();

  const onItemClick = () => {
    navigation.push('ChallengeDetailsFromHome', {
      title: item.title,
      challengeId: item.challengeId,
      description: item.description,
      userId: item.user.userId,
      createdAt: item.createdAt,
      joinDate: item.createdAt,
      endDate: item.endDate,
      startDate: item.startDate,
      config:item.config,
      difficultyRating: item.difficultyRating,
      participantCount: item.participantCount,
      mediaRefs: item.mediaRefs,
      tags: item.tags,
      likes: item.likes,
      isLiked: item.isLiked,
      location: item.location,
      isLeaderboardCreated: item.isLeaderboardCreated,
      pointsTitle: item.pointsTitle,
      points: item.points,
      pointsDescription: item.pointsDescription,
      isPointsAscending: item.isPointsAscending,
      isPointsAutomated: item.isPointsAutomated,
      visibility: item.visibility,
      loggedInUser: userId,
      isUserJoined: item.isUserJoined,
      userJoinDate: item.userJoinDate,
      firstName: item?.user.firstName,
      lastName: item?.user.lastName,
      imageReference: item?.user.imageReference,
    });
  };

  const onEditClick = () => {
    popoverRef.current?.requestClose();
    navigation.push('ChallengeUpdate', {
      title: item?.title,
      challengeId: item?.challengeId,
      description: item?.description,
      startDate: item?.startDate,
      endDate: item?.endDate,
      isLeaderboardCreated: item?.isLeaderboardCreated,
      pointsTitle: item?.pointsTitle,
      pointsDescription: item?.pointsDescription,
      isPointsAutomated: item?.isPointsAutomated,
      isPointsAscending: item?.isPointsAscending,
      difficultyRating: item?.difficultyRating,
      mediaRefs: item?.mediaRefs,
      visibility: item?.visibility,
      loggedInUser: user.userId,
      tags:item?.tags,
    });
  };

  const onDeleteClick = () => {
    popoverRef.current?.requestClose();
    Alert.alert(
      'Delete Challenge',
      'Are you sure you want to delete this challenge?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            const result = await dispatch(deleteChallenge(item?.challengeId));
            if (result.payload.code == 200) {
            } else {
              Toast.show({
                type: 'error',
                text1: 'Failed to delete challenge.',
                text2: 'Something went wrong.',
              });
            }
          },
          style: 'destructive',
        },
      ],
    );
  };

  const onReportClick = () => {
    popoverRef.current?.requestClose();
  };

  const onUserClick = () => {
    if (currentUserId == item?.user.userId) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('OtherUserProfileScreen', {userId: item?.user.userId});
    }
  };

  return (
    <TouchableOpacity onPress={onItemClick} style={styles.challengeItem}>
      <TouchableOpacity onPress={onUserClick} style={styles.hederContainer}>
        <View style={styles.userInfoContainer}>
          <FastImage
            source={
              item?.user?.imageReference && item?.user?.imageReference !== 'imageReference'
                ? {
                  uri: item?.user?.imageReference,
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                }
                : require('../../static/Images/user.png') 
            }
            style={styles.userImg}
          />
          <View style={styles.userNameContainer}>
            <Text
              style={
                styles.userName
              }>{`${item?.user.firstName} ${item?.user.lastName}`}</Text>
            <Text style={styles.daysAgo}>
              {moment.utc(item?.updatedAt).fromNow()}
              {expired && (
                <Text
                  style={styles.expiredText}>{` ( Challenge Ended ) `}</Text>
              )}
            </Text>
          </View>
        </View>
        <Popover
          ref={popoverRef}
          from={
            <TouchableOpacity>
              <Image
                source={require('../../assets/images/more.png')}
                resizeMode="contain"
                style={styles.moreIcon}
              />
            </TouchableOpacity>
          }
          popoverStyle={styles.popoverStyle}
          backgroundStyle={{opacity: 0}}>
          {isOwnPost ? (
            <>
              <TouchableOpacity style={styles.optionItem} onPress={onEditClick}>
                <Text>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.optionItem}
                onPress={onDeleteClick}>
                <Text>Delete</Text>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity style={styles.optionItem} onPress={onReportClick}>
              <Text>Report</Text>
            </TouchableOpacity>
          )}
        </Popover>
      </TouchableOpacity>
      <ChallengeHomePreviewBox
        title={item.title}
        imageSource={require('../../static/Images/beach.jpg')}
        creatorUserName={item.user.firstName} //Get name from the backedn API
        userId={item.user.userId}
        description={item.description}
        onItemClick={onItemClick}
        mediaRefs={item.mediaRefs}
        createdAt={item.createdAt}
        joinDate={item.createdAt} // this will be replaced by with joinDate for userjoined challenges
        loggedInUser={userId}
        challengeId={item.challengeId}
        endDate={item.endDate}
      />
    </TouchableOpacity>
  );
};

let bottomLoad = false;
export function ChallengeHomeScreen({navigation, route}: any) {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const tabRef = useRef<any>(null);
  const challengesFeed = useAppSelector(
    state => state.challenge.challengesFeed.data,
  );
  const nextPageToken = useAppSelector(
    state => state.challenge.challengesFeed.nextPageToken,
  );
  const hasNext = useAppSelector(
    state => state.challenge.challengesFeed.hasNext,
  );
  const challengesFeedCompleted = useAppSelector(
    state => state.challenge.challengesFeedCompleted.data,
  );
  const nextPageTokenCompleted = useAppSelector(
    state => state.challenge.challengesFeedCompleted.nextPageToken,
  );
  const hasNextCompleted = useAppSelector(
    state => state.challenge.challengesFeedCompleted.hasNext,
  );
  const authState = useAppSelector(state => state.auth);
  const {userId} = authState;

  useEffect(() => {
    if (route.params && route.params?.active && tabRef.current) {
      tabRef.current?.setIndex(0);
    }
  }, [route.params, tabRef.current]);

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        dispatch(getChallengeFeed({userId}));
        dispatch(getChallengeCompletedFeed({userId}));
      }
    }, [userId]),
  );

  const onExploreFeedCompletedEnd = useCallback(() => {
    if (
      challengesFeedCompleted.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasNextCompleted
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeCompletedFeed({
          userId: userId,
          nextPageToken: nextPageTokenCompleted,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [
    dispatch,
    challengesFeedCompleted,
    nextPageTokenCompleted,
    loading,
    hasNextCompleted,
  ]);

  const onExploreFeedEnd = useCallback(() => {
    if (challengesFeed.length > 0 && !loading && !bottomLoad && hasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeFeed({userId: userId, nextPageToken: nextPageToken}),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, challengesFeed, nextPageToken, loading, hasNext]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(getChallengeFeed({userId})).finally(() => setRefreshing(false));
  }, [dispatch, userId]);

  return (
    <View style={styles.container}>
      <View style={[styles.HeaderStyle]}>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>Challenges</Text>
        </View>
      </View>
      <Tabs.Container
        // @ts-ignore
        renderHeader={null}
        ref={tabRef}
        renderTabBar={(props: any) => (
          <MaterialTabBar
            {...props}
            indicatorStyle={{backgroundColor: '#C3E7F5', height: 5}}
          />
        )}
        headerContainerStyle={{shadowColor: 'transparent'}}
        pagerProps={{
          scrollEnabled: false,
        }}>
        <Tabs.Tab
          name="ActiveTab"
          label={() => <Text style={styles.lableStyle}>Active</Text>}>
          <FlatList
            data={challengesFeed}
            keyExtractor={(fri: any) => fri.challengeId}
            onEndReachedThreshold={0.1}
            onEndReached={onExploreFeedEnd}
            ListFooterComponent={renderFooter}
            style={{marginTop: 50}}
            ListEmptyComponent={() => {
              return (
                <View style={styles.loaderContainer}>
                  <Text>There no active challenge data.</Text>
                </View>
              );
            }}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={({item}) => (
              <ChallengeItem
                item={item}
                userId={userId}
                navigation={navigation}
                expired={false}
              />
            )}
            showsVerticalScrollIndicator={false}
          />
        </Tabs.Tab>
        <Tabs.Tab
          name="CompletedTab"
          label={() => <Text style={styles.lableStyle}>Completed</Text>}>
          <FlatList
            data={challengesFeedCompleted}
            keyExtractor={(fri: any) => fri.challengeId}
            style={{marginTop: 50}}
            onEndReachedThreshold={0.1}
            onEndReached={onExploreFeedCompletedEnd}
            ListEmptyComponent={() => {
              return (
                <View style={styles.loaderContainer}>
                  <Text>There no completed challenge data.</Text>
                </View>
              );
            }}
            ListFooterComponent={renderFooter}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={({item}) => (
              <ChallengeItem
                item={item}
                userId={userId}
                navigation={navigation}
                expired={true}
              />
            )}
            showsVerticalScrollIndicator={false}
          />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
}

const styles = StyleSheet.create({
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Platform.OS == 'android' ? 10 : 0,
  },
  hiddenUI: {
    height: 50, // Height of the hidden UI
    backgroundColor: '#ccc', // Background color of hidden UI
    opacity: 0, // Fully hide the UI
  },
  lableStyle: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: '#E5E5E5',
    marginVertical: 5,
  },
  moreIcon: {
    width: 18,
    height: 18,
  },
  optionItem: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    minWidth:40,
  },
  popoverStyle: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
    paddingVertical: 5,
  },
  expiredText: {
    color: 'red',
  },
  sectionHeader: {
    fontSize: 14,
    fontWeight: '700',
    color: 'black',
    fontFamily: 'Helvetica Neue',
    marginHorizontal: 14,
    marginVertical: 10,
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  daysAgo: {
    fontSize: 14,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  userNameContainer: {
    marginLeft: 7,
  },
  userImg: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'gray',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hederContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 10,
    paddingBottom: 8,
    alignItems: 'center',
  },
  challengeItem: {
    marginVertical: 10,
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
});

export default ChallengeHomeScreen;
