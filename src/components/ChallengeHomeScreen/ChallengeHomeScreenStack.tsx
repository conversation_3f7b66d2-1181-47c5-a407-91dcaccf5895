import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import ChallengeDetailsScreen from '../ChallengeDetailsScreen/ChallengeDetailsScreen';
import ChallengeHomeScreen from './ChallengeHomeScreen';
import {ChallengeUpdateScreen} from './ChallengeUpdateScreen';
import CalendarScreenChallUp from '../Common/CalendarScreenChallUp';
import CalendarScreen from '../Common/CalendarScreen';
import ChallengeDetailsFromHomeScreen from '../ChallengeDetailsScreen/ChallengeDetailsFromHomeScreen';
import {TabRouter} from '@react-navigation/routers';
import UserProfileScreen from '../UserProfileScreen/UserProfileScreen';
import UserPostsScreen from '../UserProfileScreen/UserPostsScreen';
import {TouchableOpacity} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import UserActivity from '../UserProfileScreen/UserActivity';
import ChallengeParticipant from '../ChallengeDetailsScreen/ChallengeParticipant';
import LeaderboardDetailsScreen from '../LeaderboardDetailsScreen/LeaderboardDetailsScreen';
import UserConnectionsScreen from '../UserProfileScreen/UserConnectionsScreen';
import ChallengeDetailsScreen2 from '../ChallengeDetailsScreen/ChallengeDetailsScreen2';
import ChallengeLikeUser from './ChallengeLikeUser';
import TagScreen from '../Interest/TagScreen';

const challengeStack = createStackNavigator();

export function ChallengeHomeScreenStack() {
  return (
    <challengeStack.Navigator
      initialRouteName="ChallengesHome"
      screenOptions={{
        headerShown: true,
        gestureEnabled: false,
      }}>
      <challengeStack.Screen
        name="ChallengeHome"
        component={ChallengeHomeScreen}
        options={{title: 'Challenges', header: () => null}}
      />
      <challengeStack.Screen
        name="ChallengeDetails"
        component={ChallengeDetailsScreen}
        options={({route}) => ({title: 'Challenge', header: () => null})}
      />
      <challengeStack.Screen
        name="ChallengeDetailsFromHome"
        component={ChallengeDetailsFromHomeScreen}
        options={({route}) => ({title: 'Challenge', header: () => null})}
      />
      <challengeStack.Screen
        name="ChallengeUpdate"
        component={ChallengeUpdateScreen}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <challengeStack.Screen
        name="ChallengeParticipant"
        component={ChallengeParticipant}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <challengeStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{headerShown: false}}
      />
      <challengeStack.Screen
        name="OtherUserProfileScreen"
        component={UserProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <challengeStack.Screen
        name="UserConnectionsScreen"
        component={UserConnectionsScreen}
        options={({navigation}) => ({
          title: 'Connections',
          header: () => null,
        })}
      />
      <challengeStack.Screen
        name="UserActivity"
        component={UserActivity}
        options={{
          headerShown: false,
        }}
      />
      <challengeStack.Screen
        name="UserPostsScreen"
        component={UserPostsScreen}
        options={() => ({
          title: 'Posts',
          header: () => null,
        })}
      />
      <challengeStack.Screen
        name="LeaderboardDetailsScreen"
        component={LeaderboardDetailsScreen}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />
      <challengeStack.Screen
        name="ChallengeLikeUser"
        component={ChallengeLikeUser}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />
      <challengeStack.Screen
        name="TagScreen"
        component={TagScreen}
        options={{headerShown: false}}
      />
             
    </challengeStack.Navigator>
  );
}

export default ChallengeHomeScreenStack;
