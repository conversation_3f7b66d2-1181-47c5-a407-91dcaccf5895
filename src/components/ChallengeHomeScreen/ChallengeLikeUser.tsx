import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {useAppSelector} from '../../redux/Store';
import FastImage from 'react-native-fast-image';
import {getLikeUserList} from '../../redux/Challenge/ChallengeAction';

export interface User {
  userId: string;
  firstName: string;
  lastName: string;
  imageReference: string;
}

let bottomLoading = false;
export function ChallengeLikeUser({route, navigation}: any) {
  const dispatch = useDispatch();
  const [userList, setUserList] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const authState = useAppSelector(state => state.auth);
  const [nextPageToken, setNextPageToken] = useState('');
  const [isNext, setIsNext] = useState(true);
  const {userId, token} = authState;

  const getParticipantsOfChallengeMethod = async (challengeId: string) => {
    setLoading(true);
    const result = await dispatch(getLikeUserList({challengeId})).unwrap();
    setLoading(false);
    if (result.data) {
      setUserList(result.data);
      setNextPageToken(result?.nextPageToken);
    }
  };

  useEffect(() => {
    if (route.params?.challengeId) {
      getParticipantsOfChallengeMethod(route.params?.challengeId);
    }
  }, [route.params?.challengeId]);

  const onBackPress = () => {
    navigation.pop();
  };

  const onUserClick = (user: User) => {
    if (user.userId === userId) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('OtherUserProfileScreen', {userId: user?.userId});
    }
  };

  const renderUserItem = ({item}: {item: User}) => {
    return (
      <TouchableOpacity
        onPress={() => onUserClick(item)}
        style={styles.userContainer}>
        <FastImage
          source={
            item.imageReference && item.imageReference !== 'imageReference'
              ? {
                  uri: item?.imageReference,
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                }
              : require('../../static/Images/user.png')
          }
          style={styles.userImage}
        />
        <Text
          style={
            styles.activityTitle
          }>{`${item.firstName} ${item.lastName}`}</Text>
      </TouchableOpacity>
    );
  };

  const onEndReached = async () => {
    if (userList.length == 0) {
      return;
    }
    if (!isNext) {
      return;
    }
    if (bottomLoading) {
      return;
    }
    bottomLoading = true;
    const result = await dispatch(
      getLikeUserList({
        challengeId: route.params?.challengeId,
        nextPageToken: nextPageToken,
      }),
    ).unwrap();
    bottomLoading = false;
    if (result.data) {
      setIsNext(result.data.length == 10);
      setUserList([...userList, ...result.data]);
      setNextPageToken(result?.nextPageToken);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerView}>
        <TouchableOpacity onPress={onBackPress}>
          <Image
            style={styles.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Users Who Like Challenge</Text>
        <View style={{width: 20}} />
      </View>
      <FlatList
        data={userList}
        renderItem={renderUserItem}
        style={{flexGrow: 0}}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.1}
      />
      {loading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size={'large'} />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  userContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  userImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  backImage: {
    width: 21,
    height: 21,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    paddingTop: 16,
  },
  container: {backgroundColor: 'white', flex: 1},
});

export default ChallengeLikeUser;
