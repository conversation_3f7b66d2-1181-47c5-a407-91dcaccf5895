import React, {useState, useEffect, useRef} from 'react';
import {
  Text,
  View,
  FlatList,
  TouchableOpacity,
  TouchableHighlight,
  StyleSheet,
  TextInput,
  ScrollView,
  Alert,
  Image,
  Platform,
  Dimensions,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import {useRoute ,useFocusEffect} from '@react-navigation/native';
import Video from 'react-native-video';
import moment from 'moment-timezone';

// import Icon from 'react-native-vector-icons/Feather';

import FonteAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Modal from 'react-native-modal';
import TagFriendsScreen from '../TagFriends/TagFriendsScreen';
import VisibilitySettingsScreen from '../Common/Visibilitysettings';
import DifficultyRatingComponent from '../Common/DifficultyRatingComponent';
import {useDispatch, useSelector} from 'react-redux';
import {
  createNewChallenge,
  updateChallenge,
  getChallenge,
  deleteChallengeMedia,
  addChallengeMedia,
  getChallengeForUpdate,
} from '../../redux/Challenge/ChallengeAction';
import {
  resetUpdateSuccess,
  updateChallengeData,
} from '../../redux/Challenge/ChallengeSlice';
import acrossAllScreens from '../../styles/acrossAllScreens';
import CommonFontstyles from '../../styles/CommonFontStyles';
import SignUpStyle from '../../styles/SignUp';
import DatePicker from 'react-native-date-picker';
import PointsSourceScreen from '../Common/PointsSource';
import PointSortScreen from '../Common/PointSortScreen';
import {useAppSelector} from '../../redux/Store';
import {baseUrl, extractRef,getKeyParamFromUrl, isVideoLink} from '../../utils/Utils';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {muteVideo} from '../../utils/media';
import FastImage from 'react-native-fast-image';
import Toast from 'react-native-toast-message';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import LeaderboardTypeScreen from '../Common/LeaderboardTypeScreen';
import ImportPointDataScreen from '../Common/ImportPointDataScreen';
import AiGenerationModal, {AIGeneratedData} from '../Common/AiGenerationModal';

const {width} = Dimensions.get('window');
export function ChallengeUpdateScreen({navigation, route}: any) {
  const [title, setTitle] = useState(route.params?.title);
  const [description, setDescription] = useState(route.params?.description);
  const [pointsTitle, setPointsTitle] = useState(route.params?.pointsTitle);
  const [pointsDetail, setPointsDetail] = useState(
    route.params?.pointsDescription,
  );

  const [startDate, setStartDate] = useState(route.params?.startDate);
  const [endDate, setEndDate] = useState(route.params?.endDate);
  const [friends, setFriends] = useState('TBD');
  const [visibility, setVisibility] = useState(route.params?.visibility);

  const [pointSource, setPointSource] = useState(
    route.params?.isPointsAutomated ? 'Automated' : 'Manual',
  );
  const [isPointsModalVisible, setPointsModalVisible] = useState(false);
  const [isLeaderboardToggled, setIsLeaderboardToggled] = useState(
    route.params?.isLeaderboardCreated,
  );
  const [isLeaderboardSettingsToggled, setIsLeaderboardSettingsToggled] =    useState(false);
  const [pointSort, setPointSort] = useState(
    route.params?.isPointsAscending ? 'Ascending' : 'Descending',
  );
  const [isPointSortToggled, setIsPointSortToggled] = useState(true);

  const [selectedTags, setSelectedTags] = useState(route.params?.tags ?? []);

  const [isSortModalVisible, setSortModalVisible] = useState(false);

  const [rating, setRating] = useState(route.params?.difficultyRating); // Initial rating state
  const [isImageVisible, setImageVisible] = useState(false);

  const inputRef = useRef<TextInput>(null);
  const [maxRemaianDescLength, setMaxRemainDescLength] = useState(200);
  const [height, setHeight] = useState(48);
  const [isFriendsModalVisible, setFriendsModalVisible] = useState(false);
  const [openTimePicker, setOpenTimePicker] = useState(false);
  const [time, setTime] = useState(new Date());
  const [titleError, setTitleError] = useState('');
  const [pointTitleError, setPointTitleError] = useState('');
  const [pointDescError, setPointDescError] = useState('');
  const [descError, setDescError] = useState('');
  const [mediaError, setMediaError] = useState('');
  const [remoteMedia, setRemoteMedia] = useState([]);
  const [selectedMedia, setSelectedMedia] = useState([]);
  const authState = useAppSelector(state => state.auth);
  const {token} = authState;
  const [loadingItems, setLoadingItems] = useState<{[key: string]: boolean}>(
    {},
  );
  const [numDescriptionLines, setNumDescriptionLines] = useState(1);
  
  const [playingIndex, setPlayingIndex] = useState<number | null>(null);
  const [deletedItem, setDeletedItem] = useState<any[]>([]);
  const challengeDraft: any = useAppSelector(
    state => state.challenge.challengeDraft,
  );
  const [loading, setLoading] = useState(false);

    const [isAiGenerateOpen, setAiGenerateModal] = useState(false);
    const [isAiConfirming, setAiConfirming] = useState(false);
  
  
    const onAiGeneratedConfirm = (data: AIGeneratedData) => {
      if (isAiConfirming) return;
      setAiConfirming(true);
  
      inputRef.current?.blur(); // Ensure keyboard closes cleanly
  
      setTimeout(() => {
        console.log('AI Generated Data:', data);
        setTitle(data.title);
        setDescription(data.description);
        setPointsTitle(data.pointTitle);
        setAiGenerateModal(false);
        setAiConfirming(false);
      }, 250); // Slight delay to avoid race condition
    };

  useEffect(() => {
    if (challengeDraft && challengeDraft.mediaRefs) {
      setRemoteMedia(challengeDraft.mediaRefs);
    }
  }, [challengeDraft]);

  useEffect(() => {
    if (route.params?.selectedMedia) {
      setSelectedMedia(route.params.selectedMedia);
      setRemoteMedia(challengeDraft?.mediaRefs);
    }
  }, [route.params?.selectedMedia]);

  useEffect(() => {
    if (route.params?.challengeId) {
      if (
        !challengeDraft ||
        challengeDraft.challengeId != route.params?.challengeId
      ) {
        dispatch(getChallengeForUpdate(route.params?.challengeId));
      }
    }
  }, [route.params?.challengeId, challengeDraft]);

  const togglePointsModal = () => {
    setPointsModalVisible(!isPointsModalVisible);
  };

  const updatePointSource = (newPointSource: any) => {
    setPointSource(newPointSource);
    console.log('Selected point Source:', pointSource);
  };
  const toggleSortModal = () => {
    setSortModalVisible(!isSortModalVisible);
  };
  const updatePointSort = (newPointSort: any) => {
    setPointSort(newPointSort);
    console.log('Selected point Sort:', pointSort);
  };

  const toggleFriendsModal = () => {
    setFriendsModalVisible(!isFriendsModalVisible);
  };
  const updateFriends = (newFriends: any) => {
    // Join the array of friend names with a space followed by '@'
    const friendsString = newFriends.map((name: any) => `@${name}`).join(' ');
    setFriends(friendsString);
  };
  const toggleImage = () => {
    setImageVisible(!isImageVisible);
  };
  const pointSortTogglePress = () => {
    const nextToggleState = !isPointSortToggled;
    setIsPointSortToggled(nextToggleState);
    setPointSort(nextToggleState ? 'Descending' : 'Ascending');
    console.log(
      'Selected point Sort:',
      nextToggleState ? 'Descending' : 'Ascending',
    );
  };
  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.tags) {
        setSelectedTags(route.params.tags);
        navigation.setParams({ tags: undefined }); // clear after use (optional but prevents reuse)
      }
    }, [route.params?.tags])
  );
  useEffect(() => {
    if (endDate) {
      // Set the time part of `endDate` as initial value for `time`
      const endTime = moment(endDate).toDate(); // Convert to JS Date object
      setTime(endTime);
    }
  }, []);
  useEffect(() => {
    if (route.params?.date && route.params?.type === 'start') {
      setStartDate(route.params.date);
    } else if (route.params?.date && route.params?.type === 'end') {
      setEndDate(route.params.date);
    }
  }, [route.params]);
  let challengeValue: any;
  const dispatch = useDispatch();
  const sdate = new Date(startDate);
  const edate = new Date(endDate);
  console.log(sdate, typeof sdate, edate);

  const imagePath = route.params?.imagePath || null;
  const imageURI = imagePath
    ? Platform.OS === 'android'
      ? `file://${imagePath}`
      : imagePath
    : null;
  console.log('imagePath', imagePath, route.params?.imagePath);

  const handleTextChange = (newText: any) => {
    setDescription(newText);
    setMaxRemainDescLength(200 - newText.length);
  };
  const handleContentSizeChange = (event: any) => {
    const newHeight = event.nativeEvent.contentSize.height;
    if (newHeight > 48) {
      // Ensures the height is not less than 48
      setHeight(newHeight);
    } else {
      setHeight(48); // Sets the minimum height
    }
  };
  const onLayoutHandler = (Name: any) => (event: any) => {
    const {x, y, width, height} = event.nativeEvent.layout;
    console.log(`Name: ${Name},Width: ${width}, Height: ${height}`);
  };
  const leaderboardTogglePress = () => {
    setIsLeaderboardToggled(!isLeaderboardToggled);
  };
  const leaderboardSettingsTogglePress = () => {
    setIsLeaderboardSettingsToggled(!isLeaderboardSettingsToggled);
  };
  const numberToWord = (num: number) => {
    const words = [
      'ZERO',
      'ONE',
      'TWO',
      'THREE',
      'FOUR',
      'FIVE',
      'SIX',
      'SEVEN',
      'EIGHT',
      'NINE',
    ];
    return words[num] || 'THREE';
  };
  const handleRatingChange = (newRating: any) => {
    setRating(newRating);
    console.log('Selected Difficulty Rating:', newRating);
  };

  const validateAllFields = () => {
    const isTitleValid = validateTitle();
    const isDescValid = validateDesc();
    const isMediaValid = validateMedia();
    const isValidatePointTitle = validatePointTitle();
    // const isValidatePointRule = validatePointRule();
    return (
      isTitleValid &&
      isDescValid &&
      isMediaValid &&
      isValidatePointTitle 
      // isValidatePointRule
    );
  };
  const isFieldEmpty = (field: any) => field?.trim() === '';

  const validatePointRule = () => {
    if (pointSource != 'Automated' && isFieldEmpty(pointsDetail)) {
      setPointDescError('*Point Rules is required.');
      return false;
    }
    setPointDescError('');
    return true;
  };

  const validatePointTitle = () => {
    if (isLeaderboardToggled && isFieldEmpty(pointsTitle)) {
      setPointTitleError('*Point Title is required.');
      return false;
    }
    setPointTitleError('');
    return true;
  };

  const validateTitle = () => {
    if (isFieldEmpty(title)) {
      setTitleError('*Title is required.');
      return false;
    }
    setTitleError('');
    return true;
  };
  const validateDesc = () => {
    if (isFieldEmpty(description)) {
      setDescError('*Description is required.');
      return false;
    }
    setDescError('');
    return true;
  };
  const validateMedia = () => {
    if ([...selectedMedia, ...remoteMedia].length == 0) {
      setMediaError('*Media is required.');
      return false;
    }
    setMediaError('');
    return true;
  };

  const updateChallengeMethod = async (challengeValue: any) => {
    try {
      setLoading(true);
      if (selectedMedia.length > 0) {
        const mediaFile = selectedMedia.map((image: any) => ({
          uri: image.uri,
          name: image.filename, // The server usually expects this to be 'name'
          type: image.type,
        }));
        let challengeId = challengeDraft.challengeId;

        const mediaUploadresult = await dispatch(
          addChallengeMedia({challengeId, mediaFile}),
        ).unwrap();
        console.log('Challenge media upload successfully!', mediaUploadresult);
        if (mediaUploadresult.code !== 200) {
          Toast.show({
            type: 'error',
            text1: 'Failed to update media.',
            text2: 'Something went wrong.',
          });
          setLoading(false);
          return;
        }
      }
      const result = await dispatch(updateChallenge(challengeValue)).unwrap();
      console.log('Challenge created successfully!', result);
      if (result.code !== 200) {
        Toast.show({
          type: 'error',
          text1: 'Failed to update challenge.',
          text2: 'Something went wrong.',
        });
        setLoading(false);
        return;
      } else {
        if (deletedItem && deletedItem.length > 0) {
          for (let index = 0; index < deletedItem.length; index++) {
            const element = deletedItem[index];
            await dispatch(deleteChallengeMedia(element));
          }
        }
      }
      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTabsNavigator', params: {screen: 'Challenges'}}],
      });
      setLoading(false);
      dispatch(resetUpdateSuccess());
    } catch (error) {
      setLoading(false);
      Toast.show({
        type: 'error',
        text1: 'Failed to update challenge.',
        text2: 'Something went wrong.',
      });
      console.error('Failed to update challenge:', error);
    }
  };

  const checkBeforeUpdateAndConfirm = () => {
    if (route.params?.isLeaderboardCreated && !isLeaderboardToggled) {
      Alert.alert(
        'Delete Leaderboard',
        'Challenge particiapnts will lose all their leaderboard progress and all participants will be removed from the leaderboard',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Update',
            onPress: async () => {
              onUpdateChallenge();
            },
            style: 'destructive',
          },
        ],
      );
    } else {
      onUpdateChallenge();
    }
  };

  const onUpdateChallenge = () => {
    edate.setUTCHours(time.getUTCHours(), time.getUTCMinutes(), 0, 0);
    if (
      sdate &&
      edate &&
      edate > sdate &&
      isLeaderboardToggled &&
      validateAllFields()
    ) {
      console.log('Visibility', visibility);
      console.log('Updating Challenge with Leaderboard');
      challengeValue = {
        challengeId: challengeDraft?.challengeId,
        title: title,
        description: description,
        visibility: visibility,
        difficultyRating: numberToWord(rating),
        startDate: sdate,
        endDate: edate,
        isLeaderboardCreated: isLeaderboardToggled,
        pointsTitle: pointsTitle,
        pointsDescription: pointsDetail,
        // isPointsAutomated: pointSource == 'Automated' ? true : false,
        isPointsAscending: pointSort == 'Ascending' ? true : false,
        tags: selectedTags,
      };
      console.log(challengeValue);
      updateChallengeMethod(challengeValue);
    } else if (sdate && edate && edate > sdate && validateAllFields()) {
      console.log('Updating Challenge without Leaderboard');
      challengeValue = {
        challengeId: challengeDraft?.challengeId,
        title: title,
        description: description,
        visibility: visibility,
        difficultyRating: numberToWord(rating),
        startDate: sdate,
        endDate: edate,
        isLeaderboardCreated: isLeaderboardToggled,
      };
      console.log(challengeValue);
      updateChallengeMethod(challengeValue);
    } else if (validateAllFields()) {
      Alert.alert(' Please set a date atleast 1 day from today.');
    } else Alert.alert(' Please upload media and fill all necessary details*.');
  };

  const handleVideoTap = (index: number) => {
    setPlayingIndex(prevIndex => (prevIndex === index ? null : index)); // Toggles play/pause
  };

  const handleMuteUnmute = async (uri: string) => {
    const selectedItem: any = selectedMedia.find(
      (item: any) => item.uri === uri,
    );

    if (!selectedItem?.muteUrl) {
      setLoadingItems(prev => ({...prev, [uri]: true}));
      const updatedItems: any = await Promise.all(
        selectedMedia.map(async (item: any) => {
          if (item.uri === uri) {
            if (item.muteUrlStore) {
              item.uri = item.muteUrlStore;
              item.muteUrl = item.muteUrlStore;
              item.originalUri = uri;
            } else {
              const muteUrl = `${uri}_mute.mp4`;
              await muteVideo(uri, muteUrl); // Wait for compression
              item.muteUrl = muteUrl;
              item.uri = muteUrl;
              item.muteUrlStore = muteUrl;
              item.originalUri = uri;
            }
          }
          return item;
        }),
      );
      setLoadingItems(prev => ({...prev, [uri]: false}));
      setSelectedMedia([...updatedItems] as any);
    } else {
      const updatedItems: any = selectedMedia.map((item: any) => {
        if (item.uri === uri) {
          item.uri = item.originalUri;
          item.muteUrlStore = item.muteUrl;
          delete item.muteUrl;
        }
        return item;
      });
      setSelectedMedia([...updatedItems] as any);
    }
  };

  const handleDelete = (uri: any, remote?: boolean, hashRef?: any) => {
    if (remote) {
      const remoteUrl = hashRef + '';
      hashRef = getKeyParamFromUrl(hashRef) ? getKeyParamFromUrl(hashRef) : hashRef;

      Alert.alert(
        'Delete Media',
        'Are you sure you want to delete this media?',
        [
          {
            text: 'No',
            onPress: () => console.log('Ask me later pressed'),
          },
          {
            text: 'Delete',
            onPress: async () => {
              const deleteMedia = {
                id: challengeDraft?.challengeId,
                media: [hashRef],
              };

              setDeletedItem(prev => {
                prev.push(deleteMedia);
                return prev;
              });
              setRemoteMedia((prevMedia: any) =>
                prevMedia.filter((media: any) => media !== remoteUrl),
              );
              const draftPost = {
                ...challengeDraft,
                mediaRefs: challengeDraft.mediaRefs?.filter(
                  (media: any) => media !== remoteUrl,
                ),
              };
              dispatch(updateChallengeData(draftPost));
            },
            style: 'cancel',
          },
        ],
      );
    } else {
      setSelectedMedia((prevMedia: any) =>
        prevMedia.filter((media: any) => media.uri !== uri),
      );
    }
  };

  const renderMediaItem = ({item, index}: any) => {
    const isRemoteMedia = typeof item == 'string';
    const hashRef = item;
    const imageURI =
      typeof item == 'string'
        ? item
        : item.uri
        ? Platform.OS === 'android'
          ? `file://${item.uri}`
          : item.uri
        : null;

    if (isRemoteMedia) {
      item = {
        type: isVideoLink(imageURI) ? 'video' : 'image',
        uri: imageURI,
      };
    }

    return (
      <TouchableOpacity activeOpacity={1} style={styles.mediaItem}>
        {item.type.includes('image') ? (
          <FastImage
            source={{
              uri: imageURI,
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }}
            style={styles.mediaImage}
          />
        ) : (
          <TouchableWithoutFeedback onPress={() => handleVideoTap(index)}>
            <Video
              source={{
                uri: item.uri,
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }}
              paused={playingIndex !== index}
              ignoreSilentSwitch="ignore"
              resizeMode={'cover'}
              useTextureView={false}
              style={styles.mediaImage}
              controls={false}
            />
          </TouchableWithoutFeedback>
        )}

        {/* Delete icon */}
        {[...selectedMedia, ...remoteMedia].length > 1 && (
          <TouchableOpacity
            style={styles.deleteIconContainer}
            onPress={() => handleDelete(item.uri, isRemoteMedia, hashRef)}>
            <MaterialCommunityIcons name="delete" size={24} color="red" />
          </TouchableOpacity>
        )}

        {item.type.includes('video') && (
          <TouchableOpacity
            style={styles.muteIconContainer}
            onPress={() => handleMuteUnmute(item.uri)}>
            {loadingItems[item.uri] ? (
              <ActivityIndicator size="small" color="red" /> // Loader component
            ) : (
              <MaterialCommunityIcons
                name={item.muteUrl ? 'volume-off' : 'volume-high'}
                size={24}
                color="red"
              />
            )}
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  const isAnyItemLoading = () => {
    return Object.values(loadingItems).some(isLoading => isLoading);
  };

  return (
    // <View style={styles.container}>
    <View style={acrossAllScreens.ScreenBackground}>
      
        <View style={[acrossAllScreens.ScreenBorders]}>
          <View
            style={[styles.HeaderStyle]}
            onLayout={onLayoutHandler('HeaderViewTag')}>
            <TouchableOpacity
              style={[
                acrossAllScreens.backImageContainer,
                styles.HeaderbackButton,
              ]}
              disabled={isAnyItemLoading() || loading}
              onPress={() => navigation.goBack()}>
              <Image
                style={acrossAllScreens.backImage}
                source={require('../../assets/images/back.png')}
              />
            </TouchableOpacity>
            <View style={styles.HeaderHorizontalPosition}>
              <Feather name="award" size={20} color="black" />
              <Text style={[acrossAllScreens.ScreenHeaderText]}>
                Update Challenge
              </Text>
            </View>
          </View>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                enableOnAndroid={true}
                extraScrollHeight={Platform.OS === 'ios' ? 60 : 100}>
          {/* {isImageVisible ? (
          <Image style={styles.cardImage} source={{uri: imageURI}} />
        ) : null} */}

          <View
            style={{
              marginTop: 4,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onLayout={onLayoutHandler('Title TextInputViewTag')}>
            <TextInput
              onLayout={onLayoutHandler('Title TextInputTag')}
              style={[
                acrossAllScreens.InputTextLarge,
                {
                  minHeight: 52.7, // Ensures the height is not less than 48
                  textAlignVertical: 'center',
                },
              ]}
              value={title}
              onChangeText={newtext => setTitle(newtext)}
              placeholder="Challenge Title"
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
              maxLength={50}
              multiline={true}
            />
            <TouchableOpacity
              onPress={() => {
                console.log('Napoz AI button pressed for title');
                setAiGenerateModal(true);
              }}>
              <Image
                source={require('../../assets/images/NapozAI.png')}
                style={[styles.napozAIImage, {marginLeft: 2.5}]}
              />
            </TouchableOpacity>
          </View>
          {titleError ? (
            <View style={{marginTop: 10}}>
              <Text style={acrossAllScreens.ErrorText}>{titleError}</Text>
            </View>
          ) : null}
          {[...selectedMedia, ...remoteMedia].length > 0 && (
            <View style={styles.mediaContainer}>
              <FlatList
                data={[...selectedMedia, ...remoteMedia]}
                horizontal
                renderItem={renderMediaItem}
                keyExtractor={item =>
                  typeof item == 'string' ? item : item.uri
                }
                showsHorizontalScrollIndicator={false}
              />
               <TouchableOpacity
                  disabled={isAnyItemLoading() || loading}
                  onPress={() =>
                    navigation.navigate('CameraScreen', {
                      destinationScreen: 'ChallengeUpdate',
                      selectedMedia: selectedMedia,
                      totalMedia:
                        selectedMedia?.length | (0 + remoteMedia?.length) | 0,
                    })
                  }>
                            
                  <Image style={styles.plusButton} source={require('../../assets/images/plus_button.png')} />
                </TouchableOpacity>
            </View>
          )}
          <View
            // style={styles.PartitionStyle}
            onLayout={onLayoutHandler('Desc. TextInputViewTag')}>
            {/* Character count disbale for now  */}
            {/* <Text onLayout={onLayoutHandler('Avail char TextTag')}>
              Available Characters: {maxRemaianDescLength}
            </Text> */}

            <TextInput
              onLayout={onLayoutHandler('Desc. TextInputTag')}
              ref={inputRef}
              style={[
                acrossAllScreens.InputTextRegular,
                {
                 textAlignVertical: 'top',
                                     minHeight: 40,
                                     marginTop: Platform.OS === 'android' ? 4 : 16,
                                     marginBottom:
                                       numDescriptionLines === 1 || description.trim().length === 0
                                         ? 0
                                         : Platform.OS === 'android' ? 5 : 16
                },
              ]}
              value={description}
              onChangeText={handleTextChange}
              onContentSizeChange={event => {
                                handleContentSizeChange(event);
                                const lineHeight = Platform.OS === 'android' ?32 : 20; // approximate line height in pixels
                                const contentHeight = event.nativeEvent.contentSize.height;
                                const lines = Math.max(1, Math.round(contentHeight / lineHeight));
                                console.log('Number of lines:', lines, 'Content Height:', contentHeight , description.trim().length);
                                setNumDescriptionLines(lines);
                              }}
              placeholder="Enter Challenge Description "
              maxLength={400}
              multiline={true}
              onEndEditing={() => inputRef.current?.blur()}
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
            />
            {descError ? (
              <View style={{marginTop: Platform.OS === 'android' ? 10 : 0,marginBottom: 11}}>
                <Text style={acrossAllScreens.ErrorText}>{descError}</Text>
              </View>
            ) : null}
          </View>
          <TouchableOpacity
            style={styles.napozAIButton}
            onPress={() => {
              console.log('Napoz AI button pressed for description');
              setAiGenerateModal(true);
            }}>
            <Image
              source={require('../../assets/images/NapozAI.png')}
              style={styles.napozAIImage}
            />
            <Text style={acrossAllScreens.H2}>Napoz AI</Text>
          </TouchableOpacity>          

         

          <View style={[styles.PartitionStyle, {borderBottomWidth: 0.2}]}>
            <Text style={[acrossAllScreens.H2]}>Challenge Expiry </Text>
            <View style={[{flexDirection: 'row'}, styles.spacerBottom]}>
              <TouchableOpacity
                style={[styles.HorizontalStyle, {marginTop: 7}]}
                disabled={isAnyItemLoading() || loading}
                onPress={() => setOpenTimePicker(true)}>
                <Feather name="clock" size={20} color="black" />
                <DatePicker
                  modal
                  title={'Challenge End Time :'}
                  open={openTimePicker}
                  date={time}
                  mode="time"
                  onConfirm={selectedTime => {
                    console.log(
                      'selected Time',
                      typeof selectedTime,
                      selectedTime,
                    );
                    setOpenTimePicker(false);
                    setTime(selectedTime);
                  }}
                  onCancel={() => {
                    setOpenTimePicker(false);
                  }}
                />
                <Text style={[acrossAllScreens.H3, , {marginLeft: 6}]}>
                  {moment(time).format('hh:mm A')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate('Calendar', {
                    type: 'end',
                    screen: 'ChallengeUpdate',
                  })
                }
                disabled={isAnyItemLoading() || loading}
                style={[
                  styles.HorizontalStyle,

                  {marginTop: 7, marginLeft: 35},
                ]}>
                <AntDesign
                  name="calendar"
                  size={20}
                  color={isAnyItemLoading() || loading ? 'gray' : 'black'}
                />

                <Text style={[acrossAllScreens.H3, , {marginLeft: 6}]}>
                  {/* {endDate} */}
                  {/* {moment.utc(endDate).local().format('YYYY-MM-DD')} */}
                  {moment(endDate).format('YYYY-MM-DD')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={[styles.PartitionStyle, {borderBottomWidth: 0.2}]}>
            <TouchableOpacity
              onPress={() =>
                navigation.push('TagScreen', {existingTags: selectedTags})
              }>
              <View style={[styles.ModalButton]}>
                <Text style={[acrossAllScreens.H3]}>Tags</Text>
                <AntDesign name="right" size={20} color="black" />
              </View>
              <Text style={[acrossAllScreens.H2Italic]}>
                Associating a tag lets users easily find your challenge in the
                explore page if it matches hier interests.
              </Text>
            </TouchableOpacity>
            <View style={[{marginBottom: selectedTags.length ? 11:16}]}>
              {selectedTags.length ? (
                <View style={[styles.tagContainer]}>
                  {selectedTags.map(tag => (
                    <View key={tag} style={styles.tag}>
                      <Text style={styles.tagText}>{tag}</Text>
                    </View>
                  ))}
                </View>
              ) : null}
            </View>
          </View>

          {isLeaderboardToggled ? (
          <View
            style={[
              styles.HorizontalStyle,
              // styles.spacerBottom,
              {justifyContent: 'space-between'},
            ]}>
            <Text style={[acrossAllScreens.H2]}>Create Leaderboard</Text>
            <TouchableOpacity
              onPress={leaderboardTogglePress}
              disabled={isAnyItemLoading() || loading}>
              <FonteAwesome
                name={isLeaderboardToggled ? 'toggle-on' : 'toggle-off'}
                size={30}
                color="#c3e7f5"
              />
            </TouchableOpacity>
          </View>):null}
          {isLeaderboardToggled ? (
            <View>
               <View style={[
                  styles.PartitionStyle,
                  {borderBottomWidth: 0.2},
                ]}> 
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',marginTop:  Platform.OS === 'android' ? 0 : 10,
                      marginBottom: Platform.OS === 'android' ? 0 : 10,
                     
                    }}>
                    <TextInput
                     
                      style={[
                        acrossAllScreens.H3,
                        {minWidth: 60, },
                      ]}
                      onLayout={onLayoutHandler('Points Title TextInputTag')}
                      value={pointsTitle}
                      onChangeText={newtext => setPointsTitle(newtext)}
                      placeholder="Leaderboard Points Column Title: Ex: Score  "
                      placeholderTextColor="grey"
                      underlineColorAndroid="transparent"
                      maxLength={18}
                      multiline={false}
                    />
                    <TouchableOpacity
                      onPress={() => {
                        console.log('Napoz AI button pressed for points title');
                        setAiGenerateModal(true);
                      }}>
                      <Image
                        source={require('../../assets/images/NapozAI.png')}
                        style={[styles.napozAIImage, {marginLeft: 0}]}
                      />
                    </TouchableOpacity>
                  </View>              
                  {pointTitleError ? (
                    <View style={{marginBottom: 5,marginTop: Platform.OS === 'android' ? 0 : 10}}>
                      <Text
                        style={[
                          acrossAllScreens.ErrorText,
                          {position: 'relative'},
                        ]}>
                        {pointTitleError}
                      </Text>
                    </View>
                  ) : null}
                </View>
             <View
                style={[
                  styles.PartitionStyle,
                  {borderBottomWidth: 0.2, paddingBottom: 16},
                ]}>
                <TouchableOpacity onPress={leaderboardSettingsTogglePress}>
                  <View style={[styles.ModalButton]}>
                    <Text style={[acrossAllScreens.H3]}>Advanced Settings</Text>
                    <AntDesign
                      name={isLeaderboardSettingsToggled ? 'up' : 'down'}
                      size={20}
                      color="black"
                    />
                  </View>
                </TouchableOpacity>
                {isLeaderboardSettingsToggled ? (
                  <View>
                    <TextInput
                      style={[acrossAllScreens.H3, {marginBottom: Platform.OS === 'android' ? 0 : 10,marginTop: Platform.OS === 'android' ? 0 : 5}]}
                      onLayout={onLayoutHandler('Points Desc. TextInputTag')}
                      value={pointsDetail}
                      onChangeText={newtext => setPointsDetail(newtext)}
                      placeholder="Enter Point Rules"
                      placeholderTextColor="grey"
                      underlineColorAndroid="transparent"
                      maxLength={50}
                      multiline={true}
                    />
                    <Text style={[acrossAllScreens.H2Italic]}>
                      ( Describe to your participants how they can earn points
                      in this challenge. )
                    </Text>

                    {pointDescError ? (
                      <View style={{marginTop: 5}}>
                        <Text
                          style={[
                            acrossAllScreens.ErrorText,
                            {position: 'relative'},
                          ]}>
                          {pointDescError}
                        </Text>
                      </View>
                    ) : null}

                    <View
                      style={[
                       
                          styles.spacerBottom,
                      ]}>
                      <View
                        style={[
                          styles.HorizontalStyle,
                          // styles.spacerBottom,
                          {justifyContent: 'space-between'},
                        ]}>
                        <Text style={[acrossAllScreens.H2]}>
                          Descending points sort order
                        </Text>
                        <TouchableOpacity
                          onPress={pointSortTogglePress}
                          disabled={isAnyItemLoading() || loading}>
                          <FonteAwesome
                            name={
                              isPointSortToggled ? 'toggle-on' : 'toggle-off'
                            }
                            size={30}
                            color="#c3e7f5"
                          />
                        </TouchableOpacity>
                      </View>
                      <Text style={[acrossAllScreens.H2Italic]}>
                        Napoz will rank users in {pointSort} order of points
                        obtained in this challenge
                      </Text>
                    </View>

                  </View>
                ) : null}
              </View>


            </View>
          ) : null}

          <View
            style={[
              styles.HorizontalStyle,
              {justifyContent: 'space-between'},
              styles.spacerTop,
            ]}>
            <Text style={[acrossAllScreens.H2]}>Difficulty rating</Text>

            <DifficultyRatingComponent
              onRatingChange={handleRatingChange}
              rating={rating}
            />
          </View>

          <View style={[styles.CameraRowStyle]}>
            {/* <TouchableOpacity
              disabled={isAnyItemLoading() || loading}
              onPress={() =>
                navigation.navigate('CameraScreen', {
                  destinationScreen: 'ChallengeUpdate',
                  selectedMedia: selectedMedia,
                  totalMedia:
                    selectedMedia?.length | (0 + remoteMedia?.length) | 0,
                })
              }>
              <FonteAwesome
                name="camera"
                size={75}
                color={isAnyItemLoading() || loading ? 'gray' : 'black'}
              />
            </TouchableOpacity> */}

            <TouchableHighlight
              style={[
                SignUpStyle.signupButton,
                (isAnyItemLoading() || loading) && {backgroundColor: 'gray'},
              ]}
              disabled={isAnyItemLoading() || loading}
              onPress={checkBeforeUpdateAndConfirm}>
              <Text style={SignUpStyle.signUpText}>Update Challenge</Text>
            </TouchableHighlight>
          </View>
          </KeyboardAwareScrollView>
        </View>
      
    </View>
  );
}

const styles = StyleSheet.create({
  muteIconContainer: {
    position: 'absolute',
    top: 40,
    right: 5,
    padding: 5,
    borderRadius: 20,
  },
  deleteIconContainer: {
    position: 'absolute',
    top: 5,
    right: 5,
    padding: 5,
    borderRadius: 20,
  },
  mediaImage: {
    width: 150,
    height: 150,
    borderRadius: 8,
    backgroundColor: '#ccc',
  },
  mediaItem: {
    marginRight: 10,
  },
  mediaContainer: {
    height: 150,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  ColorStyle: {
    color: 'black',
  },
  PartitionStyle: {
    borderBottomWidth: 0.2,
    paddingBottom: 0,
    marginBottom: 16,
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  ModalButton: {
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'space-between',
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  TitleStyle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  DescriptionStyle: {
    textAlignVertical: 'top',
  },
  HorizontalStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  HashtagStyle: {
    fontSize: 18,
    fontWeight: 'bold',
    borderWidth: 1,
    alignSelf: 'flex-start',
    padding: 3,
    marginTop: 5,
    marginBottom: 15,
  },
  SubTitleStyle: {
    fontSize: 18,
  },
  ViewSubTitleStyle: {
    marginTop: 0,
  },
  CameraRowStyle: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  PreviewButtonStyle: {
    fontSize: 18,
    fontWeight: 'bold',
    borderWidth: 1,
    padding: 5,
    borderRadius: 10,
  },
  cardImage: {
    width: '100%',
    height: 300,
    //
  },
  spacerTop: {
    marginTop: 10,
  },
  spacerBottom: {
    marginBottom: 10,
  },
  plusButton: {
    width: 24.5,
    height: 24.5,
    resizeMode: 'contain',
  marginLeft: 8,
  marginRight: 2,
  marginTop: 25,marginBottom: 25},  
  napozAIImage: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginHorizontal: 5,
  },
  createButton: {
    backgroundColor: '#c3e7f5',
    height: 70,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 225,
    borderRadius: 6,
    // marginBottom: 40
  },
  napozAIButton: {
    backgroundColor: '#C3E7F5',

    height: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    // marginTop: Platform.OS === 'android' ? 0 : 16,
    width: 115,
    borderRadius: 15,

  }, tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  tag: {
    backgroundColor: '#C3E7F5',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    margin: 5,
  },
  tagText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default ChallengeUpdateScreen;
