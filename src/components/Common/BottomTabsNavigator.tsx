import React, {useEffect, useState} from 'react';
import {
  BottomTabNavigationOptions,
  createBottomTabNavigator,
} from '@react-navigation/bottom-tabs';
import {
  getFocusedRouteNameFromRoute,
  useNavigation,
} from '@react-navigation/native';
import ChallengeHomeScreenStack from '../ChallengeHomeScreen/ChallengeHomeScreenStack';
import ExploreScreenStack from '../ExploreMainScreen/ExploreScreenStack';
import HomeFeedScreenStack from '../HomeScreen/HomeFeedScreenStack';
import LeaderboardHomeScreenStack from '../Leaderboard/LeaderboardHomeScreenStack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CreateContent from './CreateContent';
import UserProfileScreenStack from '../UserProfileScreen/UserProfileScreenStack';

const BottomTabs = createBottomTabNavigator();

function tabOptions(icon: string): BottomTabNavigationOptions {
  return {
    tabBarActiveTintColor: 'black',
    tabBarInactiveTintColor: 'grey',
    tabBarIcon: iconInfo => {
      return (
        <Icon
          name={icon}
          size={iconInfo.size}
          color={iconInfo.focused ? 'black' : 'grey'}
        />
      );
    },
  };
}

export function BottomTabsNavigator({navigation}: any) {
  const naviagtion = useNavigation();
  const hideFABIcon = [
    'CameraScreen',
    'CreatePostScreen',
    'UpdatePostScreen',
    'ChallengeUpdate',
    'ChallengeCreationScreen',
    'LeaderboardCreationScreen',
  ];
  const [shouldHideCreateContent, setShouldHideCreateContent] = useState(false);

  useEffect(() => {
    // Function to get current screen name
    const getCurrentScreenName = () => {
      const state: any = naviagtion.getState();
      let actualRoute = state.routes[state.index];

      while (actualRoute?.state) {
        actualRoute = actualRoute?.state?.routes[actualRoute.state.index];
      }
      let routeName = actualRoute?.name;
      if (
        actualRoute?.params?.screen &&
        typeof actualRoute?.params?.screen == 'string'
      ) {
        routeName = actualRoute?.params?.screen;
      }
      setShouldHideCreateContent(hideFABIcon.includes(routeName));
    };

    // Log the current screen name whenever the navigation state changes
    const unsubscribe = naviagtion.addListener('state', getCurrentScreenName);
    getCurrentScreenName(); // Get the initial screen on mount

    return unsubscribe; // Cleanup the listener on unmount
  }, [naviagtion]);
  return (
    <React.Fragment>
      <BottomTabs.Navigator
        initialRouteName="Home"
        screenOptions={({route, navigation}) => {
          const navigationState = navigation.getState();
          const routeName =
            getFocusedRouteNameFromRoute(route) ?? 'DefaultScreen';

          return {
            tabBarStyle:
              routeName === 'ChallengeUpdate' ||
              routeName === 'ChallengeDetails' ||
              routeName === 'OtherUserProfileScreen' ||
              routeName === 'UserActivity' ||
              routeName === 'LeaderboardDetailsScreen' ||
              routeName === 'UserPostsScreen' && navigationState.index !== 4 ||
              routeName === 'UserConnectionsScreen' && navigationState.index !== 4 ||
              routeName === 'ChallengeParticipant' && navigationState.index != 2
                ? {display: 'none'}
                : {},
            headerShown: false,
            gestureEnabled: false
          };
        }}>
        <BottomTabs.Screen
          name="Home"
          component={HomeFeedScreenStack}
          options={tabOptions('home')}
        />
        <BottomTabs.Screen
          name="Explore"
          component={ExploreScreenStack}
          options={tabOptions('magnify')}
        />
        <BottomTabs.Screen
          name="Challenges"
          component={ChallengeHomeScreenStack}
          options={tabOptions('medal')}
        />
        <BottomTabs.Screen
          name="Leaderboard"
          component={LeaderboardHomeScreenStack}
          options={tabOptions('podium')}
        />
        <BottomTabs.Screen
          name="Profile"
          component={UserProfileScreenStack}
          options={tabOptions('account-circle')}
        />
      </BottomTabs.Navigator>
      {!shouldHideCreateContent && (
        <CreateContent createContentNav={navigation} />
      )}
    </React.Fragment>
  );
}

export default BottomTabsNavigator;
