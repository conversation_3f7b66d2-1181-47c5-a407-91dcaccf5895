import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {Calendar, CalendarList, Agenda} from 'react-native-calendars';
import {useDispatch, useSelector} from 'react-redux';
import {useRoute} from '@react-navigation/native';
import acrossAllScreens from '../../styles/acrossAllScreens';
export function CalendarScreen({navigation, route}) {
  const [selectedDate, setSelectedDate] = useState('');

  const handleSelectDate = date => {
    const previousScreen = route.params?.screen;
    navigation.navigate(previousScreen, {
      date,
      type: route.params?.type,
    });
  };
  return (
    <View style={styles.container}>
      <View style={[styles.HeaderStyle]}>
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>Calendar</Text>
        </View>
      </View>
      <CalendarList
        pastScrollRange={0}
        futureScrollRange={6}
        minDate={Date()}
        markedDates={{
          [selectedDate]: {selected: true},
        }}
        onDayPress={day => handleSelectDate(day.dateString)}
      />
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
export default CalendarScreen;
