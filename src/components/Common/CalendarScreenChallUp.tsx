import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {Calendar, CalendarList, Agenda} from 'react-native-calendars';
import {useDispatch, useSelector} from 'react-redux';
import {useRoute} from '@react-navigation/native';
export function CalendarScreenChallUp({navigation, route}) {
  const [selectedDate, setSelectedDate] = useState('');

  const handleSelectDate = date => {
    const previousScreen = route.params?.screen;
    navigation.navigate(previousScreen, {
      date,
      type: route.params?.type,
    });
  };
  return (
    <View>
      {/* <Text style={styles.Date}>Date: {selectedDate}</Text>
      <TouchableOpacity style={styles.Button} onPress={handleSelectDate}>
        <Text style={styles.ButtonText}>Done</Text>
      </TouchableOpacity> */}
      <CalendarList
        pastScrollRange={0}
        futureScrollRange={2}
        minDate={Date()}
        markedDates={{
          [selectedDate]: {selected: true},
        }}
        onDayPress={day => handleSelectDate(day.dateString)}
      />
    </View>
  );
}
const styles = StyleSheet.create({});
export default CalendarScreenChallUp;
