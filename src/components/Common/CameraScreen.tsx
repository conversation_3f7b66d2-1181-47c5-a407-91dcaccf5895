import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Linking,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  View,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  AppState,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Camera,
  PhysicalCameraDeviceType,
  useCameraDevices,
  useCameraFormat,
} from 'react-native-vision-camera';
import { checkPermission, requestPermission } from './PermissionsHelper';
import { PermissionStatus } from 'react-native-permissions';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import Video from 'react-native-video';
import * as ImagePicker from 'react-native-image-picker';
import {
  fastForwordVideoWithFFmpeg,
  MAX_VIDEO_DURATION,
  TARGET_VIDEO_DURATION,
} from '../../utils/media';
import Loader from './Loader';
import ImageCropPicker from 'react-native-image-crop-picker';
import acrossAllScreens from '../../styles/acrossAllScreens';

const BACK_BUTTON_ICON_SIZE = 30;
const NEXT_BUTTON_ICON_SIZE = 30;
const CAPTURE_BUTTON_ICON_SIZE = 40;
const MEDIA_BUTTON_ICON_SIZE = 30;

const FLIP_CAMERA_BUTTON_ICON_SIZE = 40;
const FLASH_BUTTON_ICON_SIZE = 40;

const MAX_FILE_SIZE_MB = 50;
const MAX_FILE_SIZE_KB = MAX_FILE_SIZE_MB * 1024 * 1024;

const screenWidth = Dimensions.get('window').width;

// TODO: When recording is completed, it should navigate to the capture screen to show the captured video
// like how we have captured image
export function CameraScreen({ navigation, route }: any) {
  const [cameraPermission, setCameraPermission] = useState<PermissionStatus>();
  const [microphonePermission, setMicrophonePermission] =
    useState<PermissionStatus>();
  const [mediaPermission, setMediaPermission] = useState<PermissionStatus[]>([]);
  const [cameraDirection, setCameraDirection] = useState<'back' | 'front'>(
    'back',
  );
  const [zoomLevel, setZoomLevel] = useState<
    'ultra-wide' | 'wide' | 'telephoto'
  >('wide');
  const [flashMode, setFlashMode] = useState<'on' | 'off'>('off');
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [capturedVideo, setCapturedVideo] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [timer, setTimer] = useState<number | null>(null);
  const cameraRef = useRef<Camera>(null);
  const appState = useRef(AppState.currentState);
  const [isMuted, setIsMuted] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState([]);
  const [isFastForwording, setIsFastForwording] = useState(false);
  const [imageCaptureMode, setImageCaptureMode] = useState(true);

  const toggleRecording = async () => {
    if (cameraRef.current == null) throw new Error('Camera ref is null!');
    if (isRecording) {
      await cameraRef.current.stopRecording();
      setIsRecording(false);
      console.log('Recording has stopped');
    } else {
      cameraRef.current.startRecording({
        flash: flashMode,
        onRecordingError: error => {
          console.error('Recording failed!', error);
        },
        onRecordingFinished: video => {
          console.log(`Recording successfully finished! ${video.path}`);
          CameraRoll.save(video.path);
          setCapturedVideo(video.path);
        },
        videoBitRate: 1,
      });
      setIsRecording(true);
      console.log('Recording in progress');
    }
  };

  useEffect(() => {
    if (timer && timer === 60) {
      toggleRecording();
    }
  }, [timer]);

  useEffect(() => {
    console.log('Permission Status Update:', {
      mediaPermission,
      cameraPermission,
      microphonePermission,
      androidVersion: Platform.OS === 'android' ? Platform.Version : 'N/A',
    });
  }, [mediaPermission, cameraPermission, microphonePermission]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        console.log('App became active - rechecking permissions...');
        checkPermission('camera').then(status => {
          console.log('Camera permission status:', status);
          setCameraPermission(status as PermissionStatus);
        });
        checkPermission('microphone').then(status => {
          console.log('Microphone permission status:', status);
          setMicrophonePermission(status as PermissionStatus);
        });
        checkPermission('media').then(status => {
          console.log('Media permission status:', status);
          const mediaStatus = Array.isArray(status) ? status : [status];
          setMediaPermission(mediaStatus as PermissionStatus[]);
        });
      }

      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  const destinationScreen = route.params?.destinationScreen;
  const destinationStack = route.params?.destinationStack;

  useEffect(() => {
    console.log('In useEffect');
    checkPermission('camera').then(status =>
      setCameraPermission(status as PermissionStatus),
    );
    checkPermission('microphone').then(status =>
      setMicrophonePermission(status as PermissionStatus),
    );
    checkPermission('media').then(status => {
      const mediaStatus = Array.isArray(status) ? status : [status];
      setMediaPermission(mediaStatus as PermissionStatus[]);
    });
    if (route.params?.selectedMedia) {
      setSelectedMedia(route.params?.selectedMedia);
    }
  }, []);

  useEffect(() => {
    if (isRecording) {
      setTimer(0);
      const interval = setInterval(() => {
        setTimer(prevTimer => (prevTimer !== null ? prevTimer + 1 : null));
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setTimer(null);
    }
  }, [isRecording]);

  if (
    cameraPermission == null ||
    microphonePermission == null ||
    mediaPermission == null ||
    mediaPermission.length === 0
  )
    return null;

  if (cameraPermission === 'denied' || microphonePermission === 'denied') {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Welcome to Napoz!</Text>
        <Text style={styles.permissionText}>
          Napoz! requires Camera and Microphone permissions
        </Text>
        <Button
          title="Continue"
          onPress={async () => {
            let cameraStatus = await requestPermission('camera');
            let microphoneStatus = await requestPermission('microphone');
            let mediaStatus = await requestPermission('media');
            setCameraPermission(cameraStatus as PermissionStatus);
            setMicrophonePermission(microphoneStatus as PermissionStatus);
            const mediaStatusArray = Array.isArray(mediaStatus) ? mediaStatus : [mediaStatus];
            setMediaPermission(mediaStatusArray as PermissionStatus[]);
          }}
        />
      </View>
    );
  } else if (
    (cameraPermission === 'granted' && microphonePermission === 'granted') ||
    (Array.isArray(mediaPermission) && mediaPermission.every(
      status => status === 'granted' || status === 'limited',
    ))
  ) {
    const capturePhoto = async () => {
      //Handle this properly
      let photo: any = null;
      if (cameraRef.current) {
        if (Platform.OS == 'ios') {
          photo = await cameraRef.current.takePhoto({
            flash: flashMode,
            // @ts-ignore
            qualityPrioritization: 'speed',
          });
        } else if (Platform.OS == 'android') {
          photo = await cameraRef.current.takePhoto({
            flash: flashMode,
            // @ts-ignore
            qualityPrioritization: 'speed',
          });
        }
        const croppedImage = await ImageCropPicker.openCropper({
          path: `file://${photo.path}`,
          width: (screenWidth * 2) - 32, // Set crop width
          height: (screenWidth * 2)  - 32,
          mediaType: 'photo',
        });
        console.log('Photo path: ', croppedImage.path);
        CameraRoll.save(croppedImage.path);
        setCapturedImage(croppedImage.path);
        console.log('capturedImage1', capturedImage);
      }
    };

    const resetCapture = () => {
      setCapturedImage(null);
      setCapturedVideo(null);
    };

    const navigateNext = async () => {
      // Fixed the navigation to story. Getting pages.map
      console.log('Navigate to next screen');
      console.log('capturedImage2', capturedImage);
      const selectedAssets = {
        uri: capturedImage || capturedVideo,
        type: capturedImage ? 'image/jpeg' : 'video/mp4',
        fileName: capturedImage ? 'captured-image.jpg' : 'captured-video.mp4',
        filename: capturedImage ? 'captured-image.jpg' : 'captured-video.mp4',
      };
      if (destinationStack) {
        navigation.navigate(destinationStack, {
          screen: destinationScreen,
          params: { selectedMedia: [...[selectedAssets], ...selectedMedia] },
        });
      } else {
        navigation.navigate(destinationScreen, {
          selectedMedia: [...[selectedAssets], ...selectedMedia],
        });
      }
    };

    const flipCamera = () => {
      setCameraDirection(prevDirection =>
        prevDirection === 'back' ? 'front' : 'back',
      );
    };

    const toggleFlash = () => {
      setFlashMode(prevMode => (prevMode === 'off' ? 'on' : 'off'));
    };

    const filterFiles = (files: any): any => {
      // Filter out files greater than the max size
      const filteredFiles = files.filter((file: any) => {
        const maxFileSize =
          file.type == 'video/quicktime'
            ? MAX_FILE_SIZE_KB * 1.65
            : MAX_FILE_SIZE_KB;
        if (file.fileSize && file.fileSize > maxFileSize) {
          Alert.alert(
            'File Removed',
            `${file.fileName} is larger than ${MAX_FILE_SIZE_MB} MB and has been removed.`,
            [{ text: 'OK' }],
          );
          return false; // Exclude this file
        }
        return true; // Keep this file
      });

      return filteredFiles;
    };

    const sleep = (ms: number): Promise<void> => {
      return new Promise<void>(resolve => setTimeout(() => resolve(), ms));
    };

    const launchImageLibrary = async () => {
      console.log('Gallery button pressed - checking permissions...');
      console.log('Current mediaPermission state:', mediaPermission);
      console.log('Android version:', Platform.OS === 'android' ? Platform.Version : 'N/A');

      // Check if mediaPermission is properly initialized
      if (!mediaPermission || !Array.isArray(mediaPermission) || mediaPermission.length === 0) {
        console.log('Media permissions not initialized, requesting...');
        try {
          const freshMediaPermission = await checkPermission('media');
          setMediaPermission(freshMediaPermission as PermissionStatus[]);

          // Use the fresh permission status
          const hasMediaPermission = Array.isArray(freshMediaPermission)
            ? freshMediaPermission.every(status => status === 'granted' || status === 'limited')
            : freshMediaPermission === 'granted' || freshMediaPermission === 'limited';

          if (!hasMediaPermission) {
            console.log('Fresh media permissions not granted, requesting...');
            const requestedPermissions = await requestPermission('media');
            setMediaPermission(requestedPermissions as PermissionStatus[]);

            const newHasPermission = Array.isArray(requestedPermissions)
              ? requestedPermissions.every(status => status === 'granted' || status === 'limited')
              : requestedPermissions === 'granted' || requestedPermissions === 'limited';

            if (!newHasPermission) {
              Alert.alert(
                'Permission Required',
                'Media access permission is required to select photos and videos. Please enable it in Settings.',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Open Settings', onPress: () => Linking.openSettings() }
                ]
              );
              return;
            }
          }
        } catch (error) {
          console.error('Error checking/requesting media permissions:', error);
          Alert.alert('Error', 'Failed to check permissions. Please try again.');
          return;
        }
      } else {
        // Check if media permissions are properly granted
        const hasMediaPermission = mediaPermission.every(
          status => status === 'granted' || status === 'limited',
        );

        console.log('hasMediaPermission:', hasMediaPermission);

        if (!hasMediaPermission) {
          // Check if any permissions are permanently blocked
          const hasBlockedPermissions = Array.isArray(mediaPermission) && mediaPermission.some(item => item === 'blocked');

          if (hasBlockedPermissions) {
            Alert.alert(
              'Permission Required',
              'Media access permission is required to select photos and videos. Please enable it in Settings.',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Open Settings', onPress: () => Linking.openSettings() }
              ]
            );
            return;
          }

          // Try to request permissions
          try {
            const mediaPermissionRes: PermissionStatus[] =
              (await requestPermission('media')) as PermissionStatus[];

            // Update the state with new permissions
            setMediaPermission(mediaPermissionRes);

            // Check if permissions were granted after request
            const newHasPermission = mediaPermissionRes.every(
              status => status === 'granted' || status === 'limited',
            );

            if (!newHasPermission) {
              // Check if any are now blocked (user selected "Don't ask again")
              const nowBlocked = mediaPermissionRes.some(status => status === 'blocked');

              if (nowBlocked) {
                Alert.alert(
                  'Permission Required',
                  'Media access permission is required to select photos and videos. Please enable it in Settings.',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Open Settings', onPress: () => Linking.openSettings() }
                  ]
                );
              } else {
                Alert.alert(
                  'Permission Denied',
                  'Media access permission is required to select photos and videos from your gallery.'
                );
              }
              return;
            }

            // Permissions granted, continue with gallery opening
          } catch (error) {
            console.error('Error requesting media permissions:', error);
            Alert.alert(
              'Error',
              'Failed to request permissions. Please try again.'
            );
            return;
          }
        }
        const limit = 5 - (route.params?.totalMedia || 0);
        if (selectedMedia?.length >= limit) {
          Alert.alert('You can select a maximum of 5 media items.');
          return;
        }
        try {
          console.log('Opening image picker with limit:', limit);
          console.log('Current media permission status:', mediaPermission);

          const response = await ImageCropPicker.openPicker({
            multiple: limit > 1,
            mediaType: 'any', // or 'photo' / 'video'
            includeExif: true,
            cropping: false, // Disable automatic cropping to avoid issues
            compressImageQuality: 0.8,
          });

          console.log('Image picker response received:', response);

          // `response` is an array if multiple, otherwise an object
          const selectedAssets = Array.isArray(response) ? response : [response];
          setIsFastForwording(true);

          const filteredAssets = filterFiles(selectedAssets).map((item: any) => {
            item.type = item.mime ? item.mime : item.type;
            item.uri = item.path;
            return item;
          });

          await sleep(600);

          // Crop images
          const processedAssets = [];

          for (let i = 0; i < filteredAssets.length; i++) {
            const asset = filteredAssets[i];

            if (asset.mime?.startsWith('image/')) {
              try {
                const croppedImage = await ImageCropPicker.openCropper({
                  path: asset.path,
                  width: (screenWidth * 2) - 32, // Set crop width
                  height: (screenWidth * 2)  - 32,
                  mediaType: 'photo',
                });

                processedAssets.push({
                  ...asset,
                  path: croppedImage.path,
                  uri: croppedImage.path,
                  mime: croppedImage.mime,
                });
              } catch (error) {
                console.log('Cropper cancelled or failed', error);
                processedAssets.push(asset); // Keep original asset if crop cancelled
              }
            } else {
              asset.duration = asset.duration ? asset.duration / 1000 : 0; // Set duration to 0 if not available
              processedAssets.push(asset); // Skip cropping for non-image files
            }
          }

          // Handle compression/processing
          const compressedAssets = await Promise.all(
            processedAssets.map(async asset => {
              let outputPath;
              if (
                asset.mime?.startsWith('video/') &&
                asset.duration &&
                asset.duration < MAX_VIDEO_DURATION &&
                asset.duration > TARGET_VIDEO_DURATION + 1
              ) {
                outputPath = `${asset.path}_compress_fastforword.mp4`;
                setTimeout(() => {
                  Alert.alert(
                    'The selected video is longer than 1 minute and will be fast-forwarded to a duration of 1 minute.',
                  );
                }, 500);
                try {
                  await fastForwordVideoWithFFmpeg(
                    asset.path,
                    asset.duration,
                    outputPath,
                  );
                } catch (error) {
                  outputPath = asset.path;
                }

                return {
                  path: outputPath,
                  uri: outputPath,
                  mime: 'video/mp4',
                  type: 'video/mp4',
                  filename: asset.filename || 'compressed-video.mp4',
                };
              } else if (
                asset.mime?.startsWith('video/') &&
                asset.duration &&
                asset.duration > MAX_VIDEO_DURATION
              ) {
                Alert.alert(
                  'One of your Video duration exceeds the maximum allowed duration.',
                );
                return null;
              } else {
                return asset;
              }
            }),
          );

          const finalAssets = compressedAssets.filter(Boolean);

          setIsFastForwording(false);
          if (destinationStack) {
            navigation.navigate(destinationStack, {
              screen: destinationScreen,
              params: { selectedMedia: [...finalAssets, ...selectedMedia] },
            });
          } else {
            navigation.navigate(destinationScreen, {
              selectedMedia: [...finalAssets, ...selectedMedia],
            });
          }
        } catch (error) {
          console.error('Error in launchImageLibrary:', error);
          setIsFastForwording(false);

          // Check if it's a user cancellation or a real error
          if (error && typeof error === 'object' && 'code' in error) {
            const errorCode = (error as any).code;

            if (errorCode === 'E_PICKER_CANCELLED') {
              console.log('User cancelled image picker');
              // Don't show error for user cancellation, just return
              return;
            } else if (errorCode === 'E_NO_LIBRARY_PERMISSION') {
              Alert.alert(
                'Permission Required',
                'Please grant permission to access your photo library in Settings.',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Open Settings', onPress: () => Linking.openSettings() }
                ]
              );
              return;
            }
          }

          // For other errors, try fallback with native ImagePicker
          console.log('Trying fallback with native ImagePicker...');
          try {
            const options: ImagePicker.ImageLibraryOptions = {
              mediaType: 'mixed',
              selectionLimit: limit > 1 ? limit : 1,
              quality: 0.8,
              includeBase64: false,
            };

            ImagePicker.launchImageLibrary(options, (response) => {
              if (response.didCancel) {
                console.log('User cancelled native image picker');
                return;
              }

              if (response.errorMessage) {
                console.error('Native ImagePicker error:', response.errorMessage);
                Alert.alert(
                  'Error',
                  'Failed to open gallery. Please check your permissions in Settings.',
                  [
                    { text: 'OK' },
                    { text: 'Open Settings', onPress: () => Linking.openSettings() }
                  ]
                );
                return;
              }

              if (response.assets && response.assets.length > 0) {
                console.log('Native ImagePicker success:', response.assets);
                // Process the selected assets
                const selectedAssets = response.assets.map(asset => ({
                  uri: asset.uri,
                  type: asset.type,
                  fileName: asset.fileName,
                  filename: asset.fileName,
                  path: asset.uri,
                  mime: asset.type,
                }));

                if (destinationStack) {
                  navigation.navigate(destinationStack, {
                    screen: destinationScreen,
                    params: { selectedMedia: [...selectedAssets, ...selectedMedia] },
                  });
                } else {
                  navigation.navigate(destinationScreen, {
                    selectedMedia: [...selectedAssets, ...selectedMedia],
                  });
                }
              }
            });
          } catch (fallbackError) {
            console.error('Fallback ImagePicker also failed:', fallbackError);
            Alert.alert(
              'Error',
              'Failed to open gallery. Please check your permissions in Settings.',
              [
                { text: 'OK' },
                { text: 'Open Settings', onPress: () => Linking.openSettings() }
              ]
            );
          }
        }
      }
    };

    const toggleMute = () => {
      setIsMuted((prevState: boolean) => !prevState);
    };

    return (
      <View style={styles.container}>
        <Loader visible={isFastForwording} text="Processing..." />
        {capturedImage ? (
          <View style={styles.capturedImageContainer}>
            <Image
              source={{
                uri:
                  Platform.OS === 'android'
                    ? `file://${capturedImage}`
                    : capturedImage,
              }}
              resizeMode={'contain'}
              style={styles.capturedImage}
            />
            <TouchableOpacity style={styles.backButton} onPress={resetCapture}>
              <MaterialCommunityIcons
                name="arrow-left"
                size={BACK_BUTTON_ICON_SIZE}
                color="white"
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.nextButton} onPress={navigateNext}>
              <MaterialCommunityIcons
                name="arrow-right"
                size={NEXT_BUTTON_ICON_SIZE}
                color="white"
              />
            </TouchableOpacity>
          </View>
        ) : capturedVideo ? (
          <View style={styles.capturedVideoContainer}>
            <Video
              source={{
                uri:
                  Platform.OS === 'android'
                    ? `file://${capturedVideo}`
                    : capturedVideo,
              }}
              style={styles.capturedImage}
              useTextureView={false}
              ignoreSilentSwitch="ignore"
              controls
              resizeMode={'contain'} // Adjust the video display
            />
            <TouchableOpacity style={styles.backButton} onPress={resetCapture}>
              <MaterialCommunityIcons
                name="arrow-left"
                size={BACK_BUTTON_ICON_SIZE}
                color="white"
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.nextButton} onPress={navigateNext}>
              <MaterialCommunityIcons
                name="arrow-right"
                size={NEXT_BUTTON_ICON_SIZE}
                color="white"
              />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.cameraContainer}>
            <CameraView
              cameraRef={cameraRef}
              audio={!isMuted}
              zoomLevel={zoomLevel}
              cameraDirection={cameraDirection}
            />
          </View>
        )}

        {/* Camera Flip buttons */}
        {!capturedImage && !capturedVideo && !isRecording && (
          <TouchableOpacity
            style={styles.flipCameraButton}
            onPress={flipCamera}>
            <MaterialCommunityIcons
              name="camera-flip"
              size={FLIP_CAMERA_BUTTON_ICON_SIZE}
              color="white"
            />
          </TouchableOpacity>
        )}

        {/* Flash button */}
        {!capturedImage && !capturedVideo && !isRecording && (
          <TouchableOpacity style={styles.flashButton} onPress={toggleFlash}>
            <MaterialCommunityIcons
              name={flashMode === 'on' ? 'flash' : 'flash-off'} // Toggle flash icon
              size={FLASH_BUTTON_ICON_SIZE}
              color="white"
            />
          </TouchableOpacity>
        )}

        {/* Mute button */}
        {!capturedImage && !capturedVideo && !isRecording && (
          <TouchableOpacity style={styles.muteButton} onPress={toggleMute}>
            <MaterialCommunityIcons
              name={isMuted ? 'volume-off' : 'volume-high'}
              size={FLASH_BUTTON_ICON_SIZE}
              color="white"
            />
          </TouchableOpacity>
        )}

        {/* Zoom buttons */}
        {!capturedImage &&
          !capturedVideo &&
          !isRecording &&
          cameraDirection === 'back' && (
            <View style={styles.zoomButtonsContainer}>
              <TouchableOpacity
                style={[
                  styles.zoomButton,
                  zoomLevel === 'ultra-wide' && styles.activeZoomButton,
                ]}
                onPress={() => setZoomLevel('ultra-wide')}>
                <Text style={styles.zoomButtonText}>0.5x</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.zoomButton,
                  zoomLevel === 'wide' && styles.activeZoomButton,
                ]}
                onPress={() => setZoomLevel('wide')}>
                <Text style={styles.zoomButtonText}>1x</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.zoomButton,
                  zoomLevel === 'telephoto' && styles.activeZoomButton,
                ]}
                onPress={() => setZoomLevel('telephoto')}>
                <Text style={styles.zoomButtonText}>3x</Text>
              </TouchableOpacity>
            </View>
          )}

        {/* Camera & Video buttons */}
        {!capturedImage && !capturedVideo && (
          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              onPress={() => {
                if (selectedMedia?.length >= 5) {
                  Alert.alert('You can select a maximum of 5 media items.');
                  return;
                }
                if (imageCaptureMode) {
                  capturePhoto();
                } else {
                  toggleRecording();
                }
              }}>
              {isRecording ? (
                <MaterialCommunityIcons name={'stop'} size={60} color="white" />
              ) : (
                <Image source={require('../../assets/images/capture.png')} />
              )}
            </TouchableOpacity>
          </View>
        )}

        {/* Camera & Video buttons */}
        {!capturedImage && !capturedVideo && !isRecording && (
          <View style={styles.buttonsEndContainer}>
            {imageCaptureMode ? (
              <TouchableOpacity
                style={styles.captureButton}
                onPress={() => setImageCaptureMode(!imageCaptureMode)}>
                <MaterialCommunityIcons
                  name={'video'}
                  size={CAPTURE_BUTTON_ICON_SIZE}
                  color="white"
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.captureButton}
                onPress={() => setImageCaptureMode(!imageCaptureMode)}
                disabled={isRecording}>
                <MaterialCommunityIcons
                  name="camera"
                  size={CAPTURE_BUTTON_ICON_SIZE}
                  color="white"
                />
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Media button */}
        {!capturedImage && !capturedVideo && !isRecording && (
          <TouchableOpacity
            style={styles.mediaButton}
            onPress={launchImageLibrary}>
            <MaterialCommunityIcons
              name="image-multiple"
              size={MEDIA_BUTTON_ICON_SIZE}
              color="white"
            />
          </TouchableOpacity>
        )}
        {/* Close button */}
        {!capturedImage && !capturedVideo && !isRecording && (
          <TouchableOpacity
            style={[acrossAllScreens.backImageContainer, styles.closeButton]}
            onPress={() => navigation.goBack()}>
            <Image
              style={[acrossAllScreens.backImage, { tintColor: 'white' }]}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
        )}
        {isRecording && (
          <View style={styles.timerContainer}>
            <Text style={styles.timerText}>{formatTime(timer)}</Text>
          </View>
        )}
      </View>
    );
  } else {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>Welcome to Napoz!</Text>
        <Text style={styles.permissionText}>
          Napoz! requires Camera and Microphone permissions
        </Text>
        <Button title="Open Settings" onPress={() => Linking.openSettings()} />
      </View>
    );
  }
}

type CameraViewProps = {
  cameraRef: React.RefObject<Camera>;
  zoomLevel: 'ultra-wide' | 'wide' | 'telephoto';
  cameraDirection: 'back' | 'front';
  audio: boolean;
};

function CameraView({
  cameraRef,
  zoomLevel,
  cameraDirection,
  audio,
}: CameraViewProps) {
  let cameraType: PhysicalCameraDeviceType;

  if (cameraDirection === 'front') {
    // Front camera only supports wide-angle
    cameraType = 'wide-angle-camera';
  } else {
    // Back camera allows for zoom levels
    if (zoomLevel === 'ultra-wide') {
      cameraType = 'ultra-wide-angle-camera'; // 0.5x
    } else if (zoomLevel === 'telephoto') {
      cameraType = 'telephoto-camera'; // 3x
    } else {
      cameraType = 'wide-angle-camera'; // 1x (Default)
    }
  }

  const devices = useCameraDevices();
  const directionalCam = devices.filter(
    item => item.position == cameraDirection,
  );
  const device = directionalCam.filter(item =>
    item.physicalDevices.includes(cameraType),
  )[0];
  const format = useCameraFormat(device, [
    { videoResolution: { width: 720, height: 1280 }, photoHdr: false },
  ]);

  if (device == null) return <ActivityIndicator />;
  return (
    <Camera
      ref={cameraRef}
      style={StyleSheet.absoluteFill}
      device={device}
      isActive={true}
      photo={true}
      video={true}
      audio={audio}
      outputOrientation={'device'}
      format={format}
    />
  );
}

function formatTime(seconds: number | null): string {
  if (seconds === null) {
    return '00:00';
  }
  const mins = Math.floor(seconds / 60)
    .toString()
    .padStart(2, '0');
  const secs = (seconds % 60).toString().padStart(2, '0');
  return `${mins}:${secs}`;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
  },
  buttonsEndContainer: {
    position: 'absolute',
    bottom: 30,
    flexDirection: 'row',
    alignSelf: 'flex-end',
    zIndex: 100,
    right: 20,
  },
  buttonsContainer: {
    position: 'absolute',
    bottom: 30,
    flexDirection: 'row',
    alignSelf: 'center',
    zIndex: 100,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingTop: StatusBar.currentHeight,
    backgroundColor: '#ecf0f1',
    padding: 8,
  },
  capturedImageContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  capturedVideoContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  capturedImage: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 16,
      android: 16,
    }),
    left: 15,
    zIndex: 100,
  },
  flipCameraButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 16,
      android: 16,
    }),
    right: 20,
    zIndex: 100,
  },
  flashButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 110,
      android: 90,
    }),
    right: 20,
    zIndex: 100,
  },
  muteButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 169,
      android: 140,
    }),
    right: 20,
    zIndex: 100,
  },
  backButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 40,
      android: 20,
    }),
    left: 10,
    zIndex: 100,
  },
  nextButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 40,
      android: 20,
    }),
    right: 10,
    zIndex: 100,
  },
  permissionText: {
    margin: 24,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    color: 'black',
  },
  mediaButton: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    zIndex: 100,
  },
  captureButton: {
    alignSelf: 'center',
    zIndex: 100,
  },
  timerContainer: {
    position: 'absolute',
    top: Platform.select({
      ios: 40,
      android: 20,
    }),
    alignSelf: 'center',
    zIndex: 100,
  },
  timerText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  zoomButtonsContainer: {
    position: 'absolute',
    bottom: 100, // Adjust to position it above the camera/video icons
    left: 20, // Align to the left, or adjust for a more centered position
    zIndex: 100,
    flexDirection: 'row', // Display buttons horizontally
    justifyContent: 'center', // Center the buttons horizontally
    width: '90%',
  },
  zoomButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
    borderRadius: 20, // Rounded buttons
    padding: 10, // Add padding for better touch area
    marginHorizontal: 10, // Space between buttons
  },
  zoomButtonText: {
    color: 'white', // Text color for zoom levels
    fontSize: 16, // Adjust font size if needed
    textAlign: 'center', // Center the text inside the button
  },
  activeZoomButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)', // Highlight active button
  },
});

export default CameraScreen;
