import React, {useState} from 'react';
import {StyleSheet} from 'react-native';
import {FAB, Portal} from 'react-native-paper';

export function CreateContent(props: any) {
  const [state, setState] = useState({open: false});
  const onStateChange = ({open}: {open: boolean}) => setState({open});
  const {open} = state;

  const iconSize = 'medium';
  const iconColor: string = 'white';

  return (
    <Portal>
      <FAB.Group
        open={open}
        visible
        icon={open ? 'close' : 'plus'}
        color={'white'}
        style={styles.FABGroupStyle}
        fabStyle={styles.FABStyle}
        actions={
          open
            ? [
                {
                  icon: 'note-edit-outline',
                  label: 'Post',
                  color: iconColor,
                  size: iconSize,
                  style: styles.ActionsIconStyle,
                  onPress: () => {
                    console.log('Create Post');
                    props.createContentNav.navigate(
                      'CreatePostScreenStack',
                      {
                        screen: 'CreatePostScreen',
                      },
                    );
                  },
                },
                {
                  icon: 'medal',
                  label: 'Challenge',
                  color: iconColor,
                  size: iconSize,
                  style: styles.ActionsIconStyle,
                  onPress: () => {
                    console.log('Create Challenge');
                    props.createContentNav.navigate(
                      'ChallengeCreationScreenStack',
                      {
                        screen: 'ChallengeCreationScreen',
                      },
                    );
                  },
                },
                {
                  icon: 'podium',
                  label: 'Leaderboard',
                  color: iconColor,
                  size: iconSize,
                  style: styles.ActionsIconStyle,
                  onPress: () => {
                    console.log('Create Leaderboard');
                    props.createContentNav.navigate(
                      'LeaderboardCreationScreenStack',
                      {
                        screen: 'LeaderboardCreationScreen',
                      },
                    );
                  },
                }              
              ]
            : []
        }
        onStateChange={onStateChange}
      />
    </Portal>
  );
}

const styles = StyleSheet.create({
  FABGroupStyle: {
    position: 'absolute',
    bottom: 50,
  },
  FABStyle: {
    backgroundColor: 'black',
    borderRadius: 100,
    opacity: 0.7,
  },
  ActionsIconStyle: {
    backgroundColor: 'black',
    opacity: 0.7,
  },
});

export default CreateContent;
