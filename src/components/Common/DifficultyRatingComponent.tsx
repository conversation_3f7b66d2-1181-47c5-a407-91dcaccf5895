// import React, {useState, useEffect} from 'react';
// import {View, StyleSheet, TouchableOpacity} from 'react-native';
// import FontAwesome from 'react-native-vector-icons/FontAwesome';

// export function DifficultyRatingComponent({rating, onRatingChange}) {
//   // Utility functions to convert between rating words and numbers
//   // const wordsList = ['ONE', 'TWO', 'THREE', 'FOUR', 'FIVE'];
//   const wordsList = ['ONE', 'TWO', 'THREE', 'FOUR', 'FIVE'];


//   const wordsToNumber = word => wordsList.indexOf(word) + 1;

//   // Initialize clickState based on the rating prop using a function in useState
//   const [clickState, setClickState] = useState(() => {
//     const ratingNumber = wordsToNumber(rating); // Convert rating to number
//     return wordsList.map((_, i) => i < ratingNumber); // Create true/false array
//   });

//   // Sync clickState with rating changes from the parent component
//   useEffect(() => {
//     const ratingNumber = wordsToNumber(rating); // Convert rating to number
//     const newClickState = wordsList.map((_, i) => i < ratingNumber); // Update clickState
//     setClickState(newClickState); // Update state
//   }, [rating]); // Trigger this effect when rating prop changes

//   // Handle star press and update the state
//   const handleStarPress = index => {
//     const newClickState = clickState.map((_, i) => i <= index); // Update clickState
//     setClickState(newClickState); // Set new clickState

//     const newRating = index + 1; // Calculate the new rating based on the index
//     if (onRatingChange) {
//       onRatingChange(wordsList[newRating - 1]); // Call the parent's function with the new rating
//     }
//   };

//   // Render the stars based on clickState
//   return (
//     <View style={styles.container}>
//       {clickState.map((_, index) => (
//         <TouchableOpacity key={index} onPress={() => handleStarPress(index)}>
//           <FontAwesome
//             name={clickState[index] ? 'star' : 'star-o'}
//             size={20}
//             color="black"
//           />
//         </TouchableOpacity>
//       ))}
//     </View>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flexDirection: 'row',
//   },
// });

// export default DifficultyRatingComponent;

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';

export function DifficultyRatingComponent({ rating, onRatingChange }) {
  const [clickState, setClickState] = useState(() => {
    return Array.from({ length: 5 }, (_, i) => i < rating);
  });

  useEffect(() => {
    const newClickState = Array.from({ length: 5 }, (_, i) => i < rating);
    setClickState(newClickState);
  }, [rating]);

  const handleStarPress = index => {
    const newRating = index + 1;
    const newClickState = Array.from({ length: 5 }, (_, i) => i <= index);
    setClickState(newClickState);
    if (onRatingChange) {
      onRatingChange(newRating);
    }
  };

  return (
    <View style={styles.container}>
      {clickState.map((isFilled, index) => (
        <TouchableOpacity key={index} onPress={() => handleStarPress(index)}>
          <FontAwesome
            name={isFilled ? 'star' : 'star-o'}
            size={20}
            color="black"
          />
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
});

export default DifficultyRatingComponent;