import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
} from 'react-native';

export function EditProfileScreen({route, navigation}: any) {
  const [userName, setUserName] = useState('');
  const [userBio, setUserBio] = useState('');
  return (
    <View>
      {/* <Text>{route.params.arr}</Text> */}
      <Text style={styles.HeaderStyle}>Edit Profile</Text>
      <View style={styles.ButtonContainer}>
        <TouchableOpacity style={styles.profileButton}>
          <View style={styles.ProfilePicCircle}>
            <Image
              source={require('../../static/Images/vader.png')}
              style={styles.profilePic}
            />
          </View>
          <Text style={styles.profileButtonText}>Edit Profile picture</Text>
        </TouchableOpacity>
      </View>

      <TextInput
        value={userName}
        onChangeText={newtext => setUserName(newtext)}
        placeholder="Enter Username"
        returnKeyLabel="done"
              placeholderTextColor="grey"
        // autoFocus={true}
        style={styles.inputText}
      />
      <TextInput
        value={userBio}
        onChangeText={newtext => setUserBio(newtext)}
        placeholder="Enter Bio"
        returnKeyLabel="done"
              placeholderTextColor="grey"
        // autoFocus={true}
        style={styles.inputText}
      />
      <View style={styles.ButtonContainer}>
        <TouchableOpacity
          onPress={() => {
            navigation.reset({
              index: 0,
              routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
            });
          }}
          style={styles.Button}>
          <Text style={styles.ButtonText}>Done</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    fontSize: 32,
    fontWeight: 'bold',

    marginBottom: 10,
    marginTop: 5,
    color: 'black',
  },
  inputText: {color: 'black', fontSize: 20},
  ButtonContainer: {
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },

  Button: {
    backgroundColor: '#fd9644',
    height: 45,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    width: 100,
    borderRadius: 30,
  },
  ButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },

  ProfilePicCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    marginRight: 10,
    overflow: 'hidden',
  },
  profilePic: {width: '100%', height: '100%'},
  profileButton: {justifyContent: 'center', alignItems: 'center'},
  profileButtonText: {
    color: 'black',
    marginBottom: 5,
    marginTop: 5,
  },
});

export default EditProfileScreen;
