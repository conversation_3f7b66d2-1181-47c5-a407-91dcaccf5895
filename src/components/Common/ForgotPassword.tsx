import React, {useState, useCallback} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  TouchableHighlight,
  Image,
} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import {CommonFontstyles} from '../../styles/CommonFontStyles';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {createAccount} from '../../apis/mock';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Toast from 'react-native-simple-toast';
import {useFocusEffect} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {verifyEmail} from '../../redux/Auth/AuthAction';

export function ForgotPasswordScreen({route, navigation}: any) {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isButtonDisabled, setIsButtonDisabled] = useState(false); // State to track button status
  const dispatch = useDispatch();

  // Validation helper functions
  const isFieldEmpty = field => field.trim() === '';
  const isValidEmail = email => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  // Validation functions for each field
  const validateEmail = () => {
    if (!isValidEmail(email)) {
      setEmailError('*Please enter a valid email address.');
      return false;
    }
    setEmailError('');
    return true;
  };

  const validateAllFields = () => {
    const isEmailValid = validateEmail();

    return isEmailValid;
  };
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  useFocusEffect(
    useCallback(() => {
      setIsButtonDisabled(false);
    }, []),
  );
  return (
    <View style={acrossAllScreens.ScreenBackground}>
      {/* <Text>{route.params.arr}</Text> */}

      <ScrollView
        scrollEnabled={false}
        contentContainerStyle={SignUpStyle.scrollViewContent}>
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={[acrossAllScreens.ScreenBorders]}>
            <View style={SignUpStyle.backButtonPos}>
              <TouchableOpacity
                style={acrossAllScreens.backImageContainer}
                onPress={() => {
                  console.log('Login Screen');
                  navigation.reset({
                    index: 0,
                    routes: [
                      {name: 'WelcomeStack', params: {screen: 'LoginScreen'}},
                    ],
                  });
                }}>
                <Image
                  style={acrossAllScreens.backImage}
                  source={require('../../assets/images/back.png')}
                />
              </TouchableOpacity>

              <Text
                style={[
                  acrossAllScreens.ScreenHeaderText,
                  SignUpStyle.headerPos,
                ]}>
                Forgot Password
              </Text>
              {/* <View style={{width: 20, height: 20}}></View> */}
            </View>

            <View style={[SignUpStyle.signupContainer]}>
              {/* <Text>This feature will be enabled soon ! </Text> */}

              <Text style={[acrossAllScreens.H2, {marginBottom: 12}]}>
                Please enter your email ID to verify your account and reset
                Password :
              </Text>
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder="Email *"
                    placeholderTextColor="grey"
                    keyboardType="email-address"
                    underlineColorAndroid="transparent"
                    onChangeText={text => setEmail(text)}
                    autoCapitalize="none" // Prevents automatic capitalization
                    autoCorrect={false} // Disables auto-correction
                  />
                </View>
                {emailError ? (
                  <Text style={acrossAllScreens.ErrorText}>{emailError}</Text>
                ) : null}
              </View>
                <View style={styles.bottomButtonContainer}>
              <TouchableHighlight
                style={[
                  SignUpStyle.signupButton,
                  // {marginTop: 10},
                  isButtonDisabled && {backgroundColor: 'grey'},
                ]}
                underlayColor="#DDDDDD"
                disabled={isButtonDisabled}
                onPress={() => {
                  console.log('Password reset with email pressed');
                  if (validateAllFields()) {
                    setEmailError('');
                    setIsButtonDisabled(true);
                    console.log('Email:', email);
                    const verifyEmailAPI = async () => {
                     try {
                            const resultAction = await dispatch(verifyEmail(email));
                    // dispatch(verifyEmail(email));
                    
                    if (verifyEmail.fulfilled.match(resultAction)) {
                      Toast.show(
                        'Verification email with reset instructions has been to your registerd email ID.',
                        Toast.LONG,
                      );
                      navigation.reset({
                        index: 0,
                        routes: [
                          {
                            name: 'WelcomeStack',
                            params: {screen: 'LoginScreen'},
                          },
                        ],
                      });
                    } else {
                      // Thunk was rejected
                      Toast.show(
                        'Error resetting password! Try Again',
                        Toast.LONG,
                      );
                     
                    }
                    //navigation.push('PasswordReset');
                  } 
                 catch (error) {
                  Toast.show('Unexpected error occurred!', Toast.LONG);
                  console.error('Unexpected error:', error);
                }
              }
                    verifyEmailAPI();
            }
                  else console.log('Missing Fields for Password reset');
                }}>
                <Text style={SignUpStyle.signUpText}>Verify Account!</Text>
              </TouchableHighlight>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </View>
  );
}
const styles = StyleSheet.create({
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 32,
    width: '100%',
    alignItems: 'center',
  },
});

export default ForgotPasswordScreen;
