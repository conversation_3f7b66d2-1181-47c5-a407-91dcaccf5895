import React, {useState} from 'react';
import {Text, View, StyleSheet, TextInput} from 'react-native';
import {TouchableOpacity} from 'react-native-gesture-handler';

export function HashtagScreen({route, navigation}: any) {
  const [tag, setTag] = useState('');
  return (
    <View>
      {/* <Text>{route.params.arr}</Text> */}
      <TextInput
        value={tag}
        onChangeText={newtext => setTag(newtext)}
        placeholder="#Hashtag"
        returnKeyLabel="done"
        style={{color: 'black'}}
              placeholderTextColor="grey"
        autoFocus={true}
      />
      <View style={styles.DoneStyle}>
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}>
          <Text>Done</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  DoneStyle: {
    alignItems: 'center',
  },
});

export default HashtagScreen;
