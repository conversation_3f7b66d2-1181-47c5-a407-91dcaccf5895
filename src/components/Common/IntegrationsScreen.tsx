import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Linking,
  Platform,
  Alert,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import acrossAllScreens from '../../styles/acrossAllScreens';
import SignUpStyle from '../../styles/SignUp';

const interestData = require('../../static/InterestScreenStatic/InterestScreenCategories.json');

export function IntegrationsScreen({navigation, route}) {
  const existingTags = route.params?.existingTags || []; // Get existing selected tags
  const destinationScreen = route.params?.destinationScreen ?? 'ChallengeCreationScreen';
  const destinationStack = route.params?.destinationStack;

  const [selectedItems, setSelectedItems] = useState(new Set(existingTags));

  const integratedDevices = {
    apple: true,
    google: true,
  };

  const [selectedIntegration, setSelectedIntegration] = useState<string | null>(
    null,
  );

  useEffect(() => {
    const appMap = {
      'Apple Health': 'apple',
      'Google Health': 'google',
    };

    const externalAppKey = appMap[route.params?.externalApp];
    if (externalAppKey && integratedDevices[externalAppKey]) {
      setSelectedIntegration(externalAppKey);
    }
  }, [route.params?.externalApp]);

  const availableIntegrations = [
    {
      key: 'apple',
      label: 'Apple Fitness+',
      image: require('../../assets/images/AppleHealth.png'),
      platform: 'ios',
    },
    {
      key: 'google',
      label: 'Google Health',
      image: require('../../assets/images/GoogleHealth.png'),
      platform: 'android',
    },
  ];

  const getDisplayName = (key: string | null): string | null => {
    switch (key) {
      case 'apple':
        return 'Apple Health';
      case 'google':
        return 'Google Health';
      default:
        return null;
    }
  };

  const handlePress = (type: 'apple' | 'google') => {
    if (integratedDevices[type]) {
      setSelectedIntegration(prev => (prev === type ? null : type));
    } else {
      if (type === 'apple') {
        Linking.openURL('App-Prefs:Privacy&path=HEALTH');
      } else {
        Linking.openURL('https://support.google.com/fit/');
      }
    }
  };

  const handleConfirm = () => {
    const externalApp = getDisplayName(selectedIntegration);

    if (selectedIntegration) {
      console.log('Confirmed:', externalApp);

      if (destinationStack) {
        console.log('Navigating to:', destinationStack, destinationScreen);
        navigation.navigate(destinationStack, {
          screen: destinationScreen,
          params: { externalApp },
        });
      } else {
        console.log('Navigating to:', destinationStack, destinationScreen);
        navigation.navigate(destinationScreen, { externalApp });
      }
    } else {
      Alert.alert(
        'No Integration Selected',
        'Do you want to continue without selecting an integration?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Continue',
            onPress: () => {
              if (destinationStack) {
                navigation.navigate(destinationStack, {
                  screen: destinationScreen,
                  params: { externalApp: null },
                });
              } else {
                navigation.navigate(destinationScreen, { externalApp: null });
              }
            },
          },
        ],
        {cancelable: true},
      );
    }
  };

  const onClick = useCallback(category => {
    setSelectedItems(prevItems => {
      const newItems = new Set(prevItems);
      newItems.has(category)
        ? newItems.delete(category)
        : newItems.add(category);
      return newItems;
    });
  }, []);

  return (
    <View style={styles.container}>
      {/* HEADER */}
      <View style={[styles.HeaderStyle]}>
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>Integrations</Text>
        </View>
      </View>

      {/* DESCRIPTION */}
      <Text style={styles.description}>
        Please select the external app most relatable to your challenge. If
        currenlty unintegrated, please select the app you would like to
        integrate with Napoz and porvide necessary permissions. Also available
        in the settings.
      </Text>

      {/* INTEGRATION SELECTOR UI */}
      <FlatList
        data={availableIntegrations.filter(integration => {
          if (integration.platform === 'ios' && Platform.OS !== 'ios')
            return false;
          if (integration.platform === 'android' && Platform.OS !== 'android')
            return false;
          return true;
        })}
        keyExtractor={item => item.key}
        numColumns={2}
        columnWrapperStyle={{justifyContent: 'space-between'}}
        contentContainerStyle={{paddingBottom: 100}}
        renderItem={({item}) => {
          const isIntegrated = integratedDevices[item.key];
          const isSelected = selectedIntegration === item.key;

          return (
            <View style={styles.integrationItem}>
              <TouchableOpacity
                onPress={() => handlePress(item.key)}
                style={[
                  styles.integrationBox,
                  isIntegrated ? styles.enabled : styles.disabled,
                  isSelected && styles.selected,
                ]}>
                <Image source={item.image} style={styles.icon} />
              </TouchableOpacity>
              <Text
                style={[
                  styles.label,
                  !isIntegrated && {color: 'gray', fontWeight: '500'},
                ]}>
                {isIntegrated ? item.label : 'Unintegrated by user'}
              </Text>
            </View>
          );
        }}
      />

      <View style={styles.bottomButtonContainer}>
        <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
          <Text style={SignUpStyle.signUpText}>Confirm</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

// STYLES
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: 16,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },

  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  updateButton: {
    fontSize: 16,
    color: '#C3E7F5',
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 10,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 20,
  },
  integrationItem: {
    alignItems: 'center',
    marginBottom: 20,
    flexBasis: '48%',
  },
  integrationBox: {
    width: 140,
    height: 123,
    borderRadius: 10,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  enabled: {
    backgroundColor: '#fff',
    borderColor: '#ccc',
  },
  disabled: {
    backgroundColor: '#eee',
    borderColor: '#ccc',
  },
  selected: {
    borderColor: '#C3E7F5',
    borderWidth: 5,
  },
  icon: {
    width: 70,
    height: 70,
    resizeMode: 'contain',
    marginBottom: 10,
  },
  label: {
    textAlign: 'center',
    fontSize: 14,
  },
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  confirmButton: {
    backgroundColor: '#C3E7F5',
    padding: 22,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 80,
  },
  confirmText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default IntegrationsScreen;
