import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  Image,
  TouchableOpacity,
  Button,
  Dimensions,
  Linking,
  Alert,
} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import {useDispatch, useSelector} from 'react-redux';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {cos} from 'react-native-reanimated';

export function LandingPage({route, navigation}: any) {
  const {height} = Dimensions.get('window');
  console.log('height', height);

  console.log('Landing Page screen was run');
  const handleEmailPress = () => {
    const email = '<EMAIL>';
    const subject = '[Napoz]Support request from "{Csutomer name}"'; // Optional
    const body = ''; // Optional
    const mailto = `mailto:${email}?subject=${encodeURIComponent(
      subject,
    )}&body=${encodeURIComponent(body)}`;

    Linking.openURL(mailto).catch(err => {
      console.error('Error:', err);
      Alert.alert('Error', 'Unable to open mail app');
    });
  };
  return (
    <View style={[acrossAllScreens.ScreenBackground]}>
      <View style={[acrossAllScreens.ScreenBorders]}>
        <TouchableOpacity
          style={SignUpStyle.screenRedirectButton}
          onPress={() => {
            console.log('Login Screen');
            // navigation.push('LoginScreen');
            navigation.reset({
              index: 0,
              routes: [{name: 'WelcomeStack', params: {screen: 'LoginScreen'}}],
            });
          }}>
          <AntDesign name="close" size={30} color="black" />
        </TouchableOpacity>
        <View style={[styles.ScreenStyle]}>
          <Text
            style={[
              acrossAllScreens.LandingPageText,
              {marginTop: 0.2 * height - 46.5},
            ]}>
            Welcome to Napoz!
          </Text>
          <Image
            style={styles.LogoStyle}
            source={require('../../static/Images/Napoz_Logo.png')}
          />
          <Text style={[acrossAllScreens.LandingPageText, {marginTop: 10}]}>
            App Loading soon
          </Text>
          <TouchableOpacity onPress={handleEmailPress}>
            <Text
              style={[
                acrossAllScreens.ContactUsText,
                {marginTop: 0.3 * height},
              ]}>
              <EMAIL>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  ScreenStyle: {
    alignItems: 'center',
  },

  LogoStyle: {
    height: 100,
    width: 100,
    marginTop: 20,
  },
  ContactStyle: {
    position: 'absolute',
    bottom: 1,
  },
});

export default LandingPage;
