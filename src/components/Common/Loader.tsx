import React from 'react';
import {
  View,
  ActivityIndicator,
  StyleSheet,
  Text,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface LoaderProps {
  size?: 'small' | 'large';
  color?: string;
  style?: ViewStyle;
  squareSize?: number;
  squareColor?: string;
  squareBorderRadius?: number;
  text?: string;
  textStyle?: TextStyle;
  visible?: boolean;
}

const Loader: React.FC<LoaderProps> = ({
  visible = false,
  size = 'large',
  color = '#000000',
  style,
  squareSize = 150,
  squareColor = "#ffffff00",
  squareBorderRadius = 10,
  text = 'Loading...',
  textStyle,
}) => {
  if (!visible) return null;

  return (
    <View style={[styles.loaderContainer, style]}>
      <View
        style={[
          styles.square,
          {
            width: squareSize,
            height: squareSize * 0.8,
            backgroundColor: squareColor,
            borderRadius: squareBorderRadius,
          },
        ]}>
        <ActivityIndicator size={size} color={color} />
        <Text style={[styles.text, textStyle]}>{text}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  square: {
    marginTop: 20,
    marginBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    color: '#000000',
    marginTop: 10,
  },
});

export default Loader;
