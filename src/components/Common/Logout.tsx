import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  Image,
  TouchableOpacity,
} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useDispatch, useSelector} from 'react-redux';
import {logout} from './../../redux/Logout/LogoutAction';

// Usage

export function Logout({route, navigation}: any) {
  const userProfileState = useSelector(state => state.user);

  const authState = useSelector(state => state.auth);
  const challState = useSelector(state => state.challenge);
  // Delete an item
  const deleteItem = async key => {
    try {
      await AsyncStorage.removeItem(key);
      console.log('Item deleted successfully');
    } catch (error) {
      console.log('Error deleting item:', error);
    }
  };
  const dispatch = useDispatch();
  const handleLogout = () => {
    // Dispatch the logout action
    console.log('called');
    dispatch(logout());

    navigation.reset({
      index: 0,
      routes: [{name: 'WelcomeStack', params: {screen: 'LoginScreen'}}],
    });
  };
  return (
    <View style={styles.ScreenStyle}>
      <View>
        <TouchableOpacity
          style={[SignUpStyle.buttonContainer, SignUpStyle.signupButton]}
          onPress={() => {
            console.log('Logout from app');
            deleteItem('user');
            deleteItem('token');
            handleLogout();
          }}>
          <Text style={SignUpStyle.signUpText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  ScreenStyle: {
    alignItems: 'center',
    backgroundColor: 'white',
    flex: 1,
    justifyContent: 'center',
  },
  ButtonAlignmentStyle: {
    alignItems: 'center',
  },
  LogoStyle: {
    height: 200,
    width: 200,
    marginBottom: 10,
    marginTop: 10,
  },
  WelcomeTextStyle: {
    fontWeight: 'bold',
    fontSize: 30,
  },
});

export default Logout;
