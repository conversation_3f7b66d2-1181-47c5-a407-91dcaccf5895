import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  TouchableHighlight,
  Alert,
  Image,
} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import {CommonFontstyles} from '../../styles/CommonFontStyles';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {createAccount} from '../../apis/mock';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Toast from 'react-native-simple-toast';

export function OtpScreen({route, navigation}: any) {
  const [otp, setOtp] = useState('');

  const [otpError, setOtpError] = useState('');

  // Validation helper functions
  const isFieldEmpty = field => field.trim() === '';

  // Validation functions for each field

  const validateOtp = () => {
    // Example: Simple length check, modify regex as needed
    if (otp.length < 4 || otp.length > 6) {
      setOtpError('*OTP must be 5 digits.');
      return false;
    }
    setOtpError('');
    return true;
  };
  const validateAllFields = () => {
    const isOtpValid = validateOtp();

    return isOtpValid;
  };
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  return (
    <View style={acrossAllScreens.ScreenBackground}>
      {/* <Text>{route.params.arr}</Text> */}
      <ScrollView
        scrollEnabled={false}
        contentContainerStyle={SignUpStyle.scrollViewContent}>
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={[acrossAllScreens.ScreenBorders]}>
            <View style={SignUpStyle.backButtonPos}>
              <TouchableOpacity
                style={acrossAllScreens.backImageContainer}
                onPress={() => {
                  navigation.push('ForgotPassword');
                }}>
                <Image
                  style={acrossAllScreens.backImage}
                  source={require('../../assets/images/back.png')}
                />
              </TouchableOpacity>

              <Text
                style={[
                  acrossAllScreens.ScreenHeaderText,
                  SignUpStyle.headerPos,
                ]}>
                Forgot Password : OTP
              </Text>
              {/* <View style={{width: 20, height: 20}}></View> */}
            </View>

            <View style={[SignUpStyle.signupContainer]}>
              <Text style={[acrossAllScreens.H2, {marginBottom: 12}]}>
                Please enter the OTP you received on your Email Id or Phone
                Number :
              </Text>

              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder="OTP *"
                    keyboardType="phone-pad"
                    placeholderTextColor="grey"
                    secureTextEntry={true}
                    underlineColorAndroid="transparent"
                    onChangeText={text => setOtp(text)}
                  />
                </View>
                {otpError ? (
                  <Text style={acrossAllScreens.ErrorText}>{otpError}</Text>
                ) : null}
              </View>
              <TouchableOpacity
                onPress={() => {
                  console.log('Resend OTP');
                  Toast.show(
                    'OTP was resent. Please wait upto 2 mins for it to arrive.',
                  );
                }}>
                <Text style={[acrossAllScreens.H2, {marginBottom: 22}]}>
                  Resend OTP
                </Text>
              </TouchableOpacity>

              <TouchableHighlight
                style={[SignUpStyle.signupButton, {marginTop: 10}]}
                underlayColor="#DDDDDD"
                onPress={() => {
                  console.log('Password Reset with OTP pressed');
                  if (validateAllFields()) {
                    setOtpError('');
                    navigation.push('PasswordReset');
                  } else console.log('Missing OTP for Password reset');
                }}>
                <Text style={SignUpStyle.signUpText}>VERIFY</Text>
              </TouchableHighlight>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </View>
  );
}

export default OtpScreen;
