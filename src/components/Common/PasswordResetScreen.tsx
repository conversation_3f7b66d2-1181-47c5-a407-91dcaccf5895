import React, {useState, useEffect} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  TouchableHighlight,
} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import {CommonFontstyles} from '../../styles/CommonFontStyles';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {createAccount} from '../../apis/mock';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Toast from 'react-native-simple-toast';
import {useDispatch, useSelector} from 'react-redux';

import {forgotPassword} from '../../redux/Auth/AuthAction';
import {Image} from 'react-native';

export function PasswordResetScreen({route, navigation}: any) {
  const [password, setPassword] = useState('');
  const [cPassword, setCPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [resetToken, setResetToken] = useState<string | null>(null);

  const [passwordError, setPasswordError] = useState('');
  const [cPasswordError, setCPasswordError] = useState('');
  const dispatch = useDispatch();

  useEffect(() => {
    if (route.params?.resetToken) {
      setResetToken(route.params.resetToken);
      console.log('Received Reset Token:', route.params.resetToken);
    }
  }, [route.params]);

  // Validation helper functions
  const isFieldEmpty = field => field.trim() === '';

  // Validation functions for each field
  const validatePassword = () => {
    if (isFieldEmpty(password)) {
      setPasswordError('*Password is required.');
      return false;
    }
    setPasswordError('');
    return true;
  };

  const validateCPassword = () => {
    if (password !== cPassword) {
      setCPasswordError('*Passwords do not match.');
      return false;
    }
    setCPasswordError('');
    return true;
  };
  const validateAllFields = () => {
    const isPasswordValid = validatePassword();
    const isCPasswordValid = validateCPassword();

    return isPasswordValid && isCPasswordValid;
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  return (
    <View style={acrossAllScreens.ScreenBackground}>
      {/* <Text>{route.params.arr}</Text> */}
      <ScrollView
        scrollEnabled={false}
        contentContainerStyle={SignUpStyle.scrollViewContent}>
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={[acrossAllScreens.ScreenBorders]}>
            <View style={SignUpStyle.backButtonPos}>
              <TouchableOpacity
                style={acrossAllScreens.backImageContainer}
                onPress={() => {
                  Toast.show('Password not reset!', Toast.LONG);
                  navigation.reset({
                    index: 0,
                    routes: [
                      {name: 'WelcomeStack', params: {screen: 'LoginScreen'}},
                    ],
                  });
                }}>
                <Image
                  style={acrossAllScreens.backImage}
                  source={require('../../assets/images/back.png')}
                />
              </TouchableOpacity>

              <Text
                style={[
                  acrossAllScreens.ScreenHeaderText,
                  SignUpStyle.headerPos,
                ]}>
                Reset Password
              </Text>
              {/* <View style={{width: 20, height: 20}}></View> */}
            </View>

            <View style={[SignUpStyle.signupContainer]}>
              <Text style={[acrossAllScreens.H2, {marginBottom: 12}]}>
                Please enter your New Password :
              </Text>
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder="Password *"
                    placeholderTextColor="grey"
                    secureTextEntry={true}
                    underlineColorAndroid="transparent"
                    onChangeText={text => setPassword(text)}
                  />
                </View>
                {passwordError ? (
                  <Text style={acrossAllScreens.ErrorText}>
                    {passwordError}
                  </Text>
                ) : null}
              </View>
              <View style={[SignUpStyle.inputWithErrorContainer]}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder="Confirm Password *"
                    placeholderTextColor="grey"
                    secureTextEntry={true}
                    underlineColorAndroid="transparent"
                    onChangeText={text => setCPassword(text)}
                  />
                </View>
                {cPasswordError ? (
                  <Text style={acrossAllScreens.ErrorText}>
                    {cPasswordError}
                  </Text>
                ) : null}
              </View>

              <TouchableHighlight
                style={[SignUpStyle.signupButton, {marginTop: 10}]}
                underlayColor="#DDDDDD"
                onPress={() => {
                  console.log('Password reset pressed');
                  if (validateAllFields()) {
                    const resetPassword = async () => {
                      try {
                        const resultAction = await dispatch(
                          forgotPassword(resetToken, password),
                        );
                        if (forgotPassword.fulfilled.match(resultAction)) {
                          Toast.show('Password is reset!', Toast.LONG);
                          navigation.reset({
                            index: 0,
                            routes: [
                              {
                                name: 'WelcomeStack',
                                params: {screen: 'LoginScreen'},
                              },
                            ],
                          });
                        } else {
                          // Thunk was rejected
                          Toast.show(
                            'Error resetting password! Try Again',
                            Toast.LONG,
                          );
                          console.error(
                            'Update password failed:',
                            resultAction.payload,
                          );
                        }
                      } catch (error) {
                        Toast.show('Unexpected error occurred!', Toast.LONG);
                        console.error('Unexpected error:', error);
                      }
                    };

                    resetPassword();
                  } else console.log('Missing Fields for Password reset');
                }}>
                <Text style={SignUpStyle.signUpText}>Reset Password</Text>
              </TouchableHighlight>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </View>
  );
}

export default PasswordResetScreen;
