import {Platform} from 'react-native';
import {
  check,
  PERMISSIONS,
  PermissionStatus,
  request,
  Permission,
  Rationale,
  checkMultiple,
  requestMultiple,
} from 'react-native-permissions';

type PermissionName = 'camera' | 'microphone' | 'media'; // add more permissions here as needed

type PermissionRequest = {
  name: PermissionName;
  permission: Permission[];
};

const permissionRequests: Record<PermissionName, PermissionRequest> = {
  camera: {
    name: 'camera',
    permission: Platform.select({
      ios: [PERMISSIONS.IOS.CAMERA],
      android: [PERMISSIONS.ANDROID.CAMERA],
    }) as Permission[],
  },
  microphone: {
    name: 'microphone',
    permission: Platform.select({
      ios: [PERMISSIONS.IOS.MICROPHONE],
      android: [PERMISSIONS.ANDROID.RECORD_AUDIO],
    }) as Permission[],
  },
  media: {
    name: 'media',
    permission: Platform.select({
      ios: [
        PERMISSIONS.IOS.PHOTO_LIBRARY,
        PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY,
      ],
      android:
        Number(Platform.Version) >= 33
          ? [
              PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
              PERMISSIONS.ANDROID.READ_MEDIA_VIDEO,
              PERMISSIONS.ANDROID.READ_MEDIA_AUDIO,
            ]
          : [
              PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
              // Note: WRITE_EXTERNAL_STORAGE is not needed for reading media files
              // and is automatically granted when READ_EXTERNAL_STORAGE is granted on older Android versions
            ],
    }) as Permission[],
  },
  // add more permissions here as needed
};

export async function checkPermission(
  permissionName: PermissionName,
): Promise<PermissionStatus | PermissionStatus[]> {
  const permissionRequest = permissionRequests[permissionName];

  if (permissionRequest.permission.length === 1) {
    // Single permission, use check
    const permissionStatus = await check(permissionRequest.permission[0]);
    return permissionStatus;
  } else {
    // Multiple permissions, use checkMultiple
    const permissionStatus = await checkMultiple(permissionRequest.permission);
    const statusValues = Object.values(permissionStatus);

    // Special handling for media permissions on Android < 33
    if (
      permissionName === 'media' &&
      Platform.OS === 'android' &&
      Number(Platform.Version) < 33
    ) {
      // For Android < 33, we only need READ_EXTERNAL_STORAGE to be granted
      // If it's granted, we can access media files
      const readStorageStatus = statusValues[0]; // READ_EXTERNAL_STORAGE is the first (and only) permission
      return [readStorageStatus];
    }

    return statusValues;
  }
}

export async function requestPermission(
  permissionName: PermissionName,
): Promise<PermissionStatus | PermissionStatus[]> {
  const permissionRequest = permissionRequests[permissionName];

  if (permissionRequest.permission.length === 1) {
    // Single permission, use request
    const permissionStatus = await request(permissionRequest.permission[0]);
    return permissionStatus;
  } else {
    // Multiple permissions, use requestMultiple
    const permissionStatus = await requestMultiple(
      permissionRequest.permission,
    );
    const statusValues = Object.values(permissionStatus);

    // Special handling for media permissions on Android < 33
    if (
      permissionName === 'media' &&
      Platform.OS === 'android' &&
      Number(Platform.Version) < 33
    ) {
      // For Android < 33, we only need READ_EXTERNAL_STORAGE to be granted
      const readStorageStatus = statusValues[0]; // READ_EXTERNAL_STORAGE is the first (and only) permission
      return [readStorageStatus];
    }

    return statusValues;
  }
}
