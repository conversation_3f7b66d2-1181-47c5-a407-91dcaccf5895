import React, {useState} from 'react';
import {Text, View, StyleSheet, TouchableOpacity, FlatList} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5'; // Make sure to install this package
import acrossAllScreens from '../../styles/acrossAllScreens';

export function PointsSourceScreen({
  onOptionSelect,
  closeModal,
}) {
  const [optionSettings, setOptionSettings] = useState([
    {
      setting: 'Manual',
      description:
        'Every individual who joins the challenge, will have to enter in their own points while making a post as per the point rules',
      icon: 'edit',
    },
    {
      setting: 'Automated',
      description:
        'One point is assigned to each challenge particpant per post linked to the change',
      icon: 'magic',
    },
  ]);
  const handleSelectedOption = selectOption => {
    // Call the callback function passed from the parent component
    onOptionSelect(selectOption.setting);
    // Close the modal
    closeModal();
  };

  const renderItem = ({item}) => (
    <TouchableOpacity
      style={[styles.itemContainer]}
      onPress={() => handleSelectedOption(item)}>
      <Icon name={item.icon} size={24} color="#000" />
      <View style={styles.textContainer}>
        <Text style={styles.setting}>{item.setting}</Text>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={optionSettings}
      renderItem={renderItem}
      keyExtractor={item => item.setting}
      style={[styles.list]}
    />
  );
}

const styles = StyleSheet.create({
  list: {
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 10,
    flex: 1,
  },
  setting: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    flexWrap: 'wrap',
  },
});

export default PointsSourceScreen;
