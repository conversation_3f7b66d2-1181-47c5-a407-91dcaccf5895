import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet, Dimensions} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import {Post} from '../../redux/Post/PostSlice';


const {width} = Dimensions.get('window');

const PostItem = ({post}: {post: Post}) => (
  <View style={styles.postContainer}>
    {/* Post Header (User profile picture, name, and time) */}
    <View style={styles.postHeader}>
      {/* User Profile Picture */}
      <View style={styles.profilePicContainer}>
        <Image
          source={require('../../static/Images/vader.png')} // Placeholder for user profile picture
          style={styles.profilePic}
        />
      </View>
      {/* User Info (Name and Time Ago) */}
      <View style={styles.userInfoContainer}>
        <Text style={styles.userName}>{'post.userName'}</Text>
        <Text style={styles.timeAgo}>{post.timeAgo}</Text>
      </View>
      {/* Options Icon */}
      <TouchableOpacity>
        <Ionicons name="ellipsis-vertical-outline" size={20} color="black" />
      </TouchableOpacity>
    </View>

    {/* Post Image */}
    <Image source={post.imageSource} style={styles.postImage} />

    {/* Post Title and Like Button (Same Row) */}
    <View style={styles.titleAndLikeRow}>
      <Text style={styles.postTitle}>{post.title}</Text>
      <TouchableOpacity style={styles.likeButton}>
        <Entypo name="thumbs-up" size={30} color="#87CEEB" />
      </TouchableOpacity>
    </View>

    {/* Post Body */}
    <Text style={styles.postBody}>{post.body}</Text>
  </View>
);

const styles = StyleSheet.create({
  postContainer: {
    padding: 15,
    backgroundColor: '#fff',
    marginVertical: 10,
    width: width - 20,
    alignSelf: 'center',
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start', // Align items at the top to bring userInfo closer to the profile picture
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flex: 1, // This helps take up space to align items correctly
  },
  profilePicContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    overflow: 'hidden',
  },
  profilePic: {
    width: '100%',
    height: '100%',
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  timeAgo: {
    fontSize: 14,
    color: 'gray',
    fontFamily: 'Helvetica Neue',
  },
  titleAndLikeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  postTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    flex: 1, // This allows the title to take up the remaining space
    fontFamily: 'Helvetica Neue',
  },
  postImage: {
    width: '100%',
    height: 200,
    marginVertical: 10,
    resizeMode: 'cover',
  },
  postBody: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  likeButton: {
    padding: 10,
  },
});

export default PostItem;
