import React from 'react';
import {Text, View, TouchableOpacity, Image} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {WebView} from 'react-native-webview';
import {privacyHtml} from '../../constants/signup';

export function PrivacyScreen({route, navigation}: any) {
  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <View style={[acrossAllScreens.ScreenBorders]}>
        <View style={SignUpStyle.backButtonPos}>
          <TouchableOpacity
            style={acrossAllScreens.backImageContainer}
            onPress={() => {
              navigation.pop();
            }}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>

          <Text
            style={[acrossAllScreens.ScreenHeaderText, SignUpStyle.headerPos]}>
            Privacy Policy
          </Text>
        </View>

        <WebView
          originWhitelist={['*']}
          source={{html: privacyHtml}}
          style={{flex: 1, marginTop: 20}}
        />
      </View>
    </View>
  );
}

export default PrivacyScreen;
