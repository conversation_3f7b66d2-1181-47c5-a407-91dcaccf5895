import React from 'react';
import {Text, View, Image, TouchableOpacity, StyleSheet} from 'react-native';

export function SearchBox() {
  return (
    <View style={styles.input}>
      <Text style={styles.placeHolderStyle}>Search</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  input: {
    // margin: 15,\
    marginRight: 10,
    marginBottom: 15,
    borderColor: 'black',
    borderWidth: 1,
    borderRadius: 8,
  },
  placeHolderStyle: {
    fontSize: 14.1,
    color: '#ACA9A9',
    marginTop: 13,
    marginBottom: 13,
    marginLeft: 2,
  },
});
export default SearchBox;
