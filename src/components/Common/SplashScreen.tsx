import React, {useEffect} from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import {useSelector} from 'react-redux';
import {useAppDispatch} from '../../redux/Store';
import {refreshToken} from '../../redux/Auth/AuthAction';
import {logout} from '../../redux/User/UserAction';

type SplashScreenProps = {
  navigation: any;
};

const SplashScreen: React.FC<SplashScreenProps> = ({navigation}) => {
  const user = useSelector((state: any) => state.auth.user);
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    dispatch(logout())
      .unwrap()
      .then(() => {
        navigation.reset({
          index: 0,
          routes: [{name: 'WelcomeStack', params: {screen: 'LoginScreen'}}],
        });
      });
  };

  useEffect(() => {
    if (user.userId) {
      dispatch(refreshToken({token: user.refreshToken, id: user.userId}))
        .unwrap()
        .then((data: any) => {
          if (data.code == 200) {
            navigation.reset({
              index: 0,
              routes: [{name: 'BottomTabsNavigator'}],
            });
          } else {
            handleLogout();
          }
        })
        .catch(() => {
          handleLogout();
        });
    } else {
      setTimeout(() => {
        // navigation.replace('WelcomeStack');
        handleLogout();
      }, 3000);
    }
  }, []);

  return (
    <View style={styles.container}>
      <Image
        style={{width: 200, height: 80}}
        resizeMode={'contain'}
        source={require('../../assets/images/napoz.png')}
      />
      <Text style={styles.text}>Welcome to Napoz</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  appName: {
    color: 'black',
    fontSize: 50,
    fontWeight: 'bold',
  },
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    backgroundColor: '#C3E7F5',
  },
  text: {
    fontSize: 18,
    color: '#333333',
    marginTop: 20,
  },
});

export default SplashScreen;
