import React from 'react';
import {Text, View, TouchableOpacity, Image} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {WebView} from 'react-native-webview';
import {eulaHTML} from '../../constants/signup';

export function TermsScreen({route, navigation}: any) {
  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <View style={[acrossAllScreens.ScreenBorders]}>
        <View style={SignUpStyle.backButtonPos}>
          <TouchableOpacity
            style={acrossAllScreens.backImageContainer}
            onPress={() => {
              navigation.pop();
            }}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <Text
            style={[acrossAllScreens.ScreenHeaderText, SignUpStyle.headerPos]}>
            EULA Terms
          </Text>
        </View>

        <WebView
          originWhitelist={['*']}
          source={{html: eulaHTML}}
          style={{flex: 1, marginTop: 20}}
        />
      </View>
    </View>
  );
}

export default TermsScreen;
