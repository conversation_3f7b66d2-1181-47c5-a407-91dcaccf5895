import React, {useState} from 'react';
import {Text, View, StyleSheet, TouchableOpacity, FlatList} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5'; // Make sure to install this package
import acrossAllScreens from '../../styles/acrossAllScreens';

export function VisibilitySettingsScreen({
  route,
  navigation,
  onVisibilitySelect,
  closeModal,
}) {
  const [visibilitySettings, setVisibilitySettings] = useState([
    {
      key: 'public',
      setting: 'Public',
      description:
        'Anyone on app can join your challenge, see and repost your challenge stories',
      icon: 'globe',
    },
    {
      key: 'private',
      setting: 'Private',
      description:
        'Participants can only enter the challenge when invited or accepted by you. Only those who are a part of challenge can see your challenge stories',
      icon: 'user-lock',
    },
    {
      key: 'custom',
      setting: 'Custom',
      description:
        'You decide how participants can join the challenge, who sees your challenge stories and how followers can repost your challenge',
      icon: 'sliders-h',
    },
  ]);
  const handleSelectVisibility = visibilityOption => {
    // Call the callback function passed from the parent component
    onVisibilitySelect(visibilityOption.setting);
    // Close the modal
    closeModal();
  };

  const renderItem = ({item}) => (
    <TouchableOpacity
      style={[styles.itemContainer]}
      onPress={() => handleSelectVisibility(item)}>
      <Icon name={item.icon} size={24} color="#000" />
      <View style={styles.textContainer}>
        <Text style={styles.setting}>{item.setting}</Text>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={visibilitySettings}
      renderItem={renderItem}
      keyExtractor={item => item.key}
      style={[styles.list]}
    />
  );
}

const styles = StyleSheet.create({
  list: {
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 10,
    flex: 1,
  },
  setting: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    flexWrap: 'wrap',
  },
});

export default VisibilitySettingsScreen;
