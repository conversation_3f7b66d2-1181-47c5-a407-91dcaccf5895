import React, {useEffect, useState} from 'react';
import {NavigationContainer, useNavigation} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {StyleSheet, Text, View} from 'react-native';

import LoginScreen from '../Login/LoginScreen';
import InterestScreen from '../Interest/InterestScreen';
import ChallengeCreationScreenStack from '../ChallengeCreation/ChallengeCreationScreenStack';
import SignUpScreen from '../SignUp/SignUpScreen';
import WelcomeScreen from './WelcomeScreen';
import Logout from './Logout';
import ForgotPasswordScreen from './ForgotPassword';
import OtpScreen from './OtpScreen';
import PasswordResetScreen from './PasswordResetScreen';
import HomeFeedScreenStack from '../HomeScreen/HomeFeedScreenStack';
import LandingPage from './LandingPage';
import PrivacyScreen from './PrivacyScreen';
import TermsScreen from './TermsScreen';
import TutorialScreen1 from '../welcomeScreens/TutorialScreen1';
import TutorialScreen2 from '../welcomeScreens/TutorialScreen2';
import TutorialScreen3 from '../welcomeScreens/TutorialScreen3';

const welcomeStack = createStackNavigator();

export function WelcomeStack() {

  return (
    <welcomeStack.Navigator
      initialRouteName="LoginScreen"
      // initialRouteName="LandingPage"
      screenOptions={{
        headerShown: false,
        gestureEnabled: false
      }}>
      <welcomeStack.Screen
        name="WelcomeScreen"
        component={WelcomeScreen}
        options={{title: 'Welcome Screen'}}
      />
      <welcomeStack.Screen name="HomeStack" component={HomeFeedScreenStack} />
      <welcomeStack.Screen
        name="Interest"
        component={InterestScreen}
        options={{title: 'My Interests'}}
      />

      <welcomeStack.Screen
        name="SignUpScreen"
        component={SignUpScreen}
        options={{title: 'Sign Up!'}}
      />
      <welcomeStack.Screen
        name="LoginScreen"
        component={LoginScreen}
        options={{title: 'Login'}}
      />
      <welcomeStack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen}
        options={{title: 'Forgot Password'}}
      />
      <welcomeStack.Screen
        name="OtpScreen"
        component={OtpScreen}
        options={{title: 'OTP'}}
      />
      <welcomeStack.Screen
        name="PasswordReset"
        component={PasswordResetScreen}
        options={{title: 'Password Reset'}}
      />
      <welcomeStack.Screen
        name="LandingPage"
        component={LandingPage}
        options={{title: 'LandingPage'}}
      />
      <welcomeStack.Screen
        name="PrivacyScreen"
        component={PrivacyScreen}
        options={{title: 'Privacy Policy'}}
      />
      <welcomeStack.Screen
        name="TermsScreen"
        component={TermsScreen}
        options={{title: 'Terms & Condition'}}
      />
       <welcomeStack.Screen
        name="TutorialScreen1"
        component={TutorialScreen1}
        options={{title: 'Tutorial Screen 1'}}
      />
       <welcomeStack.Screen
        name="TutorialScreen2"
        component={TutorialScreen2}
        options={{title: 'Tutorial Screen 2'}}
      />
       <welcomeStack.Screen
        name="TutorialScreen3"
        component={TutorialScreen3}
        options={{title: 'Tutorial Screen 3'}}
      />

      {/* <welcomeStack.Screen
        name="Logout"
        component={Logout}
        options={{title: 'Logout'}}
      /> */}
    </welcomeStack.Navigator>
  );
}

export default WelcomeStack;
