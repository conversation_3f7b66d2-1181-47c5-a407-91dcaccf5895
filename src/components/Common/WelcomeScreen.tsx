import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  Image,
  TouchableOpacity,
  Button,
} from 'react-native';
import {SignUpStyle} from '../../styles/SignUp';
import {useDispatch, useSelector} from 'react-redux';

export function WelcomeScreen({route, navigation}: any) {
  // const userProfileState = useSelector(state => state.user);

  // const authState = useSelector(state => state.auth);
  // const challState = useSelector(state => state.challenge);
  console.log('Welcome screen was run');
  // console.log('userWelcome', userProfileState);
  // console.log('authWelcome', authState);
  // console.log('challWelcome', challState);
  return (
    <View style={styles.ScreenStyle}>
      <Text style={styles.WelcomeTextStyle}>Welcome to Napoz!</Text>
      <Image
        style={styles.LogoStyle}
        source={require('../../static/Images/Napoz_Logo.png')}
      />
      <View>
        <TouchableOpacity
          style={[SignUpStyle.buttonContainer, SignUpStyle.signupButton]}
          onPress={() => {
            console.log('Login Screen');
            navigation.push('LoginScreen');
          }}>
          <Text style={SignUpStyle.signUpText}>Login Screen</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[SignUpStyle.buttonContainer, SignUpStyle.signupButton]}
          onPress={() => {
            console.log('Signup Screen');
            navigation.push('SignUpScreen');
          }}>
          <Text style={SignUpStyle.signUpText}>SignUp Screen</Text>
        </TouchableOpacity>
        {/* <Button
          title="HomeStack"
          onPress={() => {
            console.log('Home Screen');
            navigation.push('HomeStack');
          }}
        /> */}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  ScreenStyle: {
    alignItems: 'center',
    backgroundColor: 'white',
    flex: 1,
    justifyContent: 'center',
  },
  ButtonAlignmentStyle: {
    alignItems: 'center',
  },
  LogoStyle: {
    height: 200,
    width: 200,
    marginBottom: 10,
    marginTop: 10,
  },
  WelcomeTextStyle: {
    fontWeight: 'bold',
    fontSize: 30,
  },
});

export default WelcomeScreen;
