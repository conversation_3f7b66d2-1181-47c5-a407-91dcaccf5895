import React from 'react';
import { View, StyleSheet, Switch, Text  } from 'react-native';
import { Card, CardItem, Icon} from 'native-base';
import { TouchableHighlight } from 'react-native-gesture-handler';

type settingModalProps = {
    setting: string,
    description: string,
    icon: string
}

const VisibilitySetting = {
    public: {
        setting: 'Public',
        description:  'Anyone on app can join your challenge, see and repost your challenge stories',
        icon: 'earth',
    },
    private: {
        setting: 'Private',
        description:  'Participants can only enter the challenge when invited or accepted by you. Only those who are a part of challenge can see your challenge stories',
        icon: 'lock-closed',
    },
    custom: {
        setting: 'Custom',
        description:  'You decide how participants can join the challenge, who sees your challenge stories and how followers can repost your challenge',
        icon: 'options',
    },
};

const PointSource = {
    automatic: {
        setting: 'Automatic',
        description:  '<PERSON><PERSON><PERSON> will automatically rank the challenge based on the verified points activated by each user. These points are awareded gy the app or a verified palatform',
        icon: 'earth',
    },
    moderated: {
        setting: 'Moderated',
        description:  'Only you, the admin of the leaderboard can assign points to each challenge. You can assign other challenges to be co-admins as well',
        icon: 'lock-closed',
    },
    manual: {
        setting: 'Manual',
        description:  'All chalenges on the leaderboard enter thier own  points. Challengers can contest another users points with admin in case of an unfair points entry',
        icon: 'options',
    },
};

function RenderOptions (props: settingModalProps ) {
    return (
        <Card style={styles.cardStyle}>
            <TouchableHighlight onPress={() => null}>
                <CardItem >
                    <Icon name={props.icon} type='Ionicons' style={{}} />
                    <View style={{padding:5}}>
                        <Text style={{fontSize:18, fontWeight:'500'}}>{props.setting}</Text>
                        <Text style={{fontSize:12, fontWeight:'300'}}>{props.description}</Text>
                    </View>
                </CardItem>
            </TouchableHighlight>
        </Card>
    );
};

type SettingModalProps = {
    modalSetting: 'Visibility' | 'Source';
};

export function VisibilitySettingsModal (props: SettingModalProps) {
    const data = props.modalSetting === 'Visibility' ? VisibilitySetting : PointSource;
    const header = props.modalSetting === 'Visibility' ? 'Choose visibility settings' : 'Choose leaderboard scoring system';
    
    return (
        <View style={{}}>
            <Text style={{fontSize:18, padding:'3%', alignSelf:'center'}}>{header}</Text>
            {Object.entries(data).map(([key,value]) => {
                return (
                    <RenderOptions 
                        setting={value.setting}
                        description={value.description} 
                        icon={value.icon}
                        key={key} />
                );
            })}
             <TouchableHighlight 
                style={styles.createButtonStyle}
                onPress={() => null}>
                    <Text style={{alignSelf:'center', fontSize:16, fontWeight:'300'}}>Done</Text>
            </TouchableHighlight>
        </View>
    )
};

export default VisibilitySettingsModal;

const styles = StyleSheet.create({
    cardStyle: {
        width:'95%',
        borderColor:'black', 
        alignSelf:'center'
    },
    createButtonStyle: {
        width: '35%',
        height: '28%',
        padding:'3%',
        borderRadius: 10/2,
        justifyContent: 'center',
        alignContent: 'center',
        alignSelf: 'center',
        backgroundColor: 'lightgrey'
    }
})