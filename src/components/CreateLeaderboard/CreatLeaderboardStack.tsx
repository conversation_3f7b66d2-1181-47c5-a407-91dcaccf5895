import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import VisibilitySourceSettingModal from '../CommonModals/VisibilitySourceSettingsModal';
import CreateLeaderboard from './CreateLeaderboard';
import CreateExisitngChallengeLeaderboard from './CreateExistingChallengeLeaderboard';
import { NavigationContainer } from '@react-navigation/native';

const createLeaderboardStack = createStackNavigator();
const settingsModalStack = createStackNavigator();

function SettingsModalStack() {
    return (
        <settingsModalStack.Navigator>
            <settingsModalStack.Screen 
                name='createExistingChallengeLeaderboard'
                component={CreateExisitngChallengeLeaderboard}/>
            <settingsModalStack.Screen 
                name='settingsModal'
                component={VisibilitySourceSettingModal}/>
        </settingsModalStack.Navigator>
    )
}

function CreateLeaderboardStack() {
    return (
        <NavigationContainer>
            <createLeaderboardStack.Navigator>
                <createLeaderboardStack.Screen 
                    name='createLeaderboardHome'
                    component={CreateLeaderboard}/>
                <createLeaderboardStack.Screen 
                    name='createExistingChallengeLeaderboardStack'
                    component={SettingsModalStack}/>
            </createLeaderboardStack.Navigator>
        </NavigationContainer>
    )
}

export {
    SettingsModalStack,
    CreateLeaderboardStack
}