/*import React, {FunctionComponent, ReactElement, useState} from 'react';

import {View, StyleSheet, Switch, Text} from 'react-native';
// import Icon from 'react-native-vector-icons/SimpleLineIcons';
import {TouchableHighlight} from 'react-native-gesture-handler';
import VisibilitySourceSettingModal from '../CommonModals/VisibilitySourceSettingsModal';

type renderCardItemsProps = {
  text: string | Array<string>;
  cardItemStyles: Object;
  textStyles: Object;
};

function RenderTextCardItems(props: renderCardItemsProps): ReactElement {
  return (
    <CardItem style={props.cardItemStyles}>
      <Text style={props.textStyles}>{props.text}</Text>
    </CardItem>
  );
}

type RenderSettingCardItemsType = {
  settingText: string;
  setting: string;
  onPress: () => void;
};

function RenderSettingCardItems(
  props: RenderSettingCardItemsType,
): ReactElement {
  // Need to add Icon
  return (
    <TouchableHighlight style={{}} onPress={props.onPress}>
      <CardItem style={{flexDirection: 'row'}}>
        <Text style={{alignContent: 'flex-start', flex: 1}}>
          {props.settingText}
        </Text>
        <View style={{alignContent: 'flex-end', flexDirection: 'row'}}>
          <Text style={{fontWeight: '800'}}>{props.setting}</Text>
        </View>
      </CardItem>
    </TouchableHighlight>
  );
}

export function CreateExistingChallengeLeaderboard({navigation}) {
  const [isEnabled, setIsEnabled] = useState(true);
  const toggleSwitch = () => setIsEnabled(previousState => !previousState);
  const visibilitySettingModal = () =>
    navigation.navigate('createExistingChallengeLeaderboardStack', {
      screen: 'settingsModal',
    });

  return (
    <View>
      <Card style={styles.cardStyle}>
        <CardItem style={styles.headerContainer}>
          <Icon name="trophy" fontSize={25} type="Ionicons" />
          <Text style={styles.headerStye}>Leaderboard</Text>
        </CardItem>
        <RenderTextCardItems
          text={'Challenge Name'}
          cardItemStyles={styles.headingContainer}
          textStyles={styles.headingStyle}
        />
        <RenderTextCardItems
          text={'Description for challenge'}
          cardItemStyles={styles.headingContainer}
          textStyles={styles.descpStyle}
        />
        <RenderTextCardItems
          text={'Hastags'}
          cardItemStyles={styles.headingContainer}
          textStyles={styles.descpStyle}
        />
      </Card>
      <Card style={styles.cardStyle}>
        <RenderSettingCardItems
          settingText="Visibility settings"
          setting="Private"
          onPress={visibilitySettingModal}
        />
        <RenderSettingCardItems
          settingText="Point Source"
          setting="Modrated"
          onPress={() => null}
        />
        <RenderSettingCardItems
          settingText="Leaderboard Format"
          setting="F/NF"
          onPress={() => null}
        />
        <RenderSettingCardItems
          settingText="Fitten Category"
          setting="Distance"
          onPress={() => null}
        />
        <CardItem style={styles.headingContainer}>
          <Text style={styles.descpStyle}>Share as a post</Text>
          <Switch
            style={styles.switchStyle}
            trackColor={{false: '#767577', true: '#81b0ff'}}
            onValueChange={toggleSwitch}
            value={isEnabled}
          />
        </CardItem>
      </Card>
      <TouchableHighlight style={styles.createButtonStyle} onPress={() => null}>
        <Text style={{alignSelf: 'center'}}>Publish Leaderboard</Text>
      </TouchableHighlight>
    </View>
  );
}

export default CreateExistingChallengeLeaderboard;

const styles = StyleSheet.create({
  cardStyle: {
    width: '98%',
    alignSelf: 'center',
  },
  headerContainer: {
    justifyContent: 'center',
  },
  headerStye: {
    fontSize: 25,
  },
  headingContainer: {
    justifyContent: 'center',
  },
  headingStyle: {
    fontSize: 20,
    fontWeight: '600',
  },
  descpStyle: {
    fontSize: 18,
    alignContent: 'flex-start',
    flex: 1,
    flexDirection: 'column',
  },
  switchStyle: {
    alignSelf: 'flex-end',
    transform: [{scaleX: 0.8}, {scaleY: 0.8}],
  },
  thumbNail: {
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
  },
  createButtonStyle: {
    width: '40%',
    height: '25%',
    borderRadius: 10 / 2,
    justifyContent: 'center',
    alignContent: 'center',
    alignSelf: 'center',
    backgroundColor: 'lightgrey',
  },
});
*/
