/*
import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity } from 'react-native';
import { Card, CardItem, Thumbnail, Body, Left, Right, Button } from 'native-base';
import Icon from 'react-native-vector-icons/SimpleLineIcons';
import { TouchableHighlight } from 'react-native-gesture-handler';

const challengeHomeData = require('../../static/ChallengeContent/ChallengeHomeContent.json');

export function CreateLeaderboard () {

    const [isEnabled, setIsEnabled] = useState(true);
    const toggleSwitch = () => setIsEnabled(previousState => !previousState);

    const currentData = challengeHomeData.CurrentData;
    const pastData = challengeHomeData.PastData;
    

    const challengeData = useMemo(()=>{
        let challenge = currentData.map((current: { title: any; userName: any; userProfilePic: any; }) => {
            return {
                "title" : current.title,
                "userName": current.userName,
                "userProfilePic": current.userProfilePic
            }
        });
        return challenge;
    },[currentData])

    return (
        <View>
          
            <Card style={styles.cardStyle}>
                <CardItem style={styles.headerContainer}>
                    <Icon name='trophy' size={25}/>
                    <Text style={styles.headerStye}>Leaderboard</Text>
                </CardItem>
                <CardItem style={styles.headingContainer}>
                    <Text style={styles.headingStyle}>New Leaderboard</Text>
                </CardItem>
                <CardItem style={styles.headingContainer}>
                    <Text style={styles.descpStyle}>Link to existing challenge</Text>
                    <Switch 
                        style={styles.switchStyle}
                        trackColor={{ false: "#767577", true: "#81b0ff" }}
                        onValueChange={toggleSwitch}
                        value={isEnabled}/>
                </CardItem>
                <CardItem>
                    <Text>Challenges started or signed up by you. If you don't want to find what you're looking for then join a challengein the challenges page.</Text>
                </CardItem>
            </Card>
            {isEnabled && (
                <>
                    <Card>
                        {challengeData.map((data: any, index: any) => {
                            return (
                                <TouchableHighlight onPress={() => null}>
                                    <CardItem>
                                        <Thumbnail 
                                            style={styles.thumbNail}
                                            source={{uri: data.userProfilePic}}/>
                                        <View>
                                            <Text>{data.title}</Text>
                                            <Text>{data.userName}</Text>
                                        </View>
                                    </CardItem>
                                </TouchableHighlight>
                            )
                        })}
                    </Card>
                    <TouchableHighlight 
                        style={styles.createButtonStyle}
                        onPress={() => null}>
                            <Text style={{alignSelf:'center'}}>Create</Text>
                    </TouchableHighlight>
                </>
            )}
            {!isEnabled && (
                <>
                    <Card style={styles.cardStyle}>
                        <Text style={styles.headingStyle}> Create stand alone Leaderboard</Text>
                    </Card>
                    <TouchableHighlight 
                        style={styles.createButtonStyle}
                        onPress={() => null}>
                            <Text style={{alignSelf:'center'}}>Create</Text>
                    </TouchableHighlight>
                </>
            )}
        </View>
    )
};

export default CreateLeaderboard;

const styles = StyleSheet.create({
    cardStyle:{
        width: '98%',
        alignSelf: 'center'
    },
    headerContainer: {
        justifyContent: 'center',
    },
    headerStye: {
        fontSize: 25,
    },
    headingContainer: {
        
    },
    headingStyle: {
        fontSize: 20,
        alignContent: 'flex-start',
        fontWeight: '500'
    },
    descpStyle: {
        fontSize: 18,
        alignContent: 'flex-start',
        flex: 1,
        flexDirection: 'row'
    },
    switchStyle: {
        alignSelf: 'flex-end',
        transform: [{ scaleX: .8 }, { scaleY: .8 }]
    },
    thumbNail: {
        width: 30,
        height: 30,
        borderRadius: 30/2
    },
    createButtonStyle: {
        width: '40%',
        height: '25%',
        borderRadius: 10/2,
        justifyContent: 'center',
        alignContent: 'center',
        alignSelf: 'center',
        backgroundColor: 'lightgrey'
    }
})*/
