import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  DeviceEventEmitter,
} from 'react-native';
import {isVideoLink} from '../../utils/Utils';
import FastImage from 'react-native-fast-image';
import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons'; // Import the icon library
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {muteUnMuteVideo} from '../../redux/Post/PostSlice';
import convertToProxyURL from 'react-native-video-cache';

const ExploreMediaItem = ({item, challengeId, index, appState}: any) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<Video>(null);
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  const dispatch = useAppDispatch();
  const mute = useAppSelector(state => state.post.mute);
  const [isMuted, setIsMuted] = useState(mute);

  useEffect(() => {
    setIsMuted(mute);
  }, [mute]);

  const toggleMute = () => {
    dispatch(muteUnMuteVideo(!isMuted));
  };

  useEffect(() => {
    const listener = DeviceEventEmitter.addListener('videoPlayPause', data => {
      if (
        data.videoRef == item &&
        data.challengeId == challengeId &&
        data.index == index
      ) {
        setIsPlaying(true);
      } else {
        setIsPlaying(false);
      }
    });

    return () => {
      listener.remove();
    };
  }, []);

  const handlePlayPause = () => {
    if (!isPlaying) {
      DeviceEventEmitter.emit('videoPlayPause', {
        videoRef: item,
        challengeId: challengeId,
        index: index,
      });
    } else {
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <View>
      {isVideoLink(item) ? (
        <View style={styles.imageTile}>
          <Video
            source={{
              uri: convertToProxyURL(item),
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }}
            ref={videoRef}
            useTextureView={false}
            ignoreSilentSwitch="ignore"
            bufferConfig={{
              minBufferMs: 2500,
              maxBufferMs: 50000,
              bufferForPlaybackMs: 2500,
              bufferForPlaybackAfterRebufferMs: 2500,
            }}
            muted={isMuted || appState != 'active'}
            key={isPlaying + ''}
            onLoad={() => {
              videoRef.current?.seek(0);
            }}
            onError={error => console.log(error, 'errorerrorerrorerror')}
            style={styles.videoContainer}
            resizeMode="cover"
            repeat
            paused={!isPlaying} // Controls video play/pause
          />
          {isPlaying && (
            <TouchableOpacity
              onPress={toggleMute}
              style={[styles.muteButton, {zIndex: 1000}]}>
              <Ionicons
                name={isMuted ? 'volume-mute' : 'volume-high'} // Use icons for mute/unmute
                size={20}
                color="white"
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={
              isPlaying
                ? [styles.overlay, {backgroundColor: 'transparent'}]
                : styles.overlay
            }
            onPress={handlePlayPause}>
            {!isPlaying && (
              <Icon
                name={
                  isPlaying ? 'pause-circle-outline' : 'play-circle-outline'
                }
                size={50}
                color="white"
              />
            )}
          </TouchableOpacity>
        </View>
      ) : (
        <FastImage
          source={{
            uri: item,
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }}
          style={styles.imageTile}
        />
      )}
    </View>
  );
};

export function ExploreFlatList(props: any) {
  return (
    <View>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(key, index) => key + index}
        data={props.data}
        renderItem={({item, index}) => (
          <ExploreMediaItem
            item={item}
            challengeId={props.challengeId}
            index={index}
            appState={props.appState}
          />
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  muteButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 5,
  },
  videoContainer: {
    height: 130,
    width: 100,
  },
  imageTile: {
    height: 130,
    width: 100,
    marginRight: 10,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Semi-transparent overlay
  },
});

export default ExploreFlatList;
