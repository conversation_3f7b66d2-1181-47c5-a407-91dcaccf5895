import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
  AppState,
  Switch,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons'; // Import the icon library
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {ExploreFlatList} from './ExploreFlatLists';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {
  deleteChallenge,
  getChallengeFeedExplore,
} from '../../redux/Challenge/ChallengeAction';
import {FlashList} from '@shopify/flash-list';
import Popover from 'react-native-popover-view';
import {useSelector} from 'react-redux';
import ExploreSearch from './components/ExploreSearch';
import Toast from 'react-native-toast-message';
import analytics from '@react-native-firebase/analytics';

let bottomLoad = false;
export function ExploreMainScreen({navigation}: any) {
  const [exploreKey, setExploreKey] = useState('');
  const user = useSelector((state: any) => state.auth.user);
  const popoverRefs = useRef<Popover[]>([]);
  const currentUserId = useAppSelector(state => state.auth.userId);

  const challengesExploreFeed = useAppSelector(
    state => state.challenge.challengesExploreFeed.data,
  );
  const nextPageToken = useAppSelector(
    state => state.challenge.challengesExploreFeed.nextPageToken,
  );
  const hasNext = useAppSelector(
    state => state.challenge.challengesExploreFeed.hasNext,
  );
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [appState, setAppState] = useState('active');
  const [isExpired, setIsExpired] = useState(false);
  const [loadingA, setLoadingA] = useState(false);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    dispatch(getChallengeFeedExplore({isActive: !isExpired}));
    analytics().logScreenView({
      screen_name: 'ExploreFeedScreen',
      screen_class: 'ExploreFeedScreen',
    });
  }, []);

  const onExploreFeedEnd = useCallback(() => {
    if (
      challengesExploreFeed.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasNext
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeFeedExplore({
          nextPageToken: nextPageToken,
          isActive: !isExpired,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [
    dispatch,
    challengesExploreFeed,
    nextPageToken,
    loading,
    hasNext,
    isExpired,
  ]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(getChallengeFeedExplore({isActive: !isExpired})).finally(() =>
      setRefreshing(false),
    );
  }, [dispatch, isExpired]);

  const onToggleExpire = useCallback(
    (value: boolean) => {
      setLoadingA(true);
      dispatch(getChallengeFeedExplore({isActive: !value})).finally(() =>
        setLoadingA(false),
      );
    },
    [dispatch],
  );

  const onEditClick = (post: any, index: number) => {
    popoverRefs.current[index]?.requestClose();
    navigation.push('ChallengeUpdate', {
      title: post?.title,
      challengeId: post?.challengeId,
      description: post?.description,
      startDate: post?.startDate,
      endDate: post?.endDate,
      isLeaderboardCreated: post?.isLeaderboardCreated,
      pointsTitle: post?.pointsTitle,
      pointsDescription: post?.pointsDescription,
      isPointsAutomated: post?.isPointsAutomated,
      isPointsAscending: post?.isPointsAscending,
      difficultyRating: post?.difficultyRating,
      visibility: post?.visibility,
      mediaRefs: post?.mediaRefs,
      loggedInUser: user.userId,
    });
  };

  const onDeleteClick = (post: any, index: number) => {
    popoverRefs.current[index]?.requestClose();
    Alert.alert(
      'Delete Challenge',
      'Are you sure you want to delete this challenge?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            const result = await dispatch(deleteChallenge(post?.challengeId));
            if (result.payload.code == 200) {
            } else {
              Toast.show({
                type: 'error',
                text1: 'Failed to delete challenge.',
                text2: 'Something went wrong.',
              });
            }
          },
          style: 'destructive',
        },
      ],
    );
  };

  const onReportClick = (index: number) => {
    popoverRefs.current[index]?.requestClose();
  };

  const onChallnegePress = (item: any) => {
    if (item.challengeId) {
      navigation.navigate('ChallengeDetails', {challengeId: item.challengeId});
    }
  };

  const onUserItemClick = (userId: any) => {
    if (currentUserId == userId) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('OtherUserProfileScreen', {userId: userId});
    }
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <View style={styles.headerRow}>
        {/* Custom Search Bar with Search Icon */}
        <View style={styles.searchBarContainer}>
          <Ionicons
            name="search-outline"
            size={20}
            color="gray"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            value={exploreKey}
            onChangeText={setExploreKey}
            placeholderTextColor="gray"
          />
        </View>
      </View>
      {exploreKey ? (
        <ExploreSearch navigation={navigation} searchText={exploreKey} />
      ) : (
        <View
          style={[
            acrossAllScreens.ScreenBorders,
            styles.noMarging,
            {
              paddingBottom: 30,
            },
          ]}>
          <View style={styles.toggleContainer}>
            <Text style={styles.toggleTitle}>Show expired</Text>
            {/* <Switch
              value={isExpired}
              onValueChange={(value: boolean) => {
                setIsExpired(value);
                onToggleExpire(value);
              }}
              thumbColor={isExpired ? '#FFFFFF' : '#C3E7F5'}
              trackColor={{false: '#CDCDCD', true: '#C3E7F5'}}
            /> */}
            <TouchableOpacity
              onPress={() => {
                const newValue = !isExpired;
                setIsExpired(newValue);
                onToggleExpire(newValue);
              }}>
              <FontAwesome
                name={isExpired ? 'toggle-on' : 'toggle-off'}
                size={30}
                color="#c3e7f5"
              />
            </TouchableOpacity>
          </View>
          <FlashList
            estimatedItemSize={200}
            contentContainerStyle={{paddingBottom: 40}}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item: any, index: number) => {
              return `${item.challengeId || 'item'}-${index}`;
            }}
            data={challengesExploreFeed}
            renderItem={({item, index}: any) => {
              const isOwnPost = item.user?.userId == user.userId;
              return (
                <View style={styles.challengeItem}>
                  <View style={styles.chalengeItemHeader}>
                    <TouchableOpacity onPress={() => onChallnegePress(item)}>
                      <Text style={styles.catTitleStyle}>
                        {item.title || item.description}
                      </Text>
                    </TouchableOpacity>
                    {!isOwnPost && (
                      <Popover
                        ref={ref => (popoverRefs.current[index] = ref!)}
                        from={
                          <TouchableOpacity>
                            <Image
                              source={require('../../assets/images/more.png')}
                              resizeMode="contain"
                              style={styles.moreIcon}
                            />
                          </TouchableOpacity>
                        }
                        popoverStyle={styles.popoverStyle}
                        backgroundStyle={{opacity: 0}}>
                        {isOwnPost ? (
                          <>
                            <TouchableOpacity
                              style={styles.optionItem}
                              onPress={() => onEditClick(item, index)}>
                              <Text>Edit</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={styles.optionItem}
                              onPress={() => onDeleteClick(item, index)}>
                              <Text>Delete</Text>
                            </TouchableOpacity>
                          </>
                        ) : (
                          <TouchableOpacity
                            style={styles.optionItem}
                            onPress={() => onReportClick(index)}>
                            <Text>Report</Text>
                          </TouchableOpacity>
                        )}
                      </Popover>
                    )}
                  </View>
                  <ExploreFlatList
                    data={
                      item.mediaRefs &&
                      item.linkedPosts &&
                      item.linkedPosts.length > 0
                        ? [
                            ...item.mediaRefs,
                            ...item.linkedPosts.flatMap(
                              (post: any) => post.mediaRefs,
                            ),
                          ]
                        : item.mediaRefs
                    }
                    challengeId={item.challengeId}
                    appState={appState}
                  />
                  <TouchableOpacity
                    onPress={() => onUserItemClick(item.user?.userId)}>
                    <Text style={[styles.userCountStyle, {marginTop: 9}]}>
                      By{' '}
                      <Text
                        style={
                          styles.userName
                        }>{`${item.user?.firstName} ${item.user?.lastName}`}</Text>
                    </Text>
                  </TouchableOpacity>
                  <Text style={styles.userCountStyle}>
                    {item.participantCount} participants
                  </Text>
                </View>
              );
            }}
            onEndReachedThreshold={0.1}
            onEndReached={onExploreFeedEnd}
            ListFooterComponent={renderFooter}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        </View>
      )}
      {loadingA && (
        <View style={styles.loaderAbsolute}>
          <ActivityIndicator size="large" color="#87CEEB" />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  loaderAbsolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  toggleTitle: {
    fontSize: 14,
    fontWeight: '300',
    marginEnd: 4,
    color: '#000000',
  },
  noMarging: {
    marginTop: 0,
    marginBottom: 0,
  },
  moreIcon: {
    width: 18,
    height: 18,
  },
  optionItem: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    minWidth:40,
  },
  popoverStyle: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
    paddingVertical: 5,
  },
  chalengeItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
    marginBottom: 8,
  },
  challengeItem: {
    marginBottom: 16,
  },
  userName: {
    fontWeight: '700',
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginBottom: 10,
    marginTop: Platform.OS == 'android' ? 10 : 0,
  },
  HeaderStyle: {
    fontSize: 36,
    fontFamily: 'Helvetica Neue',
  },
  searchBarContainer: {
    flex: 1,
    marginLeft: 10,
    flexDirection: 'row', // Ensure the icon and input are in the same row
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderColor: '#ccc',
    borderWidth: 1,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 5,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 14,
    fontFamily: 'Helvetica Neue',
    fontWeight: '300',
    lineHeight: 17,
    color: '#000000',
  },
  mainCatStyle: {
    borderWidth: 1.5,
    borderColor: 'black',
    marginRight: 10,
    marginTop: 5,
    marginBottom: 5,
    borderRadius: 15,
    paddingVertical: 6,
    paddingHorizontal: 12, // Adjust padding to fit the text better
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'white', // Default background color
  },
  selectedMainCatStyle: {
    backgroundColor: '#C3E7F5', // Background color for the subcategory tag
    paddingHorizontal: 10, // Adjust horizontal padding for better fit
    paddingVertical: 4, // Adjust vertical padding for better fit
    borderRadius: 15, // Same border radius as mainCatStyle
    fontSize: 16, // Adjust font size to ensure readability
    marginRight: 10,
    marginTop: 5,
    marginBottom: 5,
    textAlign: 'center',
    fontFamily: 'Helvetica Neue',
    overflow: 'hidden',
  },
  subCatStyle: {
    backgroundColor: '#C3E7F5', // Background color for the subcategory tag
    paddingHorizontal: 10, // Adjust horizontal padding for better fit
    paddingVertical: 4, // Adjust vertical padding for better fit
    borderRadius: 15, // Same border radius as mainCatStyle
    fontSize: 14, // Adjust font size to ensure readability
    marginRight: 10,
    marginTop: 5,
    marginBottom: 5,
    textAlign: 'center',
    fontFamily: 'Helvetica Neue',
    overflow: 'hidden', // Ensure text doesn't overflow out of rounded corners
  },
  viewTagStyle: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  catTitleStyle: {
    fontSize: 20,
    lineHeight: 25,
    fontFamily: 'Helvetica Neue',
    color: '#000000',
    fontWeight: '500',
  },
  userCountStyle: {
    fontSize: 14,
    fontWeight: '300',
    lineHeight: 16,
    fontFamily: 'Helvetica Neue',
    color: '#000000',
  },
});

export default ExploreMainScreen;
