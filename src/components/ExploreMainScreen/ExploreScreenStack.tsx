import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {ExploreMainScreen} from './ExploreMainScreen';
import {ExploreResultsScreen} from '../ExploreResultsScreen/ExploreResultsScreen';
import ChallengeDetailsScreen from '../ChallengeDetailsScreen/ChallengeDetailsScreen';
import ChallengeUpdateScreen from '../ChallengeHomeScreen/ChallengeUpdateScreen';
import CalendarScreen from '../Common/CalendarScreen';
import UserProfileScreen from '../UserProfileScreen/UserProfileScreen';
import UserPostsScreen from '../UserProfileScreen/UserPostsScreen';
import {TouchableOpacity} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import UserActivity from '../UserProfileScreen/UserActivity';
import ChallengeParticipant from '../ChallengeDetailsScreen/ChallengeParticipant';
import LeaderboardDetailsScreen from '../LeaderboardDetailsScreen/LeaderboardDetailsScreen';
import UserConnectionsScreen from '../UserProfileScreen/UserConnectionsScreen';

const exploreStack = createStackNavigator();

export function ExploreScreenStack() {
  return (
    <exploreStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false
      }}>
      <exploreStack.Screen
        name="ExploreMain"
        component={ExploreMainScreen}
        options={{title: 'Explore'}}
      />
      <exploreStack.Screen
        name="ExploreResults"
        component={ExploreResultsScreen}
        options={{title: 'Explore', headerLeft: () => null}}
      />
      <exploreStack.Screen
        name="ChallengeDetails"
        component={ChallengeDetailsScreen}
        options={({route}) => ({title: 'Challenge', header: () => null})}
      />
      <exploreStack.Screen
        name="ChallengeParticipant"
        component={ChallengeParticipant}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <exploreStack.Screen
        name="ChallengeUpdate"
        component={ChallengeUpdateScreen}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <exploreStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{headerShown: false}}
      />
      <exploreStack.Screen
        name="OtherUserProfileScreen"
        component={UserProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <exploreStack.Screen
        name="UserConnectionsScreen"
        component={UserConnectionsScreen}
        options={({navigation}) => ({
          title: 'Connections',
          header: () => null,
        })}
      />
      <exploreStack.Screen
        name="UserActivity"
        component={UserActivity}
        options={{
          headerShown: false,
        }}
      />
      <exploreStack.Screen
        name="UserPostsScreen"
        component={UserPostsScreen}
        options={() => ({
          title: 'Posts',
          header: () => null,
        })}
      />
      <exploreStack.Screen
        name="LeaderboardDetailsScreen"
        component={LeaderboardDetailsScreen}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />
    </exploreStack.Navigator>
  );
}
export default ExploreScreenStack;
