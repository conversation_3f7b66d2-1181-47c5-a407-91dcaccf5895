import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {MaterialTabBar, Tabs} from 'react-native-collapsible-tab-view';
import {useAppDispatch, useAppSelector} from '../../../redux/Store';
import {searchUser} from '../../../redux/User/UserAction';
import {FlatList} from 'react-native';
import {Image} from 'react-native';
import {searchChallenge} from '../../../redux/Challenge/ChallengeAction';
import Video from 'react-native-video';
import FastImage from 'react-native-fast-image';
import {baseUrl, isVideoLink} from '../../../utils/Utils';
import {searchLeaderBoard} from '../../../redux/LeaderBoard/LeaderBoardAction';

interface ExploreSearchProps {
  searchText: string;
  navigation: any;
}

export interface User {
  bio?: string;
  createdAt: string;
  userId: string;
  dateOfBirth: string;
  email: string;
  firstName: string;
  gender: string;
  id: string;
  imageReference?: string;
  isActive: boolean;
  lastName: string;
  password: any;
  phoneNumber: string;
  updatedAt: any;
}

export interface Challenge {
  challengeId: any;
  title: string;
  description: string;
  userId: string;
  location: any;
  tags: any;
  mediaRefs: string[];
  visibility: string;
  difficultyRating: string;
  postCount: any;
  participantCount: number;
  views: any;
  likes: any;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
  isLeaderboardCreated: boolean;
  pointsTitle: any;
  pointsDescription: any;
  isPointsAscending: any;
  isPointsAutomated: any;
}

const screenWidth = Dimensions.get('window').width;
const ExploreSearch: React.FC<ExploreSearchProps> = ({
  searchText,
  navigation,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const dispatch = useAppDispatch();
  const [userList, setUserList] = useState<User[]>([]);
  const [challengeList, setChallengeList] = useState<Challenge[]>([]);
  const [leaderBoardList, setLeaderBoardList] = useState<Challenge[]>([]);
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  const currentUserId = useAppSelector(state => state.auth.userId);

  const searchUserList = async (searchText: string) => {
    const result = await dispatch(searchUser({firstName: searchText}));
    if (searchUser.fulfilled.match(result)) {
      if (Array.isArray(result.payload)) {
        setUserList(result.payload);
      }
    }
  };

  const searchChallengeList = async (searchText: string) => {
    const result = await dispatch(
      searchChallenge({challengeTitle: searchText}),
    );
    if (searchChallenge.fulfilled.match(result)) {
      if (Array.isArray(result.payload)) {
        setChallengeList(result.payload);
      }
    }
  };

  const searchLeaderBoardList = async (searchText: string) => {
    const result = await dispatch(
      searchLeaderBoard({challengeTitle: searchText}),
    );
    if (searchLeaderBoard.fulfilled.match(result)) {
      if (Array.isArray(result.payload)) {
        setLeaderBoardList(result.payload);
      }
    }
  };

  useEffect(() => {
    if (searchText) {
      searchUserList(searchText);
      searchChallengeList(searchText);
      searchLeaderBoardList(searchText);
    } else {
      setUserList([]);
      setLeaderBoardList([]);
      setChallengeList([]);
    }
  }, [searchText]);

  const onChallengeClick = (challenge: Challenge, isLeaderBoard: boolean) => {
    if (isLeaderBoard) {
      navigation.navigate('LeaderboardDetailsScreen', {
        name: challenge?.pointsTitle,
        creator: challenge?.userId,
        challengeId: challenge.challengeId,
      });
    } else {
      navigation.navigate('ChallengeDetails', {
        challengeId: challenge.challengeId,
      });
    }
  };

  const renderUserItem = ({item}: {item: User; index: number}) => {
    return (
      <TouchableOpacity
        style={styles.userItemContainer}
        onPress={() => {
          if (currentUserId == item.userId) {
            navigation.navigate('Profile');
          } else {
            navigation.navigate('OtherUserProfileScreen', {
              userId: item.userId,
            });
          }
        }}>
        <FastImage
          source={
            item.imageReference && item.imageReference !== 'imageReference'
              ? {
                  uri: item?.imageReference,
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                }
              : require('../../../static/Images/user.png')
          }
          style={styles.userImg}
        />
        <View style={styles.userInfoContainer}>
          <Text
            style={
              styles.userNameTxt
            }>{`${item.firstName} ${item.lastName}`}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderChallengeItem = (
    {item}: {item: Challenge; index: number},
    isLeaderBoard: boolean,
  ) => {
    return (
      <View style={styles.challngeItem}>
        <Text style={styles.challengeTitle}>{item.title}</Text>
        {item.mediaRefs && (
          <FlatList
            data={item.mediaRefs}
            horizontal
            showsHorizontalScrollIndicator={false}
            pagingEnabled
            nestedScrollEnabled
            scrollEventThrottle={16}
            keyExtractor={(img, idx) => idx.toString()}
            renderItem={({item: imageSrc, index}) => (
              <TouchableOpacity
                onPress={() => onChallengeClick(item, isLeaderBoard)}
                activeOpacity={1}
                style={styles.imageContainer}>
                {isVideoLink(imageSrc) ? (
                  <Video
                    source={{
                      uri: imageSrc,
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }}
                    repeat
                    paused={true}
                    useTextureView={false} 
                    resizeMode={'cover'}
                    style={styles.imageContainer}
                    bufferConfig={{
                      minBufferMs: 2500,
                      maxBufferMs: 50000,
                      bufferForPlaybackMs: 2500,
                      bufferForPlaybackAfterRebufferMs: 2500,
                    }}
                  />
                ) : (
                  <FastImage
                    source={{
                      uri: imageSrc,
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }}
                    resizeMode="cover"
                    style={styles.imageContainer}
                  />
                )}
              </TouchableOpacity>
            )}
          />
        )}
        {/* <Text style={styles.userNameTxt}>{`${item?.user?.firstName} ${item?.user?.lastName}`}</Text> */}
        <Text style={styles.userIdTxt}>
          {item.participantCount} participants
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Tabs.Container
        // @ts-ignore
        renderHeader={null}
        renderTabBar={(props: any) => (
          <MaterialTabBar {...props} indicatorStyle={{height: 0}} />
        )}
        onIndexChange={index => setActiveIndex(index)}
        headerContainerStyle={{shadowColor: 'transparent'}}
        pagerProps={{
          scrollEnabled: false,
        }}>
        <Tabs.Tab
          name="User"
          label={() => (
            <Text
              style={[
                styles.lableStyle,
                activeIndex == 0 && {fontWeight: 'bold'},
              ]}>
              Users
            </Text>
          )}>
          <FlatList
            data={userList}
            ListHeaderComponent={() => <View style={styles.hideUI} />}
            contentContainerStyle={styles.listContainer}
            bounces={false}
            renderItem={renderUserItem}
            keyExtractor={(item: User, index: number) => {
              return `${item.id || 'item'}-${index}`;
            }}
          />
        </Tabs.Tab>
        <Tabs.Tab
          name="Challenges"
          label={() => (
            <Text
              style={[
                styles.lableStyle,
                activeIndex == 1 && {fontWeight: 'bold'},
              ]}>
              Challenges
            </Text>
          )}>
          <FlatList
            data={challengeList}
            ListHeaderComponent={() => <View style={styles.hideUI} />}
            contentContainerStyle={styles.listContainer}
            bounces={false}
            renderItem={item => renderChallengeItem(item, false)}
            keyExtractor={(item: Challenge, index: number) => {
              return `${item.challengeId || 'item'}-${index}`;
            }}
          />
        </Tabs.Tab>
        <Tabs.Tab
          name="Leaderboards"
          label={() => (
            <Text
              style={[
                styles.lableStyle,
                activeIndex == 2 && {fontWeight: 'bold'},
              ]}>
              Leaderboards
            </Text>
          )}>
          <FlatList
            data={leaderBoardList}
            ListHeaderComponent={() => <View style={styles.hideUI} />}
            contentContainerStyle={styles.listContainer}
            bounces={false}
            renderItem={item => renderChallengeItem(item, true)}
            keyExtractor={(item: Challenge, index: number) => {
              return `${item.challengeId || 'item'}-${index}`;
            }}
          />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
};

const styles = StyleSheet.create({
  challngeItem: {
    marginBottom: 15,
  },
  challengeTitle: {
    fontSize: 20,
    lineHeight: 25,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
    marginBottom: 5,
  },
  imageContainer: {
    width: screenWidth - 32,
    height: 170,
    marginBottom: 8,
  },
  userIdTxt: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  userNameTxt: {
    fontSize: 16,
    lineHeight: 20,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  userInfoContainer: {
    marginLeft: 10,
  },
  userImg: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  userItemContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  hideUI: {
    height: 50,
    borderBottomWidth: 0.5,
    borderBottomColor: '#323232',
    marginBottom: 15,
  },
  container: {
    flex: 1,
  },
  lableStyle: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
});

export default ExploreSearch;
