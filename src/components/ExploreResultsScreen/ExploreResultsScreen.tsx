// import {Thumbnail} from 'native-base';
import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  FlatList,
  TouchableOpacity,
  TouchableHighlight,
} from 'react-native';

const resultsFilter = [
  {cat: 'All'},
  {cat: 'Accounts'},
  {cat: 'Challenges'},
  {cat: 'Tags'},
  {cat: 'Leaderboards'},
];
export function ExploreResultsScreen({navigation}) {
  const resultsData = require('../../static/ExploreScreenStatic/ExploreResults.json');
  const [exploreKey, setExploreKey] = useState('');
  return (
    <View style={styles.mainViewStyle}>
      <Text style={styles.HeaderStyle}>Explore </Text>
      <View style={{flexDirection: 'row'}}>
        <TextInput
          style={[styles.input]}
          autoCapitalize="none"
          autoCorrect={false}
          value={exploreKey}
          onChangeText={newtext => setExploreKey(newtext)}
          placeholder="Search"
          placeholderTextColor="grey"
          underlineColorAndroid="transparent"
          returnKeyType="search"
          autoFocus={true}
        />
        <TouchableOpacity
          onPress={() => {
            navigation.push('ExploreMain');
          }}>
          <Text style={{marginTop: 13, marginRight: 5, fontSize: 16}}>
            Cancel
          </Text>
        </TouchableOpacity>
      </View>
      <FlatList
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        keyExtractor={key => key.cat}
        data={resultsFilter}
        renderItem={({item}) => {
          return (
            <View>
              <TouchableOpacity
                onPress={() => {
                  console.log(item.cat);
                }}>
                <Text style={[styles.mainCatStyle]}>{item.cat}</Text>
              </TouchableOpacity>
            </View>
          );
        }}
      />
      {exploreKey ? (
        <FlatList
          // showsVerticalScrollIndicator={false}
          keyExtractor={key => key.title}
          data={resultsData.Data}
          renderItem={({item}) => {
            return (
              <View style={[styles.resultsViewStyle, {alignItems: 'center'}]}>
                {/* <Thumbnail
                  source={require('../../static/Images/mountain.jpg')}
                /> */}
                <View style={styles.resultsTextStyle}>
                  {item.challengeName ? (
                    <Text> {item.challengeName}</Text>
                  ) : null}
                  {item.userName ? <Text> {item.userName}</Text> : null}
                  {item.firstName ? (
                    <Text>
                      {' '}
                      {item.firstName} {item.lastName}
                    </Text>
                  ) : null}

                  {item.hashTag ? <Text> {item.hashTag}</Text> : null}
                </View>
              </View>
            );
          }}
        />
      ) : null}
    </View>
  );
}
const styles = StyleSheet.create({
  mainViewStyle: {
    marginLeft: 10,
    // marginTop: 25,
    // marginRight: 10,
  },
  HeaderStyle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginStart: 10,
    marginBottom: 10,
    marginTop: 5,
  },
  input: {
    // margin: 15,\
    marginRight: 10,
    marginBottom: 15,
    // marginLeft: 10,
    borderColor: 'black',
    borderWidth: 1,
    borderRadius: 8,
    flex: 1,
    color: 'black'
  },
  mainCatStyle: {
    borderWidth: 1.5,
    borderColor: 'black',
    marginRight: 10,
    marginTop: 5,
    marginBottom: 5,
    borderRadius: 15,
    padding: 6,
    fontSize: 16,
  },
  resultsViewStyle: {
    marginTop: 10,
    marginBottom: 15,
    flexDirection: 'row',
  },
  resultsTextStyle: {
    marginLeft: 10,
  },
});
export default ExploreResultsScreen;
