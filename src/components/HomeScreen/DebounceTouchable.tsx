import React, {useState} from 'react';
import {TouchableOpacity, ViewStyle} from 'react-native';

interface Props {
  onPress: () => void;
  delay?: number;
  children: React.ReactNode;
  style?: ViewStyle;
}

const DebounceTouchable = ({onPress, delay = 500, children, style}: Props) => {
  const [isPressing, setIsPressing] = useState(false);

  const handlePress = () => {
    if (!isPressing) {
      setIsPressing(true);
      onPress(); // Fire immediately

      setTimeout(() => {
        setIsPressing(false);
      }, delay);
    }
  };

  return (
    <TouchableOpacity onPress={handlePress} disabled={isPressing} style={style}>
      {children}
    </TouchableOpacity>
  );
};

export default DebounceTouchable;
