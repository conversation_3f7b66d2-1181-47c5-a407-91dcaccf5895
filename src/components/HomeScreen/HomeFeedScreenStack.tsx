import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import HomeFeed from './HomeFeedScreen';
import Story from './Story';
import ChallengeDetailsScreen from '../ChallengeDetailsScreen/ChallengeDetailsScreen';
import ChallengeUpdateScreen from '../ChallengeHomeScreen/ChallengeUpdateScreen';
import CalendarScreen from '../Common/CalendarScreen';
import UserProfileScreen from '../UserProfileScreen/UserProfileScreen';
import UserPostsScreen from '../UserProfileScreen/UserPostsScreen';
import {TouchableOpacity} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import UserActivity from '../UserProfileScreen/UserActivity';
import ChallengeParticipant from '../ChallengeDetailsScreen/ChallengeParticipant';
import LeaderboardDetailsScreen from '../LeaderboardDetailsScreen/LeaderboardDetailsScreen';
import UserConnectionsScreen from '../UserProfileScreen/UserConnectionsScreen';
import PostLikeUser from '../Posts/PostLikeUser';
import ChallengeLikeUser from '../ChallengeHomeScreen/ChallengeLikeUser';
import TagScreen from '../Interest/TagScreen';

const homeStack = createStackNavigator();

export function HomeFeedScreenStack() {
  return (
    <homeStack.Navigator initialRouteName="HomeFeed">
      <homeStack.Screen
        name="HomeFeed"
        component={HomeFeed}
        options={{
          headerShown: false, // Hides the header only for HomeFeed
        }}
      />
      <homeStack.Screen
        name="StoriesScreen"
        component={Story}
        options={{title: 'Story'}} // Show header for StoriesScreen
      />
      <homeStack.Screen
        name="ChallengeDetails"
        component={ChallengeDetailsScreen}
        options={({route}) => ({title: 'Challenge', header: () => null})}
      />
      <homeStack.Screen
        name="PostLikeUser"
        component={PostLikeUser}
        options={({route}) => ({title: 'PostLikeUser', header: () => null})}
      />
      <homeStack.Screen
        name="ChallengeLikeUser"
        component={ChallengeLikeUser}
        options={({route}) => ({title: 'ChallengeLikeUser', header: () => null})}
      />
      <homeStack.Screen
        name="ChallengeParticipant"
        component={ChallengeParticipant}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <homeStack.Screen
        name="ChallengeUpdate"
        component={ChallengeUpdateScreen}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <homeStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{headerShown: false}}
      />
      <homeStack.Screen
        name="OtherUserProfileScreen"
        component={UserProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <homeStack.Screen
        name="UserConnectionsScreen"
        component={UserConnectionsScreen}
        options={({navigation}) => ({
          title: 'Connections',
          header: () => null,
        })}
      />
      <homeStack.Screen
        name="UserActivity"
        component={UserActivity}
        options={{
          headerShown: false,
        }}
      />
      <homeStack.Screen
        name="UserPostsScreen"
        component={UserPostsScreen}
        options={() => ({
          title: 'Posts',
          header: () => null,
        })}
      />

      <homeStack.Screen
        name="LeaderboardDetailsScreen"
        component={LeaderboardDetailsScreen}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />
      <homeStack.Screen
        name="TagScreen"
        component={TagScreen}
        options={{headerShown: false}}
      />
    </homeStack.Navigator>
  );
}

export default HomeFeedScreenStack;
