import React, {useState} from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  Text,
  FlatList,
} from 'react-native';

import FonteAwesome from 'react-native-vector-icons/FontAwesome';

export function HomeFeedStories(props: any) {
  //   const [lastClicked, setLastClicked] = useState(null);
  const [clickedItems, setClickedItems] = useState([]);

  const handleStoryPress = item => {
    console.log(item.userName);
    props.navigation.navigate('HomeStack', {
      screen: 'StoriesScreen',
      //   params: {name: item.userName, parts: item.storyIdArr},
      params: {
        name: item.userName,
        pages: [
          'First 1 page',
          'Second 2 page',
          'Third 3 page',
          'Fourth 4 page',
        ],
      },
    });

    // Add the clicked item to the clickedItems state
    if (!clickedItems.includes(item)) {
      setClickedItems(prevItems => [...prevItems, item]);
    }
  };

  return (
    <View>
      {/* <View
        style={{
          flexDirection: 'row',
          // alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <Text style={styles.HeaderText}>Napoz!</Text>
        <View
          style={{
            alignItems: 'flex-end',
            // justifyContent: 'space-between',
          }}>
          <TouchableOpacity>
            <View
              style={
                props.userStory
                  ? styles.storyProfilePicCircleLarge
                  : styles.storyProfilePicCircleSmall
              }>
              <Image
                source={require('../../static/Images/vader.png')}
                style={styles.profilePic}
              />
            </View>

            <FonteAwesome
              name="plus-circle"
              size={15}
              color="black"
              style={styles.PlusButton}
            />
          </TouchableOpacity>
          <Text>{props.name}</Text>
        </View>
      </View> */}
      <View
        style={{
          flexDirection: 'row',
          // alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View
          style={
            props.userStory
              ? styles.storyProfilePicCircleLarge
              : styles.storyProfilePicCircleSmall
          }>
          <Image
            source={require('../../static/Images/vader.png')}
            style={styles.profilePic}
          />
        </View>
        <FlatList
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          keyExtractor={key => key.add}
          //   data={props.storyFeedData.CurrentData}
          data={[
            ...props.storyFeedData.CurrentData.filter(
              story => !clickedItems.includes(story),
            ),
            ...clickedItems,
          ]}
          renderItem={({item}) => {
            return (
              <View>
                <TouchableOpacity
                  //   onPress={() => {
                  //     console.log(item.userName);
                  //     props.navigation.navigate('HomeStack', {
                  //       screen: 'StoriesScreen',
                  //       params: {name: item.userName},
                  //     });
                  //   }}
                  onPress={() => handleStoryPress(item)}
                  style={{marginRight: 7}}>
                  <View style={styles.storyProfilePicCircleLarge}>
                    <Image
                      source={{uri: item.userProfilePic}}
                      style={styles.profilePic}
                    />
                  </View>
                </TouchableOpacity>
                <Text>{item.userName}</Text>
              </View>
            );
          }}
        />
      </View>
    </View>
  );
}

export default HomeFeedStories;

const styles = StyleSheet.create({
  storyDisplayPic: {
    width: 50,
    height: 50,
    borderRadius: 50 / 2,
    margin: 4,
  },
  profilePic: {width: '100%', height: '100%'},
  HeaderText: {fontSize: 34, fontWeight: 'bold', marginBottom: 5},
  storyProfilePicCircleSmall: {
    width: 50,
    height: 50,
    borderRadius: 25,
    // marginRight: 10,
    justifyContent: 'center', // To center contents vertically
    alignItems: 'center', // To center contents horizontally
    position: 'relative', // Makes sure the children with absolute positioning use this view as a reference
    overflow: 'hidden',
  },
  storyProfilePicCircleLarge: {
    width: 54,
    height: 54,
    borderRadius: 29,
    // marginRight: 10,
    justifyContent: 'center', // To center contents vertically
    alignItems: 'center', // To center contents horizontally
    position: 'relative', // Makes sure the children with absolute positioning use this view as a reference
    overflow: 'hidden',
    borderWidth: 4, // This adds the circular border ring
    borderColor: 'orange', // Change to desired ring color
  },
  PlusButton: {
    position: 'absolute', // This makes the icon overlay on top of the Image
    bottom: 0.5, // Adjust this value as needed
    right: 0.5, // Adjust this value as needed},
    backgroundColor: 'white',
    borderRadius: 55,
  },
});
