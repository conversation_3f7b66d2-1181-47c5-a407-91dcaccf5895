// import React, {useState, useEffect, useRef} from 'react';
// import {View, Pressable, Text, Dimensions, Image} from 'react-native';
// import PagerView from 'react-native-pager-view';

// const screenWidth = Dimensions.get('window').width;

// const ProgressBar = ({duration, isActive, onProgressComplete}) => {
//   const [progress, setProgress] = useState(0);

//   useEffect(() => {
//     let interval;
//     if (isActive) {
//       setProgress(0);
//       interval = setInterval(() => {
//         setProgress(prev => {
//           if (prev < 100) {
//             return prev + 1;
//           } else {
//             clearInterval(interval);
//             onProgressComplete();
//             return prev;
//           }
//         });
//       }, duration / 100);
//     }
//     return () => clearInterval(interval);
//   }, [isActive, duration, onProgressComplete]);

//   return (
//     <View style={{flex: 1, backgroundColor: 'red', margin: 2, marginRight: 5}}>
//       <View
//         style={{
//           width: `${progress}%`,
//           height: '100%',
//           backgroundColor: 'gray',
//         }}
//       />
//     </View>
//   );
// };

// const StoryPart = ({text}) => (
//   <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
//     <Text>{text}</Text>
//     {/* <Image
//       // style={styles.cardImage}
//       source={require('../../static/Images/vader.png')}
//     /> */}
//   </View>
// );

// const Story = ({route, navigation}) => {
//   const {parts} = route.params;
//   const [currentPart, setCurrentPart] = useState(0);
//   const [isPaused, setIsPaused] = useState(false);
//   const pagerRef = useRef();

//   const handleProgressComplete = () => {
//     if (currentPart < parts.length - 1) {
//       setCurrentPart(currentPart + 1);
//     } else {
//       navigation.reset({
//         index: 0,
//         routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
//       });
//     }
//   };

//   useEffect(() => {
//     pagerRef.current.setPage(currentPart);
//   }, [currentPart]);

//   return (
//     <View style={{flex: 1}}>
//       <View style={{flexDirection: 'row', height: 5, marginTop: 10}}>
//         {parts.map((part, idx) => (
//           <ProgressBar
//             key={idx}
//             duration={3000}
//             isActive={idx === currentPart && !isPaused}
//             onProgressComplete={handleProgressComplete}
//           />
//         ))}
//       </View>
//       <PagerView style={{flex: 1}} initialPage={0} ref={pagerRef}>
//         {parts.map((part, idx) => (
//           <View key={idx} style={{flex: 1}}>
//             <StoryPart text={part} />
//             <Pressable
//               style={{
//                 position: 'absolute',
//                 top: 0,
//                 left: 0,
//                 right: screenWidth / 2,
//                 bottom: 0,
//                 zIndex: 10,
//                 backgroundColor: 'transparent',
//               }}
//               onPress={() => {
//                 if (currentPart > 0) setCurrentPart(currentPart - 1);
//               }}
//               onLongPress={() => setIsPaused(true)}
//               onPressOut={() => setIsPaused(false)}
//             />
//             <Pressable
//               style={{
//                 position: 'absolute',
//                 top: 0,
//                 left: screenWidth / 2,
//                 right: 0,
//                 bottom: 0,
//                 zIndex: 10,
//                 backgroundColor: 'transparent',
//               }}
//               onPress={() => {
//                 if (currentPart < parts.length - 1)
//                   setCurrentPart(currentPart + 1);
//               }}
//               onLongPress={() => setIsPaused(true)}
//               onPressOut={() => setIsPaused(false)}
//             />
//           </View>
//         ))}
//       </PagerView>
//     </View>
//   );
// };

// export default Story;

// import React, {useState, useEffect, useRef} from 'react';
// import {View, Pressable, Text, Dimensions, Image} from 'react-native';
// import PagerView from 'react-native-pager-view';

// const screenWidth = Dimensions.get('window').width;

// const ProgressBar = ({progressValue}) => {
//   return (
//     <View style={{flex: 1, backgroundColor: 'red', margin: 2, marginRight: 5}}>
//       <View
//         style={{
//           width: `${progressValue}%`,
//           height: '100%',
//           backgroundColor: 'gray',
//         }}
//       />
//     </View>
//   );
// };

// const StoryPart = ({text}) => (
//   <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
//     <Text>{text}</Text>
//     <Image source={require('../../static/Images/vader.png')} />
//   </View>
// );

// const Story = ({route, navigation}) => {
//   const {parts} = route.params;
//   const [currentPart, setCurrentPart] = useState(0);
//   const [progressValues, setProgressValues] = useState(parts.map(() => 0));
//   const pagerRef = useRef();

//   const progressInterval = useRef();

//   const startProgressForCurrentPart = () => {
//     if (progressInterval.current) {
//       clearInterval(progressInterval.current);
//     }

//     setProgressValues(values => {
//       const updated = [...values];
//       updated[currentPart] = 0;
//       return updated;
//     });

//     progressInterval.current = setInterval(() => {
//       setProgressValues(prev => {
//         if (prev[currentPart] < 100) {
//           const updated = [...prev];
//           updated[currentPart] = prev[currentPart] + 1;
//           return updated;
//         } else {
//           clearInterval(progressInterval.current);
//           if (currentPart < parts.length - 1) {
//             setCurrentPart(prev => prev + 1);
//           }
//           return prev;
//         }
//       });
//     }, 30);
//   };

//   useEffect(() => {
//     startProgressForCurrentPart();
//     return () => {
//       if (progressInterval.current) {
//         clearInterval(progressInterval.current);
//       }
//     };
//   }, [currentPart]);

//   useEffect(() => {
//     pagerRef.current.setPage(currentPart);
//   }, [currentPart]);

//   return (
//     <View style={{flex: 1}}>
//       <View style={{flexDirection: 'row', height: 5, marginTop: 10}}>
//         {progressValues.map((progress, idx) => (
//           <ProgressBar key={idx} progressValue={progress} />
//         ))}
//       </View>
//       <PagerView style={{flex: 1}} initialPage={0} ref={pagerRef}>
//         {parts.map((part, idx) => (
//           <View key={idx} style={{flex: 1}}>
//             <StoryPart text={part} />
//             <Pressable
//               style={{
//                 position: 'absolute',
//                 top: 0,
//                 left: 0,
//                 right: screenWidth / 2,
//                 bottom: 0,
//                 zIndex: 10,
//                 backgroundColor: 'transparent',
//               }}
//               onPress={() => {
//                 if (currentPart > 0) {
//                   setCurrentPart(prev => prev - 1);
//                 }
//               }}
//             />
//             <Pressable
//               style={{
//                 position: 'absolute',
//                 top: 0,
//                 left: screenWidth / 2,
//                 right: 0,
//                 bottom: 0,
//                 zIndex: 10,
//                 backgroundColor: 'transparent',
//               }}
//               onPress={() => {
//                 if (currentPart < parts.length - 1) {
//                   setCurrentPart(prev => prev + 1);
//                 }
//               }}
//             />
//           </View>
//         ))}
//       </PagerView>
//     </View>
//   );
// };

// export default Story;
import React, {useRef, useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  Button,
  ScrollView,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import Feather from 'react-native-vector-icons/Feather';

const ProgressBar = ({progressValue}) => {
  return (
    <View style={{flex: 1, backgroundColor: 'gray', margin: 2, marginRight: 5}}>
      <View
        style={{
          width: `${progressValue}%`,
          height: '100%',
          backgroundColor: 'red',
        }}
      />
    </View>
  );
};

export function Story({navigation, route}: any) {
  const {pages} = route.params;
  // const pages = [
  //   'First 1 page',
  //   'Second 2 page',
  //   'Third 3 page',
  //   'Fourth 4 page',
  // ];
  const [progressValues, setProgressValues] = useState(pages.map(() => 0));
  const pagerRef = useRef(null);
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [activePageProgress, setActivePageProgress] = useState(0);
  useEffect(() => {
    let interval;

    const incrementAmount = 1; // This calculates how much to increase every tick for 6 seconds

    if (activePageProgress < 100) {
      interval = setInterval(() => {
        setActivePageProgress(prevValue => {
          const newValue = prevValue + incrementAmount;
          if (newValue >= 100) {
            clearInterval(interval);
            return 100;
          }
          return newValue;
        });
      }, 60); // 60ms for a smoother transition
    }

    return () => {
      clearInterval(interval); // Cleanup on unmount or when currentPageIndex changes
    };
  }, [currentPageIndex]);

  useEffect(() => {
    const nextPage = currentPageIndex + 1;

    const timer = setTimeout(() => {
      if (nextPage >= pages.length) {
        console.log('Last page, add navigation');
      } else {
        setActivePageProgress(0);
        pagerRef.current.setPage(nextPage);
      }
    }, 6000); // Change page every 6 seconds

    return () => {
      clearTimeout(timer); // Cleanup on unmount or when currentPageIndex changes
    };
  }, [currentPageIndex]);

  return (
    <View style={{flex: 1}}>
      {/* <Text style={{color: 'black', fontSize: 30}}>
        {' '}
        Testing page No : {currentPageIndex + 1}
      </Text> */}
      <PagerView
        style={styles.pagerView}
        initialPage={0}
        scrollEnabled={false}
        ref={pagerRef}
        onPageSelected={e => {
          console.log('Received event:', e.nativeEvent);
          console.log('Progress Values', progressValues);
          setCurrentPageIndex(e.nativeEvent.position);
        }}>
        {pages.map((pageText, index) => (
          <View key={index.toString()} style={styles.pagerView}>
            {/* Left half of the screen */}
            <Pressable
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                right: '50%',
                zIndex: 1,
              }}
              onPress={() => {
                if (index > 0) {
                  setActivePageProgress(0);
                  pagerRef.current.setPage(index - 1);
                }
              }}
            />

            {/* Right half of the screen */}
            <Pressable
              style={{
                position: 'absolute',
                right: 0,
                top: 0,
                bottom: 0,
                left: '50%',
                zIndex: 1,
              }}
              onPress={() => {
                if (index < pages.length - 1) {
                  setActivePageProgress(0);
                  pagerRef.current.setPage(index + 1);
                }
              }}
            />
            <ScrollView contentContainerStyle={styles.scrollViewContent}>
              <View style={{flexDirection: 'row', height: 5, marginTop: 10}}>
                {progressValues.map((progress, idx) => {
                  if (idx < index) {
                    return <ProgressBar key={idx} progressValue={100} />;
                  } else if (idx > index) {
                    return <ProgressBar key={idx} progressValue={0} />;
                  } else if (idx == index) {
                    // return <ProgressBar key={idx} progressValue={progress} />;
                    return (
                      <ProgressBar
                        key={idx}
                        progressValue={activePageProgress}
                      />
                    );
                  }
                })}
              </View>

              {/* <Button
                title="<---Backward access"
                onPress={() => {
                  setActivePageProgress(0);
                  pagerRef.current.setPage(index - 1);
                }}
              />
              <Button
                title="Forward access--->"
                onPress={() => {
                  setActivePageProgress(0);
                  pagerRef.current.setPage(index + 1);
                }}
              /> */}
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  backgroundColor: '#F7F8C9',
                }}>
                <Image
                  style={styles.cardImage}
                  source={require('../../static/Images/forest.jpg')}
                />
                <Text
                  style={{color: 'white', fontSize: 30, position: 'absolute'}}>
                  {pageText}
                </Text>
              </View>
              {/* <TouchableOpacity
                style={[
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                  },
                ]}
                onPress={() => console.log('up')}>
                <Feather name="chevrons-up" size={30} color="black" />
              </TouchableOpacity>

              <Text style={{color: 'black', fontSize: 30}}>
                Challenge Content
              </Text> */}
            </ScrollView>
          </View>
        ))}
      </PagerView>
    </View>
  );
}
const styles = StyleSheet.create({
  pagerView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1, // Ensures the content grows to fill the available space
  },
  cardImage: {
    alignSelf: 'flex-end',
    width: '100%',
    height: 300,
    marginBottom: 8,
    marginTop: 10,
  },
});
export default Story;
