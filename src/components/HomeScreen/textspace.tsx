import React, {useState} from 'react';
import {Text, View, StyleSheet, TextInput} from 'react-native';
import {TouchableOpacity} from 'react-native-gesture-handler';

export function TextStyleScreen({route, navigation}: any) {
  const [tag, setTag] = useState('');
  const onLayoutHandler = event => {
    const {x, y, width, height} = event.nativeEvent.layout;
    console.log(`Width: ${width}, Height: ${height}`);
  };

  return (
    <View>
      <Text style={{fontSize: 20}} onLayout={onLayoutHandler}>
        HBellllllllllllllllllllllllllllBo
      </Text>
      <Text style={{fontSize: 10}} onLayout={onLayoutHandler}>
        HBellllllllllllllllllllllllllllBo
      </Text>
      {/* <Text>{route.params.arr}</Text> */}
      <TextInput
        onLayout={onLayoutHandler}
        style={{fontSize: 14, height: 40.4, color: 'black'}}
        onContentSizeChange={event => {
          const contentHeight = event.nativeEvent.contentSize.height;
          console.log('Content Height:', contentHeight);
        }}
        value={tag}
        onChangeText={newtext => setTag(newtext)}
        placeholder="#Hashtag"
        returnKeyLabel="done"
        placeholderTextColor="grey"
        autoFocus={true}
        multiline={true}
      />
      <View style={styles.DoneStyle}>
        <Text onLayout={onLayoutHandler} style={{fontSize: 100}}>
          D
        </Text>
        <Text onLayout={onLayoutHandler} style={{fontSize: 10}}>
          D
        </Text>
        <Text onLayout={onLayoutHandler}>
          Helllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllo
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  DoneStyle: {
    alignItems: 'center',
  },
});

export default TextStyleScreen;
