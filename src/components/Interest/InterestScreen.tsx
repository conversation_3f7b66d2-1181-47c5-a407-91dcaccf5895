import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import acrossAllScreens from '../../styles/acrossAllScreens';

const interestData = require('../../static/InterestScreenStatic/InterestScreenCategories.json');

export function InterestScreen({navigation}) {
  const [selectedItems, setSelectedItems] = useState(new Set());

  // Handle category selection with instant updates
  const onClick = useCallback(category => {
    setSelectedItems(prevItems => {
      const newItems = new Set(prevItems);
      newItems.has(category)
        ? newItems.delete(category)
        : newItems.add(category);
      return new Set(newItems);
    });
  }, []);

  useEffect(() => {
    console.log('Selected Items:', Array.from(selectedItems));
  }, [selectedItems]);

  return (
    <View style={styles.container}>
      {/* HEADER */}
      <View style={styles.header}>
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.headerButton]}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>User Interests</Text>

        <TouchableOpacity
          onPress={() =>
            console.log('Updating Interests:', Array.from(selectedItems))
          }
          style={styles.headerButton}>
          <Text style={styles.updateButton}>Update</Text>
        </TouchableOpacity>
      </View>

      {/* DESCRIPTION */}
      <Text style={styles.description}>
        Please select an interest tag to help us get you started. You can add or
        remove interest tags at any time on this page.
      </Text>

      {/* INTEREST LIST */}
      <FlatList
        data={interestData.Data}
        keyExtractor={item => item.MainCategory}
        extraData={selectedItems}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.flatListContent}
        showsVerticalScrollIndicator={false}
        renderItem={({item}) => (
          <View style={styles.categoryWrapper}>
            {/* MAIN CATEGORY */}
            <TouchableOpacity
              style={[
                styles.categoryButton,
                selectedItems.has(item.MainCategory) && styles.selectedCategory,
              ]}
              onPress={() => onClick(item.MainCategory)}>
              <Text style={styles.categoryText}>{item.MainCategory}</Text>
            </TouchableOpacity>

            {/* SUB-CATEGORIES */}
            <View style={styles.subCategoryWrapper}>
              {item.subCategory.map(tag => (
                <TouchableOpacity
                  key={tag.Title}
                  style={[
                    styles.subCategoryButton,
                    selectedItems.has(tag.Title) && styles.selectedCategory,
                  ]}
                  onPress={() => onClick(tag.Title)}>
                  <Text style={styles.categoryText}>{tag.Title}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      />
    </View>
  );
}

// STYLES
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  updateButton: {
    fontSize: 16,
    color: '#b6dffc',
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 10,
  },
  flatListContent: {
    paddingBottom: 2, // Ensures last items are visible
  },
  categoryWrapper: {
    marginBottom: 10,
  },
  categoryButton: {
    borderWidth: 1.5,
    borderColor: 'black',
    borderRadius: 15,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  subCategoryWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  subCategoryButton: {
    borderWidth: 1.5,
    borderColor: 'black',
    borderRadius: 15,
    paddingVertical: 6,
    paddingHorizontal: 10,
    margin: 4,
    alignSelf: 'flex-start',
  },
  selectedCategory: {
    backgroundColor: '#C3E7F5', // Blue highlight when selected
  },
  categoryText: {
    fontWeight: 'bold',
  },
});

export default InterestScreen;
