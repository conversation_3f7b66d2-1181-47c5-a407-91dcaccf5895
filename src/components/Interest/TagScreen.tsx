import React, {useState, useCallback, useEffect} from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity, Image} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import acrossAllScreens from '../../styles/acrossAllScreens';

const interestData = require('../../static/InterestScreenStatic/InterestScreenCategories.json');

export function TagScreen({navigation, route}) {
  const existingTags = route.params?.existingTags || []; // Get existing selected tags
  const [selectedItems, setSelectedItems] = useState(new Set(existingTags));

  const onClick = useCallback(category => {
    setSelectedItems(prevItems => {
      const newItems = new Set(prevItems);
      newItems.has(category)
        ? newItems.delete(category)
        : newItems.add(category);
      return newItems;
    });
  }, []);

  return (
    <View style={styles.container}>
      {/* HEADER */}
      <View style={styles.header}>
        <TouchableOpacity
          style={acrossAllScreens.backImageContainer}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tags</Text>

        <TouchableOpacity
          onPress={() => {
            // Get the previous screen's key dynamically
            const previousRoute =
              navigation.getState().routes[navigation.getState().index - 1];

            // Navigate back while passing tags as params
            navigation.navigate(previousRoute.name, {
              tags: Array.from(selectedItems),
            });
          }}
          style={styles.headerButton}>
          <Text style={styles.updateButton}>Update</Text>
        </TouchableOpacity>
      </View>

      {/* DESCRIPTION */}
      <Text style={styles.description}>
        Please select tags that are most relatable to your challenge.
      </Text>

      {/* INTEREST LIST */}
      <FlatList
        data={interestData.Data}
        keyExtractor={item => item.MainCategory}
        extraData={selectedItems}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.flatListContent}
        showsVerticalScrollIndicator={false}
        renderItem={({item}) => (
          <View style={styles.categoryWrapper}>
            {/* MAIN CATEGORY */}
            <TouchableOpacity
              style={[
                styles.categoryButton,
                selectedItems.has(item.MainCategory) && styles.selectedCategory,
              ]}
              onPress={() => onClick(item.MainCategory)}>
              <Text style={styles.categoryText}>{item.MainCategory}</Text>
            </TouchableOpacity>

            {/* SUB-CATEGORIES */}
            <View style={styles.subCategoryWrapper}>
              {item.subCategory.map(tag => (
                <TouchableOpacity
                  key={tag.Title}
                  style={[
                    styles.subCategoryButton,
                    selectedItems.has(tag.Title) && styles.selectedCategory,
                  ]}
                  onPress={() => onClick(tag.Title)}>
                  <Text style={styles.categoryText}>{tag.Title}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      />
    </View>
  );
}

// STYLES
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  updateButton: {
    fontSize: 16,
    // color: '#A5D8FF',
    color: '#2688B4',
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 10,
  },
  flatListContent: {
    paddingBottom: 80,
  },
  categoryWrapper: {
    marginBottom: 10,
  },
  categoryButton: {
    borderWidth: 1.5,
    borderColor: 'black',
    borderRadius: 15,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  subCategoryWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  subCategoryButton: {
    borderWidth: 1.5,
    borderColor: 'black',
    borderRadius: 15,
    paddingVertical: 6,
    paddingHorizontal: 10,
    margin: 4,
    alignSelf: 'flex-start',
  },
  selectedCategory: {
    backgroundColor: '#c3e7f5',
  },
  categoryText: {
    fontWeight: 'bold',
  },
});

export default TagScreen;
