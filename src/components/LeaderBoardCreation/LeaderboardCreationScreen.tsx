import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableHighlight,
  Modal,
  Switch,
  TextInput,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  Alert,
  Image,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {
  getChallengeCreatedByUser,
  updateChallenge,
} from '../../redux/Challenge/ChallengeAction';
import Video from 'react-native-video';
import FastImage from 'react-native-fast-image';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import Toast from 'react-native-toast-message';
import analytics from '@react-native-firebase/analytics';
import SignUpStyle from '../../styles/SignUp';

const {width} = Dimensions.get('window');

let bottomLoad = false;
export const LeaderboardCreationScreen = ({navigation}: any) => {
  const dispatch = useAppDispatch();
  const challengeCreatedByUser = useAppSelector(
    state => state.challenge.challengeCreatedByUser.data,
  );
  const nextPageToken = useAppSelector(
    state => state.challenge.challengeCreatedByUser.nextPageToken,
  );
  const hasNext = useAppSelector(
    state => state.challenge.challengeCreatedByUser.hasNext,
  );
  const [selectedChallenge, setSelectedChallenge] = useState<any>(null); // Track selected challenge
  const [isModalVisible, setModalVisible] = useState(false); // Modal visibility state
  const [isAscendingOrder, setIsAscendingOrder] = useState(true);
  const [isAutomaticEntry, setIsAutomaticEntry] = useState(true);
  const [pointsColumnTitle, setPointsColumnTitle] = useState('');
  const [point, setPoint] = useState('');
  const [pointErrorMessage, setPointError] = useState('');
  const [pointsErrorMessage, setPointsErrorMessage] = useState('');
  const authState = useAppSelector(state => state.auth);
  const {userId} = authState;
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  const [createLeaderBoardLoading, setCreateLeaderBoardLoading] =
    useState(false);

  useEffect(() => {
    // this is code to add analytics for the screen
    analytics().logScreenView({
      screen_name: 'LeaderboardCreationScreen',
      screen_class: 'LeaderboardCreationScreen',
    });
  }, []);

  useEffect(() => {
    if (userId) {
      setDataLoading(true);
      dispatch(
        getChallengeCreatedByUser({userId, leaderboardCreated: false}),
      ).finally(() => {
        setDataLoading(false);
      });
    }
  }, [userId]);

  // Function to handle selecting a challenge
  const handleSelectChallenge = (item: any) => {
    if (item.challengeId === selectedChallenge?.challengeId) {
      // If the item is already selected, deselect it
      setSelectedChallenge(null);
      setModalVisible(false); // Don't open modal if deselected
    } else {
      // Select a new challenge and open the modal
      setSelectedChallenge(item);
      setModalVisible(true); // Open modal only on selecting a challenge
      setPointsColumnTitle(''); // Reset points column title
    }
  };

  const renderChallengeItem = ({item}: any) => (
    <TouchableOpacity
      style={[styles.challengeItem]}
      onPress={() => handleSelectChallenge(item)}>
      {item.mediaRefs &&
        item.mediaRefs.length > 0 &&
        (isVideoLink(item.mediaRefs[0]) ? (
          <Video
            source={{
              uri: item.mediaRefs[0],
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }}
            bufferConfig={{
              minBufferMs: 2500,
              maxBufferMs: 50000,
              bufferForPlaybackMs: 2500,
              bufferForPlaybackAfterRebufferMs: 2500,
            }}
            useTextureView={false}
            onError={error => console.log(error, 'errorerrorerrorerror')}
            style={[
              styles.selectedChallengeOverlay,
              {borderRadius: 8, backgroundColor: 'gray'},
            ]}
            resizeMode="cover"
            repeat
            paused={true}
          />
        ) : (
          <FastImage
            source={{
              uri: item.mediaRefs[0],
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }}
            style={[
              styles.selectedChallengeOverlay,
              {borderRadius: 8, backgroundColor: 'gray'},
            ]}
          />
        ))}
    </TouchableOpacity>
  );

  const onCreateLeaderBoard = async () => {
    const challengeValue = {
      challengeId: selectedChallenge?.challengeId,
      isLeaderboardCreated: true,
      pointsTitle: pointsColumnTitle,
      pointsDescription: '',
      isPointsAutomated: isAutomaticEntry,
      isPointsAscending: isAscendingOrder,
      points: point,
    };
    setCreateLeaderBoardLoading(true);
    const result = await dispatch(updateChallenge(challengeValue)); // Replace `1` with the appropriate ID or payload
    if (updateChallenge.fulfilled.match(result)) {
      if (result.payload.code == 200) {
        setModalVisible(false); // Close modal
        navigation.replace('BottomTabsNavigator', {
          screen: 'Leaderboard',
          params: {
            screen: 'LeaderboardDetails',
            params: {
              name: result.payload.body.data.pointsTitle,
              creator: result.payload.body.data.userId,
              challengeId: selectedChallenge?.challengeId,
              isGoLeaderBoardTab: true,
            },
          },
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to create leaderboard.',
          text2: 'Something went wrong.',
        });
      }
    } else if (updateChallenge.rejected.match(result)) {
      Toast.show({
        type: 'error',
        text1: 'Failed to create leaderboard.',
        text2: 'Something went wrong.',
      });
    }
    setCreateLeaderBoardLoading(false);
  };

  const handleCreateLeaderBoard = () => {
    if (pointsColumnTitle.trim() === '') {
      setPointsErrorMessage('Please enter a points column title.'); // Show error message if no title
    } else if (!isAutomaticEntry && point.trim() === '') {
      setPointError('Please enter a point.');
    } else {
      setPointsErrorMessage(''); // Clear error message
      setPointError('');
      onCreateLeaderBoard();
    }
  };

  const onExploreFeedEnd = useCallback(() => {
    if (
      challengeCreatedByUser.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasNext
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeCreatedByUser({
          userId: userId,
          nextPageToken: nextPageToken,
          leaderboardCreated: false,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, challengeCreatedByUser, nextPageToken, loading, hasNext]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <View style={[acrossAllScreens.ScreenBorders]}>
        {/* 1. Header Section */}
        <View style={[styles.HeaderStyle]}>
          <TouchableOpacity
            style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
            onPress={() => navigation.goBack()}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <View style={styles.HeaderHorizontalPosition}>
            <Text style={[acrossAllScreens.ScreenHeaderText]}>
              Create Leaderboard
            </Text>
          </View>
        </View>

        {dataLoading ? (
          <View style={[styles.noChallengesContainer, {alignItems: 'center'}]}>
            <ActivityIndicator />
          </View>
        ) : challengeCreatedByUser.length === 0 ? (
          // No challenges case: Display clickable text to create a challenge
          <View style={styles.noChallengesContainer}>
            <TouchableOpacity
              style={[styles.noChallengesButton]}
              onPress={() =>
                navigation.replace('ChallengeCreationScreenStack', {
                  screen: 'ChallengeCreationScreen',
                })
              }>
              <Text style={styles.noChallengesText}>Create a challenge to</Text>
              <Text style={styles.noChallengesText}>
                start a new leaderboard
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {/* Info Text */}
            <Text style={styles.infoText}>
              Select a challenge to create leaderboard
            </Text>

            {/* Challenge Section */}
            <View style={styles.challengeListContainer}>
              <FlatList
                data={challengeCreatedByUser}
                renderItem={renderChallengeItem}
                showsVerticalScrollIndicator={false}
                keyExtractor={(item: any) => item.challengeId}
                numColumns={2} // Display two challenges per row
                onEndReached={onExploreFeedEnd}
                onEndReachedThreshold={0.1}
                ListFooterComponent={renderFooter}
              />
            </View>

            {/* Create Leaderboard Button */}
            <View style={styles.RowStyle}>
              <TouchableOpacity
                onPress={() => {
                  navigation.replace('ChallengeCreationScreenStack', {
                    screen: 'ChallengeCreationScreen',
                  });
                }}>
                <Text style={styles.createChllengeText}>
                  Create a Challenge to start a new Leaderboard
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>

      {/* Modal for Leaderboard Scoring System */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)} // Close modal when back button is pressed
      >
        <TouchableWithoutFeedback
          onPress={() => setModalVisible(false)} // Close modal on overlay touch
          accessible={false}>
          <View style={styles.modalBackground}>
            <TouchableWithoutFeedback
              onPress={Keyboard.dismiss}
              accessible={false}>
              <View style={styles.modalContainer}>
                <Text style={styles.modalTitle}>
                  Choose leaderboard scoring system
                </Text>
                <Text style={styles.modalChallengeTitle}>
                  {selectedChallenge ? selectedChallenge.title : ''}
                </Text>

                <View style={styles.modalContent}>
                  <TextInput
                    style={styles.pointsColumnTitleInput}
                    value={pointsColumnTitle}
                    onChangeText={setPointsColumnTitle}
                    placeholder="Enter Points Column Title*"
                    placeholderTextColor="grey"
                  />
                  {pointsErrorMessage ? (
                    <Text style={styles.errorText}>{pointsErrorMessage}</Text>
                  ) : null}
                  <View style={styles.separator} />
                  <View style={styles.toggleContainer}>
                    <Text style={styles.toggleTitle}>
                      Ascending Points Sorting Order
                    </Text>
                    <Switch
                      value={isAscendingOrder}
                      onValueChange={setIsAscendingOrder}
                      thumbColor={isAscendingOrder ? '#FFFFFF' : '#C3E7F5'}
                      trackColor={{false: '#FFFFFF', true: '#C3E7F5'}}
                    />
                  </View>
                  {isAscendingOrder ? (
                    <Text style={styles.descriptionText}>
                      Lower the points, better the ranking
                    </Text>
                  ) : (
                    <Text style={styles.descriptionText}>
                      Higher the points, better the ranking
                    </Text>
                  )}
                  <View style={styles.toggleContainer}>
                    <Text style={styles.toggleTitle}>
                      Automatic Points Entry
                    </Text>
                    <Switch
                      value={isAutomaticEntry}
                      onValueChange={setIsAutomaticEntry}
                      thumbColor={isAutomaticEntry ? '#FFFFFF' : '#C3E7F5'}
                      trackColor={{false: '#FFFFFF', true: '#C3E7F5'}}
                    />
                  </View>
                  {isAutomaticEntry ? (
                    <Text style={styles.descriptionText}>
                      Napoz will automatically add 1 point per post created for
                      the challenge
                    </Text>
                  ) : (
                    <Text style={styles.descriptionText}>
                      User are supposed to manually enter their points
                    </Text>
                  )}
                  {!isAutomaticEntry && (
                    <TextInput
                      style={styles.pointsColumnTitleInput}
                      value={point}
                      onChangeText={setPoint}
                      keyboardType={'number-pad'}
                      placeholder="Enter Points*"
                      placeholderTextColor="grey"
                    />
                  )}
                  {pointErrorMessage ? (
                    <Text style={styles.errorText}>{pointErrorMessage}</Text>
                  ) : null}
                  {!isAutomaticEntry && <View style={styles.separator} />}
                  {/* Close Modal Button */}
                  <TouchableHighlight
                    style={styles.closeButton}
                    onPress={handleCreateLeaderBoard}>
                    {createLeaderBoardLoading ? (
                      <ActivityIndicator />
                    ) : (
                      <Text style={styles.closeButtonText}>
                        Create Leaderboard
                      </Text>
                    )}
                  </TouchableHighlight>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  createChllengeText: {
    textDecorationLine: 'underline',
    fontSize: 14,
    fontFamily: 'Helvetica Neue',
    color: 'black',
    fontWeight: '300',
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#F5F5F5',
  },
  RowStyle: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  challengeListContainer: {
    flex: 1,
    marginTop: 10,
  },
  challengeItem: {
    backgroundColor: '#D3D3D3',
    height: 150, // Adjust height for the image
    width: (width - 48) / 2, // 2 columns with some padding
    marginBottom: 16,
    marginRight: 16,
    borderRadius: 8, // Rounded corners
    overflow: 'hidden', // Ensures the image respects border radius
  },
  challengeImage: {
    flex: 1,
    justifyContent: 'flex-end', // Position text at the bottom
    padding: 10,
  },
  challengeText: {
    fontSize: 14,
    fontFamily: 'Helvetica Neue',
    color: 'black',
  },
  selectedChallengeOverlay: {
    ...StyleSheet.absoluteFillObject, // Fills the entire image area
    backgroundColor: 'rgba(241, 195, 75, 0.8)', // Semi-transparent yellow highlight
  },
  disabledButton: {
    backgroundColor: '#cccccc', // Greyed out background
  },
  infoText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 30, // 30px below header
    marginBottom: 10,
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  textContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // Semi-transparent background behind the text
    padding: 5,
    alignSelf: 'flex-start',
  },
  // Modal Styles
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    marginBottom: 20,
    fontFamily: 'Helvetica Neue',
  },
  modalChallengeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    fontFamily: 'Helvetica Neue',
  },
  modalContent: {
    marginVertical: 10,
  },
  pointsColumnTitleInput: {
    fontSize: 14,
    fontFamily: 'Helvetica Neue',
    color: 'black',
  },
  separator: {
    height: 1,
    backgroundColor: '#d3d3d3',
    marginVertical: 10,
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  toggleTitle: {
    fontSize: 16,
  },
  descriptionText: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 20,
  },
  closeButton: {
    backgroundColor: '#c3e7f5',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  closeButtonText: {
    color: '#000000',
    fontWeight: 'bold',
    fontSize: 18,
  },
  errorText: {
    color: 'red',
    fontSize: 14,
    marginVertical: 10,
  },
  noChallengesText: {
    fontSize: 18,
    color: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: '700',
  },
  noChallengesContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 20,
    height: '90%',
  },
  noChallengesButton: {
    backgroundColor: '#c3e7f5',
    height: 70,
    // flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    width: 250,
    borderRadius: 6,
  },
});

export default LeaderboardCreationScreen;
