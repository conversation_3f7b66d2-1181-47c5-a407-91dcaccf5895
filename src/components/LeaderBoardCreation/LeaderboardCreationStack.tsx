import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {Button} from 'react-native';
import {TabRouter} from '@react-navigation/routers';
import {LeaderboardCreationScreen} from '../LeaderBoardCreation/LeaderboardCreationScreen';
import LeaderboardTableFormatScreen from './LeaderboardTableFormatScreen';
import CalendarScreen from '../Common/CalendarScreen';
import TagFriendsScreen from '../TagFriends/TagFriendsScreen';
import AllFriendsScreen from '../TagFriends/AllFriendsScreen';

const leaderboardCreationStack = createStackNavigator();

export function LeaderboardCreationScreenStack() {
  return (
    <leaderboardCreationStack.Navigator>
      <leaderboardCreationStack.Screen
        name="LeaderboardCreationScreen"
        component={LeaderboardCreationScreen}
        options={{title: 'Create Leaderboard', headerShown: false}}
      />
      <leaderboardCreationStack.Screen
        name="TableFormat"
        component={LeaderboardTableFormatScreen}
      />
      <leaderboardCreationStack.Screen
        name="TagFriends"
        component={TagFriendsScreen}
        // options={{headerShown: false}}
      />
      <leaderboardCreationStack.Screen
        name="AllFriends"
        component={AllFriendsScreen}
        // options={{headerShown: false}}
      />
    </leaderboardCreationStack.Navigator>
  );
}

export default LeaderboardCreationScreenStack;
