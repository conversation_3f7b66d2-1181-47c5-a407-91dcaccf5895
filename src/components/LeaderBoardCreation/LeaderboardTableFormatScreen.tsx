import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Octicons from 'react-native-vector-icons/Octicons';

export function LeaderboardTableFormatScreen({route, navigation}: any) {
  const [tag, setTag] = useState('');
  const [selectedItem, setSelectedItem] = useState(null); // Initialize as null since no item is selected initially

  return (
    <View>
      <Text style={styles.HeaderStyle}>Leaderboard Table Formatting</Text>

      <TextInput
        value={tag}
        onChangeText={newtext => setTag(newtext)}
        placeholder="Score Column Title"
        returnKeyLabel="done"
        // autoFocus={true}
        placeholderTextColor="grey"
        style={styles.inputText}
      />
      <TouchableOpacity
        onPress={() => {
          setSelectedItem('Asc');
        }}
        style={styles.SelectionButton}>
        <Text style={styles.optionTextStyle}>Ascending Order of Points </Text>
        {selectedItem === 'Asc' ? (
          <AntDesign name="checkcircleo" size={30} color="black" />
        ) : (
          <Octicons name="circle" size={30} color="black" />
        )}
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          setSelectedItem('Desc');
        }}
        style={styles.SelectionButton}>
        <Text style={styles.optionTextStyle}>Descending Order of Points</Text>
        {selectedItem === 'Desc' ? (
          <AntDesign name="checkcircleo" size={30} color="black" />
        ) : (
          <Octicons name="circle" size={30} color="black" />
        )}
      </TouchableOpacity>
      <View style={styles.ButtonContainer}>
        <TouchableOpacity
          onPress={() => {
            navigation.reset({
              index: 0,
              routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
            });
          }}
          disabled={selectedItem === null || tag.trim() === ''} // Button is disabled when no item is selected
          style={[
            styles.Button,

            (selectedItem === null || tag.trim() === '') &&
              styles.buttonDisabled,
          ]}>
          <Text
            style={[
              styles.ButtonText,
              (selectedItem === null || tag.trim() === '') &&
                styles.buttonTextDisabled,
            ]}>
            Done
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginStart: 10,
    marginBottom: 10,
    marginTop: 5,
    color: 'black',
  },
  optionTextStyle: {
    fontSize: 25,
    fontWeight: '600',
    marginStart: 10,
    marginBottom: 10,
    marginTop: 5,
    color: 'black',
  },
  ButtonContainer: {
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },

  Button: {
    backgroundColor: '#fd9644',
    height: 45,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    width: 100,
    borderRadius: 30,
  },
  ButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  SelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  buttonTextDisabled: {
    color: '#7f8c8d', // Grayed-out text for the disabled state
  },
  buttonDisabled: {
    backgroundColor: '#bdc3c7', // Grayed-out background for the disabled state
  },
  inputText: {color: 'black', fontSize: 20},
});

export default LeaderboardTableFormatScreen;
