import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Octicons from 'react-native-vector-icons/Octicons';

export function LeaderboardEditScreen({route, navigation}: any) {
  const [columnName, setColumnName] = useState(route.params?.columnName);
  //   const [selectedItem, setSelectedItem] = useState(null);
  const [selectedItem, setSelectedItem] = useState(route.params?.sort);
  return (
    <View>
      {/* <Text>{route.params.arr}</Text> */}
      <Text style={styles.HeaderStyle}>Edit Leaderboard table</Text>
      <View style={styles.InputTextPlacement}>
        <Text style={styles.optionTextStyle}>Score Column Title:</Text>
        <TextInput
          value={columnName}
          onChangeText={newtext => setColumnName(newtext)}
          placeholder="Default "
          returnKeyLabel="done"
          placeholderTextColor="grey"
          autoFocus={true}
          style={styles.inputText}
        />
      </View>
      <TouchableOpacity
        onPress={() => {
          setSelectedItem('Asc');
        }}
        style={styles.SelectionButton}>
        <Text style={styles.optionTextStyle}>Ascending Order of Points </Text>
        {selectedItem === 'Asc' ? (
          <AntDesign name="checkcircleo" size={30} color="black" />
        ) : (
          <Octicons name="circle" size={30} color="black" />
        )}
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          setSelectedItem('Desc');
        }}
        style={styles.SelectionButton}>
        <Text style={styles.optionTextStyle}>Descending Order of Points</Text>
        {selectedItem === 'Desc' ? (
          <AntDesign name="checkcircleo" size={30} color="black" />
        ) : (
          <Octicons name="circle" size={30} color="black" />
        )}
      </TouchableOpacity>

      <View style={styles.ButtonContainer}>
        <TouchableOpacity
          onPress={() => {
            navigation.reset({
              index: 0,
              routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
            });
          }}
          disabled={selectedItem === null || columnName.trim() === ''} // Button is disabled when no item is selected
          style={[
            styles.Button,

            (selectedItem === null || columnName.trim() === '') &&
              styles.buttonDisabled,
          ]}>
          <Text
            style={[
              styles.ButtonText,
              (selectedItem === null || columnName.trim() === '') &&
                styles.buttonTextDisabled,
            ]}>
            Done
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    fontSize: 32,
    fontWeight: 'bold',

    marginBottom: 10,
    marginTop: 5,
    color: 'black',
  },
  inputText: {color: 'black', fontSize: 20},
  ButtonContainer: {
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },

  Button: {
    backgroundColor: '#fd9644',
    height: 45,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    width: 100,
    borderRadius: 30,
  },
  ButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },

  optionTextStyle: {
    fontSize: 25,
    fontWeight: '600',
    marginStart: 10,
    marginBottom: 10,
    marginTop: 5,
    color: 'black',
  },
  SelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  InputTextPlacement: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonTextDisabled: {
    color: '#7f8c8d', // Grayed-out text for the disabled state
  },
  buttonDisabled: {
    backgroundColor: '#bdc3c7', // Grayed-out background for the disabled state
  },
});

export default LeaderboardEditScreen;
