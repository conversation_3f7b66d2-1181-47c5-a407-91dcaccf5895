import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  Dimensions,
  Alert,
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {LeaderBoard} from '../../redux/LeaderBoard/LeaderBoardSlice';
import {
  getJoinedLeaderBoard,
  getLeaderBoardCompleted,
} from '../../redux/LeaderBoard/LeaderBoardAction';
import {MaterialTabBar, Tabs} from 'react-native-collapsible-tab-view';
import {useFocusEffect} from '@react-navigation/native';
import analytics from '@react-native-firebase/analytics';
import {set} from 'react-native-reanimated';

let bottomLoad = false;
export function LeaderboardHomeScreen({navigation, route}: any) {
  const leaderBoardList = useAppSelector(
    state => state.leaderBoard.leaderBoardList.data,
  );
  const nextPageActiveLeaderBoard = useAppSelector(
    state => state.leaderBoard.leaderBoardList.nextPageToken,
  );
  const hasNextActiveLeaderBoard = useAppSelector(
    state => state.leaderBoard.leaderBoardList.hasNext,
  );
  const [loading, setLoading] = useState(false);
  const leaderBoardCompletedList = useAppSelector(
    state => state.leaderBoard.leaderBoardCompletedList.data,
  );
  const nextPageCompletedLeaderBoard = useAppSelector(
    state => state.leaderBoard.leaderBoardCompletedList.nextPageToken,
  );
  const hasNextCompletedLeaderBoard = useAppSelector(
    state => state.leaderBoard.leaderBoardCompletedList.hasNext,
  );
  const tabRef = useRef<any>(null);
  const dispatch = useAppDispatch();
  const authState = useAppSelector(state => state.auth);
  const [refreshing, setRefreshing] = useState(false);
  const [isActiverefreshing, setActiveRefreshing] = useState(false);
  const {userId} = authState;
  const warmColors = [
    '#A7C7E7', '#B0D0E8', '#BAD9EF', '#C2DFF1',
    '#CDE6F4', '#D0E7F5', '#DAEEF8', '#E0F0FA',
    '#E8F6FD', '#F0FAFF'
  ];

  useEffect(() => {
    analytics().logScreenView({
      screen_name: 'LeaderboardListScreen',
      screen_class: 'LeaderboardListScreen',
    });
  }, []);

  useEffect(() => {
    if (route.params && route.params?.active && tabRef.current) {
      tabRef.current?.setIndex(0);
    }
  }, [route.params, tabRef.current]);

  useFocusEffect(
    React.useCallback(() => {
      dispatch(getJoinedLeaderBoard({userId: userId}));
      dispatch(getLeaderBoardCompleted({userId: userId}));
    }, [userId, dispatch]),
  );

  const renderLeaderboardItem = ({
    item,
    index,
  }: {
    item: LeaderBoard;
    index: number;
  }) => (
    <TouchableOpacity
      style={[
        styles.leaderboardItem,
        {
          backgroundColor: warmColors[index % warmColors.length],
        },
      ]}
      onPress={() => handleLeaderboardPress(item)}>
      <Text style={[styles.leaderboardTitle, {marginBottom: 5}]}>
        {item.title || 'ChallengeTitle'}
      </Text>
      {false && <Text style={styles.leaderBoardPosition}>{`Pos: 2/15`}</Text>}
      <Text
        style={
          styles.leaderBoardParticipant
        }>{`${item.pointsTitle}: ${item.points}`}</Text>
    </TouchableOpacity>
  );

  const renderSeparator = () => {
    return <View style={styles.separator} />;
  };

  const handleLeaderboardPress = (item: LeaderBoard) => {
    navigation.push('LeaderboardDetails', {
      name: item.pointsTitle,
      creator: item.userId,
      challengeId: item?.challengeId,
      isGoLeaderBoardTab: true,
    });
  };
  const onActiveLeaderBoardEnd = () => {
    if (
      leaderBoardList.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasNextActiveLeaderBoard
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getJoinedLeaderBoard({
          userId: userId,
          nextPageToken: nextPageActiveLeaderBoard,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  };

  const onCompletedLeaderBoardEnd = () => {
    if (
      leaderBoardCompletedList.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasNextCompletedLeaderBoard
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getLeaderBoardCompleted({
          userId: userId,
          nextPageToken: nextPageCompletedLeaderBoard,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  };

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onRefresh = () => {
    setRefreshing(true);
    dispatch(getLeaderBoardCompleted({userId: userId})).finally(() => {
      setRefreshing(false);
    });
  };

  const onActiveRefresh = () => {
    setActiveRefreshing(true);
    dispatch(getJoinedLeaderBoard({userId: userId})).finally(() => {
      setActiveRefreshing(false);
    });
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
      {/* 1. Header Section */}
      <View style={[styles.HeaderStyle]}>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>Leaderboard</Text>
        </View>
      </View>
      {/* <View style={styles.positionContainer}>
        <Text style={styles.leaderboardTitle}>Highest Position</Text>
        <Text style={styles.rankText}>Rank 2</Text>
        <Text style={styles.leaderboardTitle}>Ice Bucket Challenge</Text>
      </View> */}
      <Tabs.Container
        // @ts-ignore
        renderHeader={null}
        ref={tabRef}
        renderTabBar={(props: any) => (
          <MaterialTabBar
            {...props}
            indicatorStyle={{backgroundColor: '#C3E7F5', height: 5}}
          />
        )}
        headerContainerStyle={{shadowColor: 'transparent'}}
        pagerProps={{
          scrollEnabled: false,
        }}>
        <Tabs.Tab
          name="ActiveTab"
          label={() => <Text style={styles.lableStyle}>Active</Text>}>
          <FlatList
            data={leaderBoardList}
            renderItem={renderLeaderboardItem}
            ListFooterComponent={renderFooter}
            onEndReachedThreshold={0.1}
            onEndReached={onActiveLeaderBoardEnd}
            initialNumToRender={1}
            windowSize={1}
            style={{
              minHeight: Dimensions.get('window').height,
            }}
            refreshing={isActiverefreshing}
            onRefresh={onActiveRefresh}
            contentContainerStyle={{paddingTop: 10}}
            ListHeaderComponent={() => <View style={styles.hiddenUI} />}
            ListEmptyComponent={() => {
              return (
                <View style={styles.emptyView}>
                  <Text>No active leaderboards found.</Text>
                </View>
              );
            }}
            keyExtractor={item => item.challengeId}
            ItemSeparatorComponent={renderSeparator}
            showsVerticalScrollIndicator={false} // To disable the scrollbar
          />
        </Tabs.Tab>
        <Tabs.Tab
          name="CompletedTab"
          label={() => <Text style={styles.lableStyle}>Completed</Text>}>
          <FlatList
            data={leaderBoardCompletedList}
            ListFooterComponent={renderFooter}
            onEndReachedThreshold={0.1}
            onEndReached={onCompletedLeaderBoardEnd}
            initialNumToRender={1}
            windowSize={1}
            style={{
              minHeight: Dimensions.get('window').height,
            }}
            refreshing={refreshing}
            onRefresh={onRefresh}
            renderItem={renderLeaderboardItem}
            contentContainerStyle={{paddingTop: 10}}
            ListEmptyComponent={() => {
              return (
                <View style={styles.emptyView}>
                  <Text>No completed leaderboards found.</Text>
                </View>
              );
            }}
            ListHeaderComponent={() => <View style={styles.hiddenUI} />}
            keyExtractor={item => item.challengeId}
            ItemSeparatorComponent={renderSeparator}
            showsVerticalScrollIndicator={false} // To disable the scrollbar
          />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
}
const styles = StyleSheet.create({
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyView: {
    width: '100%',
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  lableStyle: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  hiddenUI: {
    height: 50, // Height of the hidden UI
    backgroundColor: '#ccc', // Background color of hidden UI
    opacity: 0, // Fully hide the UI
  },
  tabBarIndicator: {
    backgroundColor: '#C3E7F5',
    height: 4,
  },
  tabBarStyle: {
    backgroundColor: '#fff',
  },
  positionContainer: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 10,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Platform.OS == 'android' ? 10 : 0,
  },
  container: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    flex: 1,
  },
  leaderboardItem: {
    padding: 12,
    marginHorizontal: 16,
    borderRadius: 10,
  },
  leaderBoardParticipant: {
    marginTop: 10,
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  leaderBoardPosition: {
    alignSelf: 'flex-end',
    marginTop: 10,
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  rankText: {
    fontSize: 24,
    lineHeight: 30,
    fontWeight: '700',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  leaderboardTitle: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: '300',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  leaderboardRank: {
    fontSize: 14,
    color: 'gray',
    marginTop: 4,
    fontFamily: 'Helvetica Neue',
  },
  separator: {
    marginVertical: 6,
  },
});

export default LeaderboardHomeScreen;
