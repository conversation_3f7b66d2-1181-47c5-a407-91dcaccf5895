import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import {TabRouter} from '@react-navigation/routers';
import LeaderboardHomeScreen from './LeaderboardHomeScreen';
import {LeaderboardDetailsScreen} from '../LeaderboardDetailsScreen/LeaderboardDetailsScreen';
import LeaderboardEditScreen from './LeaderboardEditScreen';
import ChallengeDetailsScreen from '../ChallengeDetailsScreen/ChallengeDetailsScreen';
import ChallengeUpdateScreen from '../ChallengeHomeScreen/ChallengeUpdateScreen';
import CalendarScreen from '../Common/CalendarScreen';
import ChallengeParticipant from '../ChallengeDetailsScreen/ChallengeParticipant';
import UserProfileScreen from '../UserProfileScreen/UserProfileScreen';
import UserConnectionsScreen from '../UserProfileScreen/UserConnectionsScreen';
import UserActivity from '../UserProfileScreen/UserActivity';
import UserPostsScreen from '../UserProfileScreen/UserPostsScreen';

const leaderboardStack = createStackNavigator();

export function LeaderboardHomeScreenStack() {
  return (
    <leaderboardStack.Navigator>
      <leaderboardStack.Screen
        name="LeaderboardHome"
        component={LeaderboardHomeScreen}
        options={{
          title: 'Leaderboard',
          headerShown: false,
        }}
      />
      <leaderboardStack.Screen
        name="LeaderboardDetails"
        component={LeaderboardDetailsScreen}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />
      <leaderboardStack.Screen
        name="LeaderboardEdit"
        component={LeaderboardEditScreen}
        options={({route}: any) => ({title: route.params?.name})}
      />
      <leaderboardStack.Screen
        name="ChallengeDetails"
        component={ChallengeDetailsScreen}
        options={({route}) => ({title: 'Challenge', header: () => null})}
      />
      <leaderboardStack.Screen
        name="ChallengeParticipant"
        component={ChallengeParticipant}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <leaderboardStack.Screen
        name="ChallengeUpdate"
        component={ChallengeUpdateScreen}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <leaderboardStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{headerShown: false}}
      />
      <leaderboardStack.Screen
        name="LeaderboardDetailsScreen"
        component={LeaderboardDetailsScreen}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />

      <leaderboardStack.Screen
        name="OtherUserProfileScreen"
        component={UserProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <leaderboardStack.Screen
        name="UserConnectionsScreen"
        component={UserConnectionsScreen}
        options={({navigation}) => ({
          title: 'Connections',
          header: () => null,
        })}
      />
      <leaderboardStack.Screen
        name="UserActivity"
        component={UserActivity}
        options={{
          headerShown: false,
        }}
      />
      <leaderboardStack.Screen
        name="UserPostsScreen"
        component={UserPostsScreen}
        options={() => ({
          title: 'Posts',
          header: () => null,
        })}
      />
    </leaderboardStack.Navigator>
  );
}

export default LeaderboardHomeScreenStack;
