import React, {useState, useEffect} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  Image,
} from 'react-native';
import LeaderboardTableComponent from './LeaderboardTableComponent';
import Icon from 'react-native-vector-icons/SimpleLineIcons';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {
  deleteLeaderBoard,
  getLeaderBoardById,
} from '../../redux/LeaderBoard/LeaderBoardAction';
import {LeaderBoardDetails} from '../../redux/LeaderBoard/LeaderBoardSlice';
import {CommonActions} from '@react-navigation/native';
import {updateChallenge} from '../../redux/Challenge/ChallengeAction';
import analytics from '@react-native-firebase/analytics';
import {baseUrl} from '../../utils/Utils';

let bottomLoading = false;
export function LeaderboardDetailsScreen({navigation, route}: any) {
  const [isLoading, setIsLoading] = useState(true);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false); // Dropdown visibility state
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [leaderBoardData, setLeaderBoardData] =
    useState<LeaderBoardDetails | null>(null);
  const [pointsColumnTitle, setPointsColumnTitle] = useState('');
  const [nextPageToken, setNextPageToken] = useState('');
  const [isNext, setIsNext] = useState(true);
  const userState = useAppSelector(state => state.auth);
  const {userId} = userState;
  const creator = route.params.creator;
  const challengeId = route.params.challengeId;
  const dispatch = useAppDispatch();
  const currentUserId = useAppSelector(state => state.auth.userId);
  const token = useAppSelector(state => state.auth.token);  
  useEffect(() => {
    if (leaderBoardData && leaderBoardData.challengeTitle) {
      analytics().logScreenView({
        screen_name: 'LeaderboardDetailsScreen',
        screen_class: 'LeaderboardDetailsScreen',
        challengeTitle: leaderBoardData.challengeTitle,
        pointsTitle: leaderBoardData.pointsTitle,
      });
    }
  }, [leaderBoardData]);

  // Simulate API call to fetch leaderboard data
  useEffect(() => {
    const fetchLeaderboardData = async () => {
      const result = await dispatch(
        getLeaderBoardById({challengeId: challengeId}),
      );
      if (getLeaderBoardById.fulfilled.match(result)) {
        setNextPageToken(result?.payload?.nextPageToken);
        if (
          result?.payload?.data?.length > 0 &&
          result?.payload?.data[0].challengeId
        ) {
          setLeaderBoardData(result.payload.data[0]);
          setPointsColumnTitle(result.payload.data[0].pointsTitle);
        } else {
          Alert.alert('Failed to get leaderboard.');
        }
      } else if (getLeaderBoardById.rejected.match(result)) {
        Alert.alert('Failed to get leaderboard.');
      }
      setIsLoading(false);
    };
    if (challengeId) {
      fetchLeaderboardData();
    }
  }, [challengeId]);

  // Handle "Edit" action
  const handleEditPress = () => {
    setIsDropdownVisible(false); // Close dropdown
    setIsEditModalVisible(true); // Open edit modal
  };

  // Handle "Delete" action
  const handleDeletePress = () => {
    setIsDropdownVisible(false); // Close dropdown
    Alert.alert(
      'Delete Leaderboard',
      'Are you sure you want to delete this leaderboard?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            const challengeValue = {
              challengeId: challengeId,
              userId: userId,
            };
            setIsLoading(true);
            const result = await dispatch(deleteLeaderBoard(challengeValue));
            console.log(result, 'resultresultresult');
            if (result.payload.code == 200) {
              navigation.goBack();
            } else {
              Alert.alert('Failed to delete leaderboard.');
            }
            setIsLoading(true); // Navigate back after deletion
          },
          style: 'destructive',
        },
      ],
    );
  };

  // Close modal for edit
  const handleEditModalClose = async () => {
    setIsLoading(true);
    setIsEditModalVisible(false);
    const challengeValue = {
      challengeId: challengeId,
      isLeaderboardCreated: true,
      pointsTitle: pointsColumnTitle,
    };
    await dispatch(updateChallenge(challengeValue));
    const result = await dispatch(
      getLeaderBoardById({challengeId: challengeId}),
    );
    if (getLeaderBoardById.fulfilled.match(result)) {
      setNextPageToken(result?.payload?.nextPageToken);
      if (
        result?.payload?.data?.length > 0 &&
        result?.payload?.data[0].challengeId
      ) {
        setLeaderBoardData(result.payload.data[0]);
        setPointsColumnTitle(result.payload.data[0].pointsTitle);
      } else {
        Alert.alert('Failed to create leaderboard.');
      }
    } else if (getLeaderBoardById.rejected.match(result)) {
      Alert.alert('Failed to create leaderboard.');
    }
    setIsLoading(false);
  };

  const onBackPress = () => {
    const resetToLeaderboard = () => {
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            {
              name: 'BottomTabsNavigator',
              state: {
                routes: [{name: 'Leaderboard'}],
              },
            },
          ],
        }),
      );
    };

    if (route.params?.isGoLeaderBoardTab || !navigation.canGoBack()) {
      // resetToLeaderboard();
      navigation.pop();
    } else {
      navigation.goBack();
    }
  };

  const onChallengeTitlePress = () => {
    navigation.navigate('ChallengeDetails', {
      challengeId: leaderBoardData?.challengeId,
    });
  };

  const onUserNameClick = () => {
    if (currentUserId == leaderBoardData?.challengeCreatedBy.userId) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('OtherUserProfileScreen', {
        userId: leaderBoardData?.challengeCreatedBy.userId,
      });
    }
  };

  const onEndReached = async () => {
    if (!leaderBoardData) {
      return;
    }
    if (!isNext) {
      return;
    }
    if (bottomLoading) {
      return;
    }
    bottomLoading = true;
    const result = await dispatch(
      getLeaderBoardById({
        challengeId: challengeId,
        nextPageToken: nextPageToken,
      }),
    );
    bottomLoading = false;
    if (getLeaderBoardById.fulfilled.match(result)) {
      setNextPageToken(result?.payload?.nextPageToken);
      if (
        result?.payload?.data?.length > 0 &&
        result?.payload?.data[0].challengeId &&
        result.payload.data[0].userPointsList &&
        result.payload.data[0].userPointsList.length > 0 &&
        leaderBoardData
      ) {
        const leaderBoardDataList = [
          ...leaderBoardData.userPointsList,
          ...result.payload.data[0].userPointsList,
        ];
        const oldData = JSON.parse(JSON.stringify(leaderBoardData));
        oldData.userPointsList = leaderBoardDataList;
        setIsNext(result.payload.data[0].userPointsList.length == 10);
        setLeaderBoardData(oldData);
        setPointsColumnTitle(result.payload.data[0].pointsTitle);
      }
    } else if (getLeaderBoardById.rejected.match(result)) {
      Alert.alert('Failed to get leaderboard.');
    }
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <View style={{paddingHorizontal: 16, paddingTop: 16}}>
        {/* Header Section */}
        <View style={[styles.HeaderStyle]}>
          <TouchableOpacity
            style={[
              acrossAllScreens.backImageContainer,
              styles.HeaderbackButton,
            ]}
            onPress={onBackPress}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <View style={styles.HeaderHorizontalPosition}>
            <Text style={[acrossAllScreens.ScreenHeaderText]}>Leaderboard</Text>
          </View>
        </View>

        {/* Title and Options */}
        <View style={styles.userInfoContainer}>
          <Image
            style={styles.userImage}
            source={
              leaderBoardData?.challengeCreatedBy?.imageReference
                ? {
                    uri: leaderBoardData?.challengeCreatedBy?.imageReference,
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }
                : require('../../static/Images/user.png')
            }
          />
          <View style={styles.userTextContainer}>
            <View style={styles.titleContainer}>
              <TouchableOpacity onPress={onChallengeTitlePress}>
                <Text style={styles.LeaderboardTitleStyle}>
                  {leaderBoardData?.challengeTitle}
                </Text>
              </TouchableOpacity>
              {/* Only show the options icon if current user is the creator */}
              {userId === creator && (
                <TouchableOpacity
                  onPress={() => setIsDropdownVisible(!isDropdownVisible)}>
                  <Image
                    source={require('../../assets/images/more.png')}
                    resizeMode="contain"
                    style={styles.moreIcon}
                  />
                </TouchableOpacity>
              )}
            </View>
            <TouchableOpacity onPress={onUserNameClick}>
              <Text numberOfLines={1} style={styles.SubHeaderStyle}>
                {leaderBoardData?.challengeCreatedBy.firstName +
                  ' ' +
                  leaderBoardData?.challengeCreatedBy.lastName}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* <View style={styles.separator} /> */}

      {/* Render Spinner or Leaderboard Data */}
      {isLoading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        leaderBoardData?.userPointsList && (
          <LeaderboardTableComponent
            pointTitle={leaderBoardData.pointsTitle}
            onEndReached={onEndReached}
            leaderboardData={
              leaderBoardData
                ? [...leaderBoardData?.userPointsList].sort(
                    (a, b) => b.points - a.points,
                  )
                : []
            }
          />
        )
      )}

      {/* Dropdown Menu */}
      {isDropdownVisible && (
        <View style={styles.dropdownMenu}>
          <TouchableOpacity
            onPress={handleEditPress}
            style={styles.dropdownItem}>
            <Text style={styles.dropdownText}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleDeletePress}
            style={styles.dropdownItem}>
            <Text style={styles.dropdownText}>Delete</Text>
          </TouchableOpacity>
        </View>
      )}

      <Modal
        visible={isEditModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsEditModalVisible(false)}>
        <TouchableWithoutFeedback
          onPress={() => {
            setIsEditModalVisible(false);
          }}>
          <View style={styles.modalBackground}>
            <TouchableWithoutFeedback onPress={() => {}}>
              <View style={styles.modalContainer}>
                <Text style={styles.modalTitle}>
                  Choose leaderboard scoring system
                </Text>
                <View style={styles.modalContent}>
                  <TextInput
                    style={styles.pointsColumnTitleInput}
                    value={pointsColumnTitle}
                    onChangeText={setPointsColumnTitle}
                    placeholder="Enter Points Column Title*"
                    placeholderTextColor="grey"
                  />
                  <View style={styles.modalSeparator} />
                  <TouchableHighlight
                    style={styles.closeButton}
                    onPress={handleEditModalClose}>
                    <Text style={styles.closeButtonText}>
                      Update Leaderboard
                    </Text>
                  </TouchableHighlight>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  userTextContainer: {
    flex: 1,
  },
  userImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 10,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  moreIcon: {
    width: 20,
    height: 20,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  LeaderboardTitleStyle: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: 'Helvetica Neue',
    color: 'black',
  },
  SubHeaderStyle: {
    fontSize: 14,
    fontWeight: '700',
    fontFamily: 'Helvetica Neue',
    marginBottom: 10,
    color: 'black',
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  separator: {
    height: 4,
    backgroundColor: 'black',
    width: '100%',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  dropdownMenu: {
    position: 'absolute',
    top: 80, // Adjust this based on your UI
    right: 20, // Adjust this based on your UI
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  dropdownText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
  },
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
  },
  modalContent: {
    marginVertical: 10,
  },
  modalTitle: {
    fontSize: 18,
    marginBottom: 20,
    fontFamily: 'Helvetica Neue',
  },
  pointsColumnTitleInput: {
    fontSize: 14,
    fontFamily: 'Helvetica Neue',
    color: '#000',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  closeButton: {
    backgroundColor: '#c3e7f5',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  closeButtonText: {
    color: '#000000',
    fontWeight: 'bold',
    fontSize: 18,
  },
  modalSeparator: {
    height: 1,
    backgroundColor: '#d3d3d3',
    marginVertical: 10,
  },
});

export default LeaderboardDetailsScreen;
