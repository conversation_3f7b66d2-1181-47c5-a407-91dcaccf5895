import React, {useState, useCallback, useMemo} from 'react';
import {Text, View, StyleSheet, FlatList, TouchableOpacity} from 'react-native';
import {UserPointsList} from '../../redux/LeaderBoard/LeaderBoardSlice';
import {useAppSelector} from '../../redux/Store';
import {useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';

export function LeaderboardTableComponent({
  leaderboardData,
  pointTitle,
  onEndReached,
}: {
  leaderboardData: UserPointsList[];
  pointTitle: string;
  onEndReached: () => void;
}) {
  const userState = useAppSelector(state => state.auth);
  const navigation = useNavigation<any>();
  const {userId} = userState;

  const currentUserIndex = useMemo(() => {
    return leaderboardData.findIndex(item => item.userId === userId);
  }, [leaderboardData, userId]);

  const [isFooterVisible, setFooterVisible] = useState(false);

  const handleViewableItemsChanged = useCallback(({viewableItems}: any) => {
    const footerIndex = currentUserIndex - 1;
    const isFooterInView = viewableItems.some(
      (item: any) => item.index === footerIndex,
    );
    setFooterVisible(
      !isFooterInView &&
        viewableItems &&
        viewableItems.length > 0 &&
        viewableItems[0].index < footerIndex,
    ); // Hide footer when it’s in the visible list
  }, []);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 100,
  };

  return (
    <View style={{flex: 1, paddingHorizontal: 16}}>
      {/* Table Header */}
      <View style={styles.tableHeader}>
        <View style={styles.rankColumn}>
          <Text style={styles.headerText}></Text>
        </View>
        <View style={styles.userColumn}>
          <Text style={styles.headerText}></Text>
        </View>
        <View style={styles.pointsTitleColumn}>
          <Text style={styles.headerText}>{pointTitle}</Text>
        </View>
      </View>

      {/* Table Rows */}
      <FlatList
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{flexGrow: 1}}
        data={leaderboardData}
        stickyHeaderIndices={[currentUserIndex]} // Sticky header index
        keyExtractor={item => item.userId}
        renderItem={({item, index}) => {
          const isCurrentUser = item.userId === userId; // Highlight the row if it's the current user
          return (
            <LinearGradient
              style={[styles.tableRow]}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              colors={
                index == 0
                  ? ['#FFCC45', '#D99D00']
                  : index == 1
                  ? ['#C0C0C0', '#E2E2E2']
                  : index == 2
                  ? ['#F2BA7A', '#AE492B']
                  : isCurrentUser
                  ? ['#C3E7F5', '#C3E7F5']
                  : ['#FFFFFF', '#FFFFFF']
              }>
              <View style={styles.rankColumn}>
                <Text style={index < 2 ? styles.rowTextBig : styles.rowText}>
                  {index + 1}
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  if (userId == item.userId) {
                    navigation.navigate('Profile');
                  } else {
                    navigation.navigate('OtherUserProfileScreen', {
                      userId: item.userId,
                    });
                  }
                }}
                style={styles.userColumn}>
                <Text
                  style={index < 2 ? styles.rowTextBig : styles.rowText}
                  numberOfLines={1}>
                  {item.firstName + ' ' + item.lastName}
                </Text>
              </TouchableOpacity>
              <View style={styles.pointsTitleColumn}>
                <Text style={index < 2 ? styles.rowTextBig : styles.rowText}>
                  {item.points}
                </Text>
              </View>
            </LinearGradient>
          );
        }}
        onEndReachedThreshold={0.1}
        onEndReached={onEndReached}
        onViewableItemsChanged={handleViewableItemsChanged} // Track visible items
        viewabilityConfig={viewabilityConfig} // Configuration for visibility detection
      />

      {/* Sticky Footer */}
      {isFooterVisible && (
        <View
          style={[
            styles.tableRow,
            {
              backgroundColor: '#F1C34B'
            },
          ]}>
          <View style={styles.rankColumn}>
            <Text style={styles.rowText}>{currentUserIndex + 1}</Text>
          </View>
          <TouchableOpacity style={styles.userColumn}>
            <Text style={styles.rowText}>
              {leaderboardData[currentUserIndex].firstName +
                ' ' +
                leaderboardData[currentUserIndex].lastName}
            </Text>
          </TouchableOpacity>
          <View style={styles.pointsTitleColumn}>
            <Text style={styles.rowText}>
              {leaderboardData[currentUserIndex].points}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  tableHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    color: 'black',
    paddingTop: 5,
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: 'Helvetica Neue',
  },
  rankColumn: {
    flex: 2.5,
    alignItems: 'center',
  },
  userColumn: {
    flex: 8,
    paddingLeft: 10,
  },
  pointsTitleColumn: {
    flex: 5,
    alignItems: 'center',
    marginBottom: 4,
  },
  tableRow: {
    flexDirection: 'row',
    height: 42,
    alignItems: 'center',
    marginVertical: 4,
  },
  rowTextBig: {
    color: 'black',
    fontSize: 24,
    fontWeight: '400',
    fontFamily: 'Helvetica Neue',
  },
  rowText: {
    color: 'black',
    fontSize: 20,
    fontWeight: '400',
    fontFamily: 'Helvetica Neue',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#6200ee',
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default LeaderboardTableComponent;
