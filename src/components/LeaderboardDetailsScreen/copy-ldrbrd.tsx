import React, {useState} from 'react';
import {Text, View, StyleSheet, FlatList} from 'react-native';

export function LeaderboardDetailsScreen({route}) {
  const boardDetails = require('../../static/LeaderboardContent/LeaderboardDetailContentFriends.json');
  return (
    <View>
      <Text style={styles.HeaderStyle}>{route.params.name}</Text>
      <View
        style={{
          marginStart: 5,
          marginEnd: 5,
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        {boardDetails.DataFormat.map(formatId => {
          return <Text key={formatId.id}>{formatId.id}</Text>;
        })}
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={boardDetails.Data}
        keyExtractor={key => key.userName}
        renderItem={({item}) => {
          return (
            <View
              style={{
                marginStart: 5,
                marginEnd: 5,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text>{item.rank}</Text>
              <Text>{item.userName}</Text>
              <Text>{item.score}</Text>
            </View>
          );
        }}
      />
      {/* <Text>{route.params.content.Data[0].userName}</Text> */}
    </View>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginStart: 10,
    marginBottom: 10,
    marginTop: 5,
  },
});

export default LeaderboardDetailsScreen;
