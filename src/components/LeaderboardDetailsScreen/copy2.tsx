import React, {useState} from 'react';
import {Text, View, StyleSheet, FlatList, TouchableOpacity} from 'react-native';

export function LeaderboardDetailsScreen({route}) {
  const boardDetails = require('../../static/LeaderboardContent/LeaderboardDetailContentFriends.json');
  let dataFormat = boardDetails.DataFormat;
  let creator = boardDetails.Creator[0].userName;

  return (
    <View>
      <Text style={styles.HeaderStyle}>{route.params.name}</Text>
      <Text>{creator}</Text>

      <View
        style={{
          marginStart: 5,
          marginEnd: 5,
          flexDirection: 'row',
        }}>
        {/* {boardDetails.DataFormat.map(formatId => {
          return (
            <View style={{flex: formatId.flex}}>
              <Text key={formatId.id}>{formatId.id}</Text>
            </View>
          );
        })} */}
        <View
          style={{
            flex: 2.5,
            alignItems: 'center',
          }}>
          <Text>{dataFormat[0].id}</Text>
        </View>
        <Text style={{flex: 1}} />
        <Text style={{flex: 8}}>{dataFormat[1].id}</Text>
        <View
          style={{
            flex: 5,
            alignItems: 'center',
          }}>
          <Text>{dataFormat[2].id}</Text>
        </View>
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={boardDetails.Data}
        keyExtractor={key => key.userName}
        renderItem={element => {
          return (
            <View
              style={{
                marginStart: 5,
                marginEnd: 5,
                flexDirection: 'row',
                paddingTop: 5,
                paddingBottom: 5,
                backgroundColor: element.index % 2 ? '#8ed3f5' : '#ebf2f5',
              }}>
              <View
                style={{
                  flex: 2.5,
                  alignItems: 'center',
                }}>
                <Text>{element.item.rank}</Text>
              </View>
              <View style={{flex: 1}}>
                <Text />
              </View>
              <TouchableOpacity
                style={{flex: 8}}
                onPress={() => {
                  console.log(element.item.userName);
                }}>
                <Text>{element.item.userName}</Text>
              </TouchableOpacity>
              <View style={{flex: 5, alignItems: 'center'}}>
                <Text>{element.item.score}</Text>
              </View>
            </View>
          );
        }}
      />
      {/* <Text>{route.params.content.Data[0].userName}</Text> */}
    </View>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginStart: 10,
    marginBottom: 10,
    marginTop: 5,
  },
});

export default LeaderboardDetailsScreen;
