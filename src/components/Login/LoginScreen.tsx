import React, {useState, useEffect} from 'react';
import {
  View,
  TextInput,
  TouchableHighlight,
  TouchableOpacity,
  Text,
  TouchableWithoutFeedback,
  Keyboard,
  Button,
  Image,
} from 'react-native';

import {SignUpStyle} from '../../styles/SignUp';
import {CommonFontstyles} from '../../styles/CommonFontStyles';
import {acrossAllScreens} from '../../styles/acrossAllScreens';
import HomeFeedScreenStack from '../HomeScreen/HomeFeedScreenStack';
import userLogin from '../../apis/userLogin';
import {login} from '../../redux/Auth/AuthAction';
import {useDispatch, useSelector} from 'react-redux';
import {useAppSelector} from '../../redux/Store';
import messaging from '@react-native-firebase/messaging';
import { addFcmTokem } from '../../redux/User/UserAction';
// type LoginToken = {};
export function LoginScreen({navigation}: any) {
  const [userName, setUserName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  let values: any;

  // const userLoginApi = async () => {
  //   console.log(userName, email, password);
  //   try {
  //     const response = await userLogin.post('/api/users/session', {
  //       email: email,
  //       password: password,
  //       //   password: 'someRandomSports4',
  //     });
  //     const res = response.data;
  //     console.log(res);
  //     navigation.navigate('UserProfileScreen');
  //   } catch (error) {
  //     console.log(error);
  //     setErrorMessage('Incorrect username / password. Please try again!');
  //   }
  // };
  const dispatch = useDispatch();
  const authState = useAppSelector(state => state.auth);

  const {user, isError, isLoading, isLoginSuccess, isLoginStart} = authState;

  // const getToken = async () => {
  //   try {
  //     const token = await AsyncStorage.getItem('token');
  //     console.log('Token value:', token);
  //     return token;
  //   } catch (error) {
  //     console.log('Error getting token:', error);
  //     return null;
  //   }
  // };
  // Validation helper functions
  const isFieldEmpty = (field: any) => field.trim() === '';
  const isValidEmail = (email: any) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  // Validation functions for each field
  const validateEmail = () => {
    if (!isValidEmail(email)) {
      setEmailError('*Please enter a valid email address.');
      return false;
    }
    setEmailError('');
    return true;
  };

  const validatePassword = () => {
    if (isFieldEmpty(password)) {
      setPasswordError('*Password is required.');
      return false;
    }
    setPasswordError('');
    return true;
  };
  const validateAllFields = () => {
    const isEmailValid = validateEmail();
    const isPasswordValid = validatePassword();

    return isEmailValid && isPasswordValid;
  };

  const submitToken = async () => {
    const token = await messaging().getToken();
    console.log(token, 'fcm tokentokentoken');
    const payload = {
      deviceToken: token,
      isNotificationsEnabled: true,
    };
    dispatch(addFcmTokem(payload))
  };

  useEffect(() => {
    if (isLoginSuccess) {
      console.log('1 its a Login success', authState, isLoginSuccess);
      // getToken()
      // navigation.navigate('UserProfileScreen'); // to Register

      //navigation.navigate('BottomTabsNavigator', {screen: 'Home'});
      submitToken();
      navigation.reset({
        index: 0,
        // routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
        routes: [{name: 'WelcomeStack', params: {screen: 'TutorialScreen1'}}],
      });
    } else if (isLoginStart) {
      console.log('First run');
    } else {
      console.log('2 not a Loginsucesss', authState, isLoginSuccess);
      // setErrorMessage('Invalid Email or Password. Please try again!');
    }
  }, [isLoginSuccess, isLoginStart]);
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <View
        style={[SignUpStyle.loginContainer, acrossAllScreens.ScreenBackground]}>
        <Image
          style={[acrossAllScreens.logoImage, {marginBottom: 30}]}
          source={require('../../assets/images/napoz.png')}
        />
        <View style={SignUpStyle.inputWithErrorContainer}>
          <View style={SignUpStyle.inputContainer}>
            <TextInput
              style={SignUpStyle.inputs}
              placeholder="Email *"
              placeholderTextColor="grey"
              keyboardType="email-address"
              autoCapitalize="none"
              underlineColorAndroid="transparent"
              autoCapitalize="none" // Prevents automatic capitalization
              autoCorrect={false} // Disables auto-correction
              onChangeText={text => setEmail(text.toLowerCase())}
            />
          </View>
          {emailError ? (
            <Text style={acrossAllScreens.ErrorText}>{emailError}</Text>
          ) : null}
        </View>
        <View style={SignUpStyle.inputWithErrorContainer}>
          <View style={SignUpStyle.inputContainer}>
            <TextInput
              style={SignUpStyle.inputs}
              placeholder="Password *"
              placeholderTextColor="grey"
              secureTextEntry={true}
              underlineColorAndroid="transparent"
              onChangeText={text => setPassword(text)}
            />
          </View>
          {passwordError ? (
            <Text style={acrossAllScreens.ErrorText}>{passwordError}</Text>
          ) : null}
        </View>

        <TouchableOpacity
          onPress={() => {
            console.log('Forgot Password');
            navigation.push('ForgotPassword');
          }}>
          <Text style={[acrossAllScreens.H2, {paddingBottom: 78}]}>
            Forgot Password
          </Text>
        </TouchableOpacity>

        {errorMessage ? (
          <Text style={CommonFontstyles.Napoz}>{errorMessage}</Text>
        ) : null}
        <TouchableHighlight
          style={[SignUpStyle.signupButton]}
          onPress={() => {
            console.log('Login pressed');
            if (validateAllFields()) {
              values = {
                email: email,
                password: password,
              };
              //userLoginApi();
              console.log(values);
              dispatch(login(values));
            } else console.log('Missing Fields for signup');
          }}>
          <Text style={SignUpStyle.signUpText}>Log In</Text>
        </TouchableHighlight>
        <TouchableOpacity
          style={SignUpStyle.screenRedirectButton}
          onPress={() => {
            console.log('Signup Screen');
            navigation.push('SignUpScreen');
          }}>
          <Text style={[acrossAllScreens.H2]}>New to Napoz?</Text>
          <Text style={[acrossAllScreens.H3]}> Sign Up here!</Text>
        </TouchableOpacity>
        {false && (
          <Button
            title="Backdoor access"
            onPress={() => {
              navigation.reset({
                index: 0,
                routes: [
                  {name: 'WelcomeStack', params: {screen: 'TutorialScreen1'}},
                ],
              });
            }}
          />
        )}
      </View>
    </TouchableWithoutFeedback>
  );
}

export default LoginScreen;
