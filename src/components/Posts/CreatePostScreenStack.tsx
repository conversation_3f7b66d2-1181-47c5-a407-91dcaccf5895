import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import CalendarScreen from '../Common/CalendarScreen';
import CreatePostScreen from './CreatePostScreen';
import UpdatePostScreen from './UpdatePostScreen';
import IntegrationsScreen from '../Common/IntegrationsScreen';

const createPostStack = createStackNavigator();

export function CreatePostScreenStack() {
  return (
    <createPostStack.Navigator>
      <createPostStack.Screen
        name="CreatePostScreen"
        component={CreatePostScreen}
        options={{
          title: 'Create Post',
          headerShown: false,
        }}
      />
      <createPostStack.Screen
        name="UpdatePostScreen"
        component={UpdatePostScreen}
        options={{
          title: 'Update Post',
          headerShown: false,
        }}
      />
      <createPostStack.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{headerShown: false}}
      />
      <createPostStack.Screen
       name="IntegrationsScreen"
               component={IntegrationsScreen}
               options={{headerShown: false}}
      />
    </createPostStack.Navigator>
  );
}

export default CreatePostScreenStack;
