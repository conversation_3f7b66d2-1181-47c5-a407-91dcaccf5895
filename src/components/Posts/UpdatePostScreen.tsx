import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  Text,
  View,
  FlatList,
  TouchableOpacity,
  TouchableHighlight,
  StyleSheet,
  TextInput,
  Image,
  Platform,
  Dimensions,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Alert,
  Keyboard,
} from 'react-native';
import Video from 'react-native-video';
import Modal from 'react-native-modal';


// import Icon from 'react-native-vector-icons/Feather';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import FonteAwesome from 'react-native-vector-icons/FontAwesome';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {useDispatch} from 'react-redux';
import acrossAllScreens from '../../styles/acrossAllScreens';
import SignUpStyle from '../../styles/SignUp';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  updatePost,
  addPostMedia,
  getPostById,
  deletePostMedia,
  unlinkPostFromChallenge,
  updateUserPost,
} from '../../redux/Post/PostAction';
import {getChallengeFeed} from '../../redux/Challenge/ChallengeAction';
import {
  resetCreateSuccess,
  resetUpdateSuccess,
  resetAddMediaSuccess,
  updatePostData,
} from '../../redux/Post/PostSlice';
import {muteVideo} from '../../utils/media';
import {useAppSelector} from '../../redux/Store';
import FastImage from 'react-native-fast-image';
import {baseUrl, extractRef, getKeyParamFromUrl, isVideoLink} from '../../utils/Utils';
import moment from 'moment-timezone';
import Toast from 'react-native-toast-message';
import {updatePointInBoard} from '../../redux/LeaderBoard/LeaderBoardAction';

import HealthChallengeDatePicker from '../UserProfileScreen/HealthChallengeDatePicker';
import AiGenerationModal, {AIGeneratedData} from '../Common/AiGenerationModal';
import {useRoute, useFocusEffect} from '@react-navigation/native';
import ImportPointDataScreen from '../Common/ImportPointDataScreen';


const {width, height} = Dimensions.get('window');

const TOGGLE_BUTTON_ICON_SIZE = 30;

let bottomLoad = false;
export function UpdatePostScreen({navigation, route}: any) {
  const [title, setTitle] = useState(null);
  const [description, setDescription] = useState('');
    const [numDescriptionLines, setNumDescriptionLines] = useState(1);

  const [visibility, setVisibility] = useState('PUBLIC');
  const [challengeId, setChallengeId] = useState(null);
  const [loading, setLoading] = useState(false);

  const currentDate = moment().toDate();

  const [selectedMedia, setSelectedMedia] = useState<any>([]);
  const inputRef = useRef<any>(null);
  const [height, setHeight] = useState(48);
  const [selectedChallenge, setSelectedChallenge] = useState<any>(null);
  const [isToggled, setIsToggled] = useState(false);
  const [isExternalAppToggled, setIsExternalAppToggled] = useState(false);
  const [externalApp, setExternalApp] = useState('');
  const [importDataType, setImportDataType] = useState('');
  const [isImportDataModalVisible, setImportDataModalVisible] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);
  const authState = useAppSelector(state => state.auth);
  const [points, setPoints] = useState('');
  const [remoteMedia, setRemoteMedia] = useState('');
  const [descriptionError, setDescriptionError] = useState('');
  const [deletedItem, setDeletedItem] = useState<any[]>([]);
  const {userId, token} = authState;
  const challengesFeed = useAppSelector(
    state => state.challenge.challengesFeed.data,
  );
  const nextPageToken = useAppSelector(
    state => state.challenge.challengesFeed.nextPageToken,
  );
  const hasNext = useAppSelector(
    state => state.challenge.challengesFeed.hasNext,
  );

  const [loadingItems, setLoadingItems] = useState<{[key: string]: boolean}>(
    {},
  );
  const [playingIndex, setPlayingIndex] = useState<number | null>(null);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const postState = useAppSelector(state => state.post);
  const {isUpdateSuccess, postDraft, isAddMediaSuccess} = postState;
  const [isAiGenerateOpen, setAiGenerateModal] = useState(false);

   // State for health data
    const [totalSteps, setTotalSteps] = useState(0);
    const [totalCalories, setTotalCalories] = useState(0);
    const [totalDistance, setTotalDistance] = useState(0);
  
    // Callback for health data updates
    const handleHealthDataUpdate = ({steps, calories, distance}: any) => {
      setTotalSteps(steps);
      setTotalCalories(calories);
      setTotalDistance(distance);
    };
  
    const onAiGeneratedConfirm = (data: AIGeneratedData) => {
      setAiGenerateModal(false);
      setDescription(data.description);
    };
  
    const onImageGenerate = (uri: string) => {
      setSelectedMedia((prev: any) => [
        ...[{uri: uri, type: 'image/png', filename: new Date().getTime() + '.jpg'}],
        ...prev,
      ]);
    };

  useEffect(() => {
    const fetchPostDetails = async () => {
      const result = await dispatch(getPostById(route.params?.postId));
      if (getPostById.fulfilled.match(result)) {
        if (result.payload.code == 200) {
          const details = result.payload.body.data;
          setSelectedChallenge(null);
          dispatch(updatePostData(details));
          setDescription(details.description);
          setRemoteMedia(details.mediaRefs);
          setIsToggled(details.challengeId);
          
          details?.points ? setPoints(details.points) : null;
          console.log('Set Points', points);
          
        } else {
          Alert.alert('Failed to get post.');
        }
      } else if (getPostById.rejected.match(result)) {
        Alert.alert('Failed to get post.');
      }
    };

    fetchPostDetails();
  }, []);

  const handleVideoTap = (index: number) => {
    setPlayingIndex(prevIndex => (prevIndex === index ? null : index)); // Toggles play/pause
  };

  useEffect(() => {
    if (route.params?.selectedMedia) {
      setSelectedMedia(route.params.selectedMedia);
      setRemoteMedia(postDraft.mediaRefs);
    }
  }, [route.params?.selectedMedia]);
  const handleContentSizeChange = (event: any) => {
    const newHeight = event.nativeEvent.contentSize.height;
    if (newHeight > 48) {
      // Ensures the height is not less than 48
      setHeight(newHeight);
    } else {
      setHeight(48); // Sets the minimum height
    }
  };
  const onLayoutHandler = (Name: any) => (event: any) => {
    const {x, y, width, height} = event.nativeEvent.layout;
    console.log(`Name: ${Name},Width: ${width}, Height: ${height}`);
  };

  useEffect(() => {
    if (
      challengesFeed &&
      challengesFeed.length > 0 &&
      postDraft &&
      postDraft.challengeId
    ) {
      const challenge: any[] = challengesFeed.filter(
        (item: any) => item.challengeId === postDraft.challengeId,
      );
      if (challenge.length > 0) {
        setSelectedChallenge(challenge[0]);
        setChallengeId(challenge[0].challengeId);
        setTitle(challenge[0].title);
      }
    }
  }, [challengesFeed, postDraft]);

  const handleTogglePress = () => {
    setIsToggled(!isToggled); // This will switch the state from true to false and vice versa
    if (!isToggled) {
      setSelectedChallenge(null);
      setChallengeId(null);
      setTitle(null);
    }
  };

  const externalAppTogglePress = () => {
    const newToggleValue = !isExternalAppToggled;
    setIsExternalAppToggled(newToggleValue);

    if (newToggleValue) {
      navigation.navigate('IntegrationsScreen', {
        destinationScreen: 'UpdatePostScreen',
        destinationStack: 'CreatePostScreenStack',
        externalApp: externalApp,
      });
      console.log('Navigate to Integration Screen');
    }
  };
  const toggleImportDataModal = () => {
    setImportDataModalVisible(!isImportDataModalVisible);
  };
  const updateImportData = (newImportData: any) => {
    setImportDataType(newImportData);
    console.log('Selected Import Data:', newImportData);
  };
  useFocusEffect(
      React.useCallback(() => {
        if (route.params && 'externalApp' in route.params) {
          setExternalApp(route.params.externalApp ?? '');
          console.log(
            'External App from Integration Screen',
            route.params.externalApp,
          );
        }
      }, [route.params?.externalApp]),
    );
  
    useEffect(() => {
      if (externalApp !== '') {
        setIsExternalAppToggled(true);
      }
    }, [externalApp]);

  const handleDelete = (uri: any, remote?: boolean, hashRef?: any) => {
    if (remote) {
      const remoteUrl = hashRef + '';
      hashRef = getKeyParamFromUrl(hashRef) ? getKeyParamFromUrl(hashRef) : hashRef;
      
      Alert.alert(
        'Delete Media',
        'Are you sure you want to delete this media?',
        [
          {
            text: 'No',
            onPress: () => console.log('Ask me later pressed'),
          },
          {
            text: 'Delete',
            onPress: async () => {
              const deleteMedia = {
                id: postDraft.id,
                media: [hashRef],
              };
              setDeletedItem(prev => {
                prev.push(deleteMedia);
                return prev;
              });
              setRemoteMedia((prevMedia: any) =>
                prevMedia.filter((media: any) => media !== remoteUrl),
              );
              // const draftPost = postDraft;
              // draftPost.mediaRefs = draftPost.mediaRefs.filter(
              //   (media: any) => media !== remoteUrl,
              // );
              // dispatch(updatePostData(draftPost));
              const draftPost = {
                ...postDraft,
                mediaRefs: postDraft.mediaRefs?.filter(
                  (media: any) => media !== remoteUrl,
                ),
              };

              dispatch(updatePostData(draftPost));
            },
            style: 'cancel',
          },
        ],
      );
    } else {
      setSelectedMedia((prevMedia: any) =>
        prevMedia.filter((media: any) => media.uri !== uri),
      );
    }
  };

  const isAnyItemLoading = () => {
    return Object.values(loadingItems).some(isLoading => isLoading);
  };

  const handleMuteUnmute = async (uri: string) => {
    const selectedItem: any = selectedMedia.find(
      (item: any) => item.uri === uri,
    );

    if (!selectedItem?.muteUrl) {
      setLoadingItems(prev => ({...prev, [uri]: true}));
      const updatedItems: any = await Promise.all(
        selectedMedia.map(async (item: any) => {
          if (item.uri === uri) {
            if (item.muteUrlStore) {
              item.uri = item.muteUrlStore;
              item.muteUrl = item.muteUrlStore;
              item.originalUri = uri;
            } else {
              const muteUrl = `${uri}_mute.mp4`;
              await muteVideo(uri, muteUrl); // Wait for compression
              item.muteUrl = muteUrl;
              item.uri = muteUrl;
              item.muteUrlStore = muteUrl;
              item.originalUri = uri;
            }
          }
          return item;
        }),
      );
      setLoadingItems(prev => ({...prev, [uri]: false}));
      setSelectedMedia([...updatedItems] as any);
    } else {
      const updatedItems: any = selectedMedia.map((item: any) => {
        if (item.uri === uri) {
          item.uri = item.originalUri;
          item.muteUrlStore = item.muteUrl;
          delete item.muteUrl;
        }
        return item;
      });
      setSelectedMedia([...updatedItems] as any);
    }
  };

  const renderMediaItem = ({item, index}: any) => {
    const isRemoteMedia = typeof item == 'string';
    const hashRef = item;
    const imageURI =
      typeof item == 'string'
        ? item
        : item.uri
        ? Platform.OS === 'android'
          ? `file://${item.uri}`
          : item.uri
        : null;

    if (isRemoteMedia) {
      item = {
        type: isVideoLink(imageURI) ? 'video' : 'image',
        uri: imageURI,
      };
    }

    return (
      <TouchableOpacity activeOpacity={1} style={styles.mediaItem}>
        {item.type.includes('image') ? (
          <FastImage
            source={{
              uri: imageURI,
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }}
            style={styles.mediaImage}
          />
        ) : (
          <TouchableWithoutFeedback onPress={() => handleVideoTap(index)}>
            <Video
              source={{
                uri: item.uri,
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }}
              useTextureView={false}
              paused={playingIndex !== index}
              resizeMode={'cover'}
              style={styles.mediaImage}
              ignoreSilentSwitch="ignore"
              controls={false}
            />
          </TouchableWithoutFeedback>
        )}

        {/* Delete icon */}
        {[...selectedMedia, ...remoteMedia].length > 1 && (
          <TouchableOpacity
            style={styles.deleteIconContainer}
            disabled={isAnyItemLoading() || loadingBtn}
            onPress={() => handleDelete(item.uri, isRemoteMedia, hashRef)}>
            <MaterialCommunityIcons name="delete" size={24} color="red" />
          </TouchableOpacity>
        )}

        {item.type.includes('video') && (
          <TouchableOpacity
            style={styles.muteIconContainer}
            disabled={isAnyItemLoading() || loadingBtn}
            onPress={() => handleMuteUnmute(item.uri)}>
            {loadingItems[item.uri] ? (
              <ActivityIndicator size="small" color="red" /> // Loader component
            ) : (
              <MaterialCommunityIcons
                name={item.muteUrl ? 'volume-off' : 'volume-high'}
                size={24}
                color="red"
              />
            )}
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  const handleSelectChallenge = (item: any) => {
    if (item.challengeId === selectedChallenge?.challengeId) {
      // If the item is already selected, deselect it
      setSelectedChallenge(null);
      setChallengeId(null);
      setTitle(null);
    } else {
      // Select a new challenge and open the modal
      setSelectedChallenge(item);
      setChallengeId(item.challengeId);
      setTitle(item.title);
    }
  };
  const renderChallengeItem = ({item}: any) => (
    <TouchableOpacity
      style={[styles.challengeItem]}
      onPress={() => handleSelectChallenge(item)}>
      <View style={styles.challengeImage}>
        {item.mediaRefs &&
          item.mediaRefs.length > 0 &&
          (isVideoLink(item.mediaRefs[0]) ? (
            <Video
              source={{
                uri: item.mediaRefs[0],
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }}
              useTextureView={false}
              bufferConfig={{
                minBufferMs: 2500,
                maxBufferMs: 50000,
                bufferForPlaybackMs: 2500,
                bufferForPlaybackAfterRebufferMs: 2500,
              }}
              onError={error => console.log(error, 'errorerrorerrorerror')}
              style={[
                styles.selectedChallengeOverlay,
                {borderRadius: 8, backgroundColor: 'gray'},
              ]}
              resizeMode="cover"
              repeat
              paused={true}
            />
          ) : (
            <FastImage
              source={{
                uri: item.mediaRefs[0],
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }}
              style={[
                styles.selectedChallengeOverlay,
                {borderRadius: 8, backgroundColor: 'gray'},
              ]}
            />
          ))}
        {item.challengeId === selectedChallenge?.challengeId && (
          <View style={styles.selectedChallengeOverlay} />
        )}
        <View style={styles.textContainer}>
          <Text style={styles.challengeText}>
            {item.title || item.description}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  useEffect(() => {
    if (isUpdateSuccess) {
      navigation.goBack();

      dispatch(resetUpdateSuccess());
      dispatch(resetCreateSuccess());
    } else {
      console.log('3 Update Post failed or user working on creating new post');
    }
  }, [isUpdateSuccess]);

  useEffect(() => {
    if (isToggled && !hasFetched) {
      dispatch(getChallengeFeed({userId}));
      console.log('UserId', userId);
      console.log(
        'ChallengesFeed-------------------------------------->',
        challengesFeed,
      );
      setHasFetched(true);
    }
  }, [isToggled]);

  const dispatch = useDispatch();

  const onExploreFeedEnd = useCallback(() => {
    if (challengesFeed.length > 0 && !loading && !bottomLoad && hasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeFeed({userId: userId, nextPageToken: nextPageToken}),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, challengesFeed, nextPageToken, loading, hasNext]);

  const updatePointOnBoard = (postId: string, point?: number,type?:string) => {
    const body = {
      userId: userId,
      challengeId: selectedChallenge.challengeId,
      points: point ? point : Number(points),
      postId: postId,
      leaderboardType:type ? type: '',
    };
    dispatch(updatePointInBoard(body));
  };

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onCreatePost = async () => {
    if (!description) {
      setDescriptionError('Please enter description');
      return;
    }
    if (remoteMedia.length === 0 && selectedMedia.length == 0) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please select at least 1 media.',
      });
      return;
    }
    setLoadingBtn(true);
    const mediaFile = selectedMedia.map((image: any) => ({
      uri: image.uri,
      name: image.filename,
      type: image.type,
    }));
    let postId = postDraft.id;
    console.log('postId for medeia', postId);

    if (mediaFile?.length > 0) {
      const result = await dispatch(addPostMedia({postId, mediaFile})).unwrap();
      if (result.code != 200) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to upload media.',
        });
        setLoadingBtn(false);
        return;
      }
    }
    if (!isToggled) {
      const unlink = await dispatch(
        unlinkPostFromChallenge({
          id: postDraft.id,
          challengeId: challengeId,
          userId: userId,
        }),
      ).unwrap();
      if (unlink.code != 200) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to unlink challenge',
        });
        setLoadingBtn(false);
        return;
      }
    }

    const updatePostValue = {
      id: postDraft.id,
      title: title,
      description: description,
      visibility: visibility,
      challengeId: challengeId,
      draft: false,
    };
    console.log('Update PostValue: ', updatePostValue);
    try {
      const postUpdateResponse = await dispatch(
        updatePost(updatePostValue),
      ).unwrap();
      if (postUpdateResponse.code != 200) {
        Toast.show({
          type: 'error',
          text1: 'Failed to update post.',
          text2: 'Something went wrong.',
        });
      } else {
        if (deletedItem && deletedItem.length > 0) {
          for (let index = 0; index < deletedItem.length; index++) {
            const element = deletedItem[index];
            await dispatch(deletePostMedia(element));
          }
        }
        if (route.params.from == 'user_post') {
          await dispatch(updateUserPost(postUpdateResponse.body.data)).unwrap();
        }
        // if (
        //   selectedChallenge &&
        //   selectedChallenge.isLeaderboardCreated &&
        //   !selectedChallenge.isPointsAutomated
        // ) {
        //   updatePointOnBoard();
        // } else if (
        //   selectedChallenge &&
        //   selectedChallenge.isLeaderboardCreated &&
        //   selectedChallenge.isPointsAutomated
        // ) {
        //   updatePointOnBoard(1);
        // }
        if (
          //Manual entry Scoring accumulation 
          selectedChallenge &&
          selectedChallenge.isLeaderboardCreated &&
          (!selectedChallenge.isPointsAutomated ||
            selectedChallenge.config?.pointsScoringType != 'POINT_PER_POST' ) && selectedChallenge.config?.pointsScoringType !=
            'EXTERNAL_APP_DEVICE' && selectedChallenge.config?.leaderboardType != 'THRESHOLD' && selectedChallenge.config?.leaderboardType != 'PERSONAL_RECORD'
        ) {
          console.log('Manual entry Scoring accumulation');
          updatePointOnBoard(postId);
        } else if (
          // Automated Scoring / External App Device Threshold Scoring / Point Per Post Scoring
          selectedChallenge &&
          selectedChallenge.isLeaderboardCreated &&
          (selectedChallenge.isPointsAutomated ||
            selectedChallenge.config?.pointsScoringType ===
              'POINT_PER_POST' ||
            (selectedChallenge.config?.pointsScoringType ===
              'EXTERNAL_APP_DEVICE' &&
              selectedChallenge.config?.leaderboardType === 'THRESHOLD'))
        ) {
          console.log('Automated Scoring / External App Device Threshold Scoring / Point Per Post Scoring');
          updatePointOnBoard(postId, 1);
        } else if (
          // External App Device  accumulation Scoring
          selectedChallenge &&
          selectedChallenge.isLeaderboardCreated &&
          selectedChallenge.config?.pointsScoringType ===
            'EXTERNAL_APP_DEVICE' &&
          selectedChallenge.config?.leaderboardType === 'ACCUMULATION'
        ) {
          console.log('External App Device  accumulation Scoring');
          updatePointOnBoard(postId);
        } else if (
           // External App Device / MANUAL ENTRY Personal Record Scoring
          selectedChallenge &&
          selectedChallenge.isLeaderboardCreated &&
          (selectedChallenge.config?.pointsScoringType ===
            'EXTERNAL_APP_DEVICE' || selectedChallenge.config?.pointsScoringType ===
            'MANUAL_ENTRY') &&
          selectedChallenge.config?.leaderboardType === 'PERSONAL_RECORD'
        ) {
          console.log('External App Device / MANUAL ENTRY Personal Record Scoring');
          updatePointOnBoard(postId,undefined,'PERSONAL_RECORD');
        }
      }
    } catch (error) {
      console.log(error, 'errorerrorerror');

      Toast.show({
        type: 'error',
        text1: 'Failed to update post.',
        text2: 'Something went wrong.',
      });
    }
    setLoadingBtn(false);
    dispatch(resetAddMediaSuccess());
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
        <View style={[acrossAllScreens.ScreenBorders]}>
          <View
            style={[styles.HeaderStyle, {marginBottom: 10}]}
            onLayout={onLayoutHandler('HeaderViewTag')}>
            <TouchableOpacity
              style={[
                acrossAllScreens.backImageContainer,
                styles.HeaderbackButton,
              ]}
              disabled={isAnyItemLoading() || loadingBtn}
              onPress={() => navigation.goBack()}>
              <Image
                style={acrossAllScreens.backImage}
                source={require('../../assets/images/back.png')}
              />
            </TouchableOpacity>
            <View style={styles.HeaderHorizontalPosition}>
              <Text style={[acrossAllScreens.ScreenHeaderText]}>
                Update Post
              </Text>
            </View>
          </View>
            <KeyboardAwareScrollView
              contentContainerStyle={styles.scrollViewContent}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              enableOnAndroid={true}
              extraScrollHeight={Platform.OS === 'ios' ? 60 : 100} 
                   >

          {isToggled && title ? (
            <View
              style={{marginTop: 4}}
              onLayout={onLayoutHandler('Title TextInputViewTag')}>
              <Text
                style={[
                  acrossAllScreens.InputTextLarge,
                  {
                    minHeight: 52.7, // Ensures the height is not less than 48
                    textAlignVertical: 'center',
                  },
                ]}>
                {title}
              </Text>
            </View>
          ) : null}

          {/* Show capture or selected media */}
          {[...selectedMedia, ...remoteMedia].length > 0 && (
            <View style={styles.mediaContainer}>
              <FlatList
                data={[...selectedMedia, ...remoteMedia]}
                horizontal
                renderItem={renderMediaItem}
                keyExtractor={item =>
                  typeof item == 'string' ? item : item.uri
                }
                showsHorizontalScrollIndicator={false}
              />
               
               <TouchableOpacity
              // onPress={toggleImage}
              onPress={() => {
                navigation.navigate('CameraScreen', {
                  destinationScreen: 'UpdatePostScreen',
                  destinationStack: 'CreatePostScreenStack',
                  selectedMedia,
                  totalMedia:
                    selectedMedia?.length | (0 + remoteMedia?.length) | 0,
                });
              }}
              disabled={isAnyItemLoading() || loadingBtn}>
              <Image style={styles.plusButton} source={require('../../assets/images/plus_button.png')} />
              {/* <AntDesign name="pluscircle" size={25} color="#C3E7F5" /> */}
            </TouchableOpacity>
            </View>
          )}
           <View
            style={[
              styles.HorizontalStyle,
              // styles.spacerBottom,
              {justifyContent: 'space-between'},
            ]}>
            {/* TODO: When toggled, display a modal to select the challenge from the list. Preferably, add search bar */}
            <Text style={[acrossAllScreens.H2]}>Link Challenge</Text>
            <TouchableOpacity onPress={handleTogglePress}>
              <FonteAwesome
                name={isToggled ? 'toggle-on' : 'toggle-off'}
                size={TOGGLE_BUTTON_ICON_SIZE}
                color="#c3e7f5"
              />
            </TouchableOpacity>
          </View>
          {isToggled &&
            (challengesFeed.length === 0 ? (
              // No challenges case: Display clickable text to create a challenge
              <View style={styles.noChallengesContainer}>
                <TouchableOpacity
                  onPress={() =>
                    navigation.replace('ChallengeCreationScreenStack', {
                      screen: 'ChallengeCreationScreen',
                    })
                  }>
                  <Text style={styles.noChallengesText}>
                    Create a challenge to start a new leaderboard {'>'}
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <>
                {/* Info Text */}
                {/* Challenge Section */}
                <View style={[styles.challengeListContainer]}>
                  {selectedChallenge ? null : (
                    <Text
                      style={[
                          acrossAllScreens.H2,
                          {marginBottom: 10, fontWeight: 'bold'},
                      ]}>
                        Select a Challenge to link post
                    </Text>
                  )}
                  <FlatList
                    data={challengesFeed.filter((challenge: any) => {
                      const endDate = new Date(challenge.endDate);
                      return endDate >= currentDate; // Active if current date is between start and end
                    })}
                    renderItem={renderChallengeItem}
                      keyExtractor={(item: any) => item.challengeId}
                      horizontal={true}
                      showsHorizontalScrollIndicator={false}
                      onEndReached={onExploreFeedEnd}
                      onEndReachedThreshold={0.5}
                      scrollEventThrottle={16}
                      ListFooterComponent={renderFooter}
                      contentContainerStyle={{paddingRight: 2}}
                  />
                </View>
              </>
            ))}

          <View
             style={[{paddingTop: Platform.OS === 'android' ? 0 : 0}]}
            onLayout={onLayoutHandler('Desc. TextInputViewTag')}>
            {/* Character count disbale for now  */}
            {/* <Text onLayout={onLayoutHandler('Avail char TextTag')}>
              Available Characters: {maxRemaianDescLength}
            </Text> */}
            <TextInput
              onLayout={onLayoutHandler('Desc. TextInputTag')}
              ref={inputRef}
              style={[
                acrossAllScreens.InputTextRegular,
                {
                  textAlignVertical: 'top',
                  minHeight: 40,
                  marginBottom:
                  numDescriptionLines === 1 || description.trim().length === 0
                  ? 0
                  : Platform.OS === 'android' ? 5 : 16
                },
              ]}
              value={description}
              onChangeText={newtext => {
                setDescription(newtext);
                setDescriptionError('');
              }}
               onContentSizeChange={event => {
                handleContentSizeChange(event);
                const lineHeight = Platform.OS === 'android' ?32 : 20; // approximate line height in pixels
                const contentHeight = event.nativeEvent.contentSize.height;
                const lines = Math.max(1, Math.round(contentHeight / lineHeight));
                console.log('Number of lines:', lines, 'Content Height:', contentHeight , description.trim().length);
                setNumDescriptionLines(lines);
              }}
              placeholder="Enter Post Description "
              maxLength={400}
              multiline={true}
              onEndEditing={() => inputRef.current?.blur()}
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
            />
            {descriptionError ? (
               <View style={{marginTop: Platform.OS === 'android' ? 10 : 0,marginBottom: 11}}>
                <Text style={acrossAllScreens.ErrorText}>
                  {descriptionError}
                </Text>
              </View>
            ) : null}

            <TouchableOpacity
                style={styles.napozAIButton}
                onPress={() => {
                  console.log('Napoz AI button pressed for description');
                  setAiGenerateModal(true);
                }}>
                <Image
                  source={require('../../assets/images/NapozAI.png')}
                  style={styles.napozAIImage}
                />
                <Text style={acrossAllScreens.H2}>Napoz AI</Text>
              </TouchableOpacity>
          </View>
           {isToggled &&
            selectedChallenge &&
            selectedChallenge.isLeaderboardCreated &&
            !selectedChallenge.isPointsAutomated && (
            <Text style={styles.associatetext}>
              {selectedChallenge.pointsDescription}
            </Text>
            )}
            {isToggled &&
              selectedChallenge &&
              selectedChallenge.isLeaderboardCreated &&
              !selectedChallenge.isPointsAutomated && (
                <View >
                  <TextInput
                    placeholder={
                      selectedChallenge.pointsTitle
                        ? selectedChallenge.pointsTitle
                        : 'Enter points'
                    }
                    underlineColorAndroid="transparent"
                    placeholderTextColor={'gray'}
                    value={points}
                    onChangeText={setPoints}
                    keyboardType={'number-pad'}
                    style={[
                      acrossAllScreens.InputTextRegular,
                      {
                        paddingBottom: 8,
                        color: 'black',
                        fontWeight: 'bold',
                        marginTop: Platform.OS === 'android' ? 0 : 12,
                        // marginBottom: Platform.OS === 'android' ? 0 : 8,
                      },
                    ]}
                  />
                </View>
              )}

            {!(
              isToggled &&
              selectedChallenge &&
              selectedChallenge.isLeaderboardCreated &&
              selectedChallenge.config &&
              selectedChallenge.config.pointsScoringType ==
                'EXTERNAL_APP_DEVICE'
            ) ? (
              <View
                style={[
                  styles.HorizontalStyle,
                  styles.spacerBottom,
                  {justifyContent: 'space-between'},
                ]}>
                <Text style={[acrossAllScreens.H2]}>
                  Integrate External App/ Device :
                  {isExternalAppToggled && (
                    <Text style={[acrossAllScreens.H3, {marginLeft: 5}]}>
                      {' '}
                      {externalApp}{' '}
                    </Text>
                  )}
                </Text>
                <TouchableOpacity onPress={externalAppTogglePress}>
                  <FonteAwesome
                    name={
                      isExternalAppToggled && externalApp
                        ? 'toggle-on'
                        : 'toggle-off'
                    }
                    size={TOGGLE_BUTTON_ICON_SIZE}
                    color="#c3e7f5"
                  />
                </TouchableOpacity>
              </View>
            ) : null}     
            {!(
              isToggled &&
              selectedChallenge &&
              selectedChallenge.isLeaderboardCreated &&
              selectedChallenge.config &&
              selectedChallenge.config.pointsScoringType ==
                'EXTERNAL_APP_DEVICE'
            ) &&
            isExternalAppToggled &&
            externalApp ? (
              <View style={[{marginBottom: 5}]}>
                <TouchableOpacity
                  onPress={toggleImportDataModal}
                  disabled={isAnyItemLoading() || loading}>
                  <View style={[styles.ModalButton]}>
                    <Text style={[acrossAllScreens.H2]}>Import Data Type</Text>
                    <View style={styles.HorizontalStyle}>
                      <Text style={[acrossAllScreens.H3, {marginLeft: 6}]}>
                        {importDataType}
                      </Text>
                      <AntDesign name="right" size={20} color="black" />
                    </View>
                  </View>
                </TouchableOpacity>
                <Modal
                  isVisible={isImportDataModalVisible}
                  onBackdropPress={toggleImportDataModal}
                  onBackButtonPress={toggleImportDataModal}
                  style={{margin: 0, justifyContent: 'flex-end'}}
                  backdropOpacity={0.9}>
                  <View>
                    <ImportPointDataScreen
                      onOptionSelect={updateImportData}
                      closeModal={toggleImportDataModal}
                      ExternalApp={externalApp}
                    />
                  </View>
                </Modal>
              </View>
            ) : null}                
            {isToggled &&
              selectedChallenge &&
              selectedChallenge.isLeaderboardCreated &&
              selectedChallenge.config &&
              selectedChallenge.config.pointsScoringType ==
                'EXTERNAL_APP_DEVICE' && (
                <View>
                  <HealthChallengeDatePicker
                    onImageGenerate={onImageGenerate}
                    config={selectedChallenge.config}
                    onDataUpdate={handleHealthDataUpdate}
                    disabled={isAnyItemLoading() || loading}
                  />
                </View>
              )}
            {!(
              isToggled &&
              selectedChallenge &&
              selectedChallenge.isLeaderboardCreated &&
              selectedChallenge.config &&
              selectedChallenge.config.pointsScoringType ==
                'EXTERNAL_APP_DEVICE'
            ) &&
              isExternalAppToggled &&
              importDataType && (
                <View>
                  <HealthChallengeDatePicker
                    onImageGenerate={onImageGenerate}
                    config={{
                      importDataType: importDataType,
                      externalAppType: externalApp,
                    }}
                    onDataUpdate={handleHealthDataUpdate}
                    disabled={isAnyItemLoading() || loading}
                  />
                </View>
              )}
         
           <View style={!importDataType ? [styles.CameraRowStyle, {marginTop: 14}] : [styles.CameraRowStyle, {marginTop: 4}]}>
            {/* <TouchableOpacity
              // onPress={toggleImage}
              onPress={() => {
                navigation.navigate('CameraScreen', {
                  destinationScreen: 'UpdatePostScreen',
                  destinationStack: 'CreatePostScreenStack',
                  selectedMedia,
                  totalMedia:
                    selectedMedia?.length | (0 + remoteMedia?.length) | 0,
                });
              }}
              disabled={isAnyItemLoading() || loadingBtn}>
              <FonteAwesome
                name="camera"
                size={75}
                color={isAnyItemLoading() || loadingBtn ? 'gray' : 'black'}
              />
            </TouchableOpacity> */}

            <TouchableHighlight
              style={styles.createButton}
              disabled={isAnyItemLoading() || loadingBtn}
              onPress={onCreatePost}>
              {isAnyItemLoading() || loadingBtn ? (
                <ActivityIndicator />
              ) : (
                <Text style={SignUpStyle.signUpText}>Update Post</Text>
              )}
            </TouchableHighlight>
          </View>
        
        </KeyboardAwareScrollView>
          {/* </KeyboardAvoidingView> */}
          </View>
       
        <AiGenerationModal
          visible={isAiGenerateOpen}
          onClose={() => setAiGenerateModal(false)}
          onConfirm={onAiGeneratedConfirm}
          isPost={true}
          challengeTitle={title}
        />
    </View>
  );
}

const styles = StyleSheet.create({
  borderview: {
    borderBottomWidth: 1,
    marginVertical: 15,
    borderBottomColor: '#E5E5E5',
    marginHorizontal: -15,
  },
  associatetext: {
    fontFamily: 'Helvetica Neue',
    fontSize: 12,
    fontWeight: '300',
    lineHeight: 14.51,
    marginTop: 10,
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  scrollViewContent: {
    flexGrow: 1, // Ensures the content grows to fill the available space
  },
  ColorStyle: {
    color: 'black',
  },
  PartitionStyle: {
    // paddingTop: 1,
    borderBottomWidth: 1,
    paddingBottom: 0,
    marginBottom: 16,
    borderBottomColor: '#E5E5E5',
  },
  //  MarginStyle: {marginLeft: 10},
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  ModalButton: {
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'space-between',
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // width: width,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  TitleStyle: {
    fontSize: 24,
    fontWeight: 'bold',
    // borderWidth: 1,
    // textAlignVertical: 'top',
    // height: 100,
    // flex: 1,
    // alignSelf: 'stretch',
  },
  DescriptionStyle: {
    textAlignVertical: 'top',
  },
  HorizontalStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  HashtagStyle: {
    fontSize: 18,
    fontWeight: 'bold',
    borderWidth: 1,
    alignSelf: 'flex-start',
    padding: 3,
    marginTop: 5,
    marginBottom: 15,
  },
  SubTitleStyle: {
    fontSize: 18,
    // marginTop: 10,
    // marginBottom: 5,
  },
  ViewSubTitleStyle: {
    marginTop: 0,
  },
  CameraRowStyle: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  PreviewButtonStyle: {
    fontSize: 18,
    fontWeight: 'bold',
    borderWidth: 1,
    padding: 5,
    borderRadius: 10,
  },
  cardImage: {
    // alignSelf: 'center',
    width: '100%',
    height: 300,
    // marginBottom: 8,
    // marginTop: 10,
  },
  spacerTop: {
    marginTop: 10,
  },
  spacerBottom: {
    marginBottom: 10,
  },
  mediaContainer: {
    height: 150,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  plusButton: {
    width: 24.5,
    height: 24.5,
    resizeMode: 'contain',
  marginLeft: 8,
  marginRight: 2,
  marginTop: 25,marginBottom: 25},  
  mediaItem: {
    marginRight: 10,
  },
  mediaImage: {
    width: 150,
    height: 150,
    borderRadius: 8,
    backgroundColor: '#ccc',
  },
  muteIconContainer: {
    position: 'absolute',
    top: 40,
    right: 5,
    padding: 5,
    borderRadius: 20,
  },
  deleteIconContainer: {
    position: 'absolute',
    top: 5,
    right: 5,
    padding: 5,
    borderRadius: 20,
  },
  challengeListContainer: {
    // height: height * 0.9, // Takes 50% of the screen height
    marginTop: 10,
  },
  challengeItem: {
    backgroundColor: '#D3D3D3',
    height: 130, // Adjust height for the image
    width: (width - 48) / 2, // 2 columns with some padding
    marginBottom: 16,
    marginRight: 16,
    borderRadius: 8, // Rounded corners
    overflow: 'hidden', // Ensures the image respects border radius
  },
  challengeImage: {
    flex: 1,
    justifyContent: 'flex-end', // Position text at the bottom
    padding: 10,
  },
  challengeText: {
    fontSize: 14,
    fontFamily: 'Helvetica Neue',
    color: 'black',
  },
  selectedChallengeOverlay: {
    ...StyleSheet.absoluteFillObject, // Fills the entire image area
    backgroundColor: 'rgba(241, 195, 75, 0.8)', // Semi-transparent yellow highlight
  },
  disabledButton: {
    backgroundColor: '#cccccc', // Greyed out background
  },
  infoText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 10, // 30px below header
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  textContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // Semi-transparent background behind the text
    padding: 5,
    alignSelf: 'flex-start',
  },
  noChallengesText: {
    fontSize: 18,
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  noChallengesContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 20,
    height: 48,
  },napozAIButton: {
    backgroundColor: '#C3E7F5',

    height: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    // marginTop: Platform.OS === 'android' ? 0 : 16,
    width: 115,
    borderRadius: 15,
  },
  napozAIImage: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginHorizontal: 5,
  }, createButton: {
    backgroundColor: '#c3e7f5',
    height: 70,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 225,
    borderRadius: 6,
    // marginBottom: 40
  },
});

export default UpdatePostScreen;
