import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {Button} from 'react-native';
import {TabRouter} from '@react-navigation/routers';
import RecordActivityScreen from './RecordActivityScreen';

const recordActivityStack = createStackNavigator();

export function RecordActivityStack() {
  return (
    <recordActivityStack.Navigator>
      <recordActivityStack.Screen
        name="RecordActivityScreen"
        component={RecordActivityScreen}
        options={{title: 'Record Activity'}}
      />
      {/* <recordActivityStack.Screen
        name="Hash"
        component={HashtagScreen}
        options={{
          title: 'Hashtag',
          headerTitleAlign: 'center',
          headerLeft: () => null,
          // headerRight: () => (
          //   <Button title="Done" onPress={() => console.log('done')} />
          // ),
        }}
      /> */}
    </recordActivityStack.Navigator>
  );
}

export default RecordActivityStack;
