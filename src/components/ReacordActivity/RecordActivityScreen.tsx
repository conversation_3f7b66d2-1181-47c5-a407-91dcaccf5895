import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';

export function RecordActivityScreen() {
  const [activity, setActivity] = useState('Biking');
  const [device, setDevice] = useState('IPhone 11');
  return (
    <View style={[styles.MarginLeftStyle]}>
      <Text style={[styles.TitleStyle]}>Record Activity</Text>
      <View style={[styles.HorizontalStyle, {justifyContent: 'space-between'}]}>
        <TouchableOpacity style={[styles.HorizontalStyle]}>
          <Text>{activity}</Text>
          <EvilIcons name="chevron-down" size={30} color="black" />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.HorizontalStyle, styles.MarginRightStyle]}>
          <Text>{device}</Text>
          <EvilIcons name="chevron-down" size={30} color="black" />
        </TouchableOpacity>
      </View>
      <View>
        <Image
          style={{height: 300, width: 350, alignSelf: 'center', marginTop: 20}}
          source={require('../../static/Images/mapplaceholder.jpg')}
        />
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  TitleStyle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  MarginLeftStyle: {
    marginLeft: 10,
  },
  MarginRightStyle: {marginRight: 10},
  HorizontalStyle: {
    flexDirection: 'row',
  },
});

export default RecordActivityScreen;
