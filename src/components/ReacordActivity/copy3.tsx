import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Button,
} from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Feather from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import DeviceSelectionScreen from '../DeviceAcitivitySelection/DeviceSelectionScreen';
import ActivitySelectionScreen from '../DeviceAcitivitySelection/ActivitySelectionScreen';
import Stopwatch from './StopWatch';

export function RecordActivityScreen() {
  const [activity, setActivity] = useState('Run');
  const [device, setDevice] = useState('My Phone');
  const [isModalVisibleDev, setModalVisibleDev] = useState(false);
  const [isModalVisibleAct, setModalVisibleAct] = useState(false);
  const [playPause, setPlayPause] = useState(true);
  const [start, setStart] = useState(0);
  const [now, setNow] = useState(0);
  const [stop, setStop] = useState(false);
  const timer = now - start;
  let intervalId;
  const toggleModalDev = () => {
    setModalVisibleDev(!isModalVisibleDev);
  };
  const toggleModalAct = () => {
    setModalVisibleAct(!isModalVisibleAct);
  };
  const sendDevdata = Devdata => {
    setDevice(Devdata);
    toggleModalDev();
  };
  const sendActdata = Actdata => {
    setActivity(Actdata);
    toggleModalAct();
  };
  const togglePlay = () => {
    setPlayPause(!playPause);
    setStop(false);

    setStart(new Date().getTime());

    intervalId = setInterval(() => {
      setNow(new Date().getTime());
    }, 100);
  };
  const togglePause = () => {
    setPlayPause(!playPause);
    clearInterval(intervalId);
    setStart(0);
    console.log(start);
    setNow(0);
    console.log(now);
  };
  const toggleStop = () => {
    setStop(true);
  };
  // let timer = new Date().getTime();

  return (
    <View style={[styles.MarginLeftStyle]}>
      <Text style={[styles.TitleStyle]}>Record Activity</Text>
      <Text style={styles.TextStyle}>
        Record your fitness activity here and earn braggig rights on the
        leaderboard!
      </Text>
      <View style={[styles.HorizontalStyle, {justifyContent: 'space-between'}]}>
        <TouchableOpacity
          onPress={toggleModalAct}
          style={[styles.HorizontalStyle]}>
          <Text style={styles.SubHeadingStyle}>{activity}</Text>
          <EvilIcons name="chevron-down" size={30} color="black" />
        </TouchableOpacity>
        <Modal
          isVisible={isModalVisibleAct}
          onBackdropPress={toggleModalAct}
          onBackButtonPress={toggleModalAct}
          style={styles.ModalStyle}
          backdropOpacity={0.9}>
          <View>
            <ActivitySelectionScreen sendAct={sendActdata} />
          </View>
        </Modal>
        <TouchableOpacity
          onPress={toggleModalDev}
          style={[styles.HorizontalStyle, styles.MarginRightStyle]}>
          <Text style={styles.SubHeadingStyle}>{device}</Text>
          <EvilIcons name="chevron-down" size={30} color="black" />
        </TouchableOpacity>
        <Modal
          isVisible={isModalVisibleDev}
          onBackdropPress={toggleModalDev}
          onBackButtonPress={toggleModalDev}
          style={styles.ModalStyle}
          backdropOpacity={0.9}>
          <View>
            <DeviceSelectionScreen sendDev={sendDevdata} />
          </View>
        </Modal>
      </View>
      <View>
        <Image
          style={{height: 300, width: 350, alignSelf: 'center', marginTop: 20}}
          source={require('../../static/Images/mapplaceholder.jpg')}
        />
      </View>
      <View>
        {playPause ? (
          <View
            style={[styles.HorizontalStyle, {justifyContent: 'space-evenly'}]}>
            {/* <TouchableOpacity onPress={() => setPlayPause(false)}> */}
            <TouchableOpacity onPress={togglePlay}>
              <Feather name="play-circle" size={60} />
            </TouchableOpacity>
            <TouchableOpacity>
              <Feather name="stop-circle" size={60} />
            </TouchableOpacity>
          </View>
        ) : (
          <View
            style={[styles.HorizontalStyle, {justifyContent: 'space-evenly'}]}>
            {/* <TouchableOpacity onPress={() => setPlayPause(true)}> */}
            <TouchableOpacity onPress={togglePause}>
              <Feather name="pause-circle" size={60} />
            </TouchableOpacity>
          </View>
        )}
      </View>

      <View style={[styles.HorizontalStyle, {justifyContent: 'space-evenly'}]}>
        <View style={{alignItems: 'center'}}>
          <Stopwatch />
          <Text>{timer}</Text>
          <Text style={[styles.SubHeadingStyle]}>Time</Text>
        </View>
        <View style={{alignItems: 'center'}}>
          <Text style={styles.SubHeadingStyle}>0.0</Text>
          <Text style={[styles.SubHeadingStyle]}>Distance(m)</Text>
        </View>
        <View style={{alignItems: 'center'}}>
          <Text style={styles.SubHeadingStyle}>0</Text>
          <Text style={[styles.SubHeadingStyle]}>Cals</Text>
        </View>
      </View>

      <View
        style={{
          alignItems: 'flex-end',
          padding: 10,
          paddingRight: 30,
        }}>
        <Button title="Finish" onPress={() => console.log('press')} />
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  TitleStyle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  SubHeadingStyle: {fontSize: 18, fontWeight: 'bold'},
  MarginLeftStyle: {
    marginLeft: 10,
  },
  TextStyle: {fontSize: 16},
  MarginRightStyle: {marginRight: 10},
  HorizontalStyle: {
    flexDirection: 'row',
  },
  ModalStyle: {margin: 0, justifyContent: 'flex-end', marginTop: 150},
});

export default RecordActivityScreen;
