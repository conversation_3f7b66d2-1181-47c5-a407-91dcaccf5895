import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Modal from 'react-native-modal';
import DeviceSelectionScreen from '../DeviceAcitivitySelection/DeviceSelectionScreen';
import ActivitySelectionScreen from '../DeviceAcitivitySelection/ActivitySelectionScreen';
export function RecordActivityScreen() {
  const [activity, setActivity] = useState('Bikings');
  const [device, setDevice] = useState('My Phone');

  const [isModalVisible, setModalVisible] = useState(false);
  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const sendDevdata = Devdata => {
    setDevice(Devdata);
    toggleModal();
  };
  const sendActdata = Actdata => {
    setActivity(Actdata);
    toggleModal();
  };

  return (
    <View style={[styles.MarginLeftStyle]}>
      <Text style={[styles.TitleStyle]}>Record Activity</Text>
      <View style={[styles.HorizontalStyle, {justifyContent: 'space-between'}]}>
        <TouchableOpacity
          onPress={toggleModal}
          style={[styles.HorizontalStyle]}>
          <Text>{activity}</Text>
          <EvilIcons name="chevron-down" size={30} color="black" />
        </TouchableOpacity>
        <Modal
          isVisible={isModalVisible}
          onBackdropPress={toggleModal}
          onBackButtonPress={toggleModal}
          style={{margin: 0, justifyContent: 'flex-end'}}
          backdropOpacity={0.9}>
          <View style={{marginTop: 150}}>
            <ActivitySelectionScreen sendAct={sendActdata} />
          </View>
        </Modal>
        <TouchableOpacity
          onPress={toggleModal}
          style={[styles.HorizontalStyle, styles.MarginRightStyle]}>
          <Text>{device}</Text>
          <EvilIcons name="chevron-down" size={30} color="black" />
        </TouchableOpacity>
        <Modal
          isVisible={isModalVisible}
          onBackdropPress={toggleModal}
          onBackButtonPress={toggleModal}
          style={{margin: 0, justifyContent: 'flex-end'}}
          backdropOpacity={0.9}>
          <View style={{marginTop: 150}}>
            <DeviceSelectionScreen sendDev={sendDevdata} />
          </View>
        </Modal>
      </View>
      <View>
        <Image
          style={{height: 300, width: 350, alignSelf: 'center', marginTop: 20}}
          source={require('../../static/Images/mapplaceholder.jpg')}
        />
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  TitleStyle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  MarginLeftStyle: {
    marginLeft: 10,
  },
  MarginRightStyle: {marginRight: 10},
  HorizontalStyle: {
    flexDirection: 'row',
  },
});

export default RecordActivityScreen;
