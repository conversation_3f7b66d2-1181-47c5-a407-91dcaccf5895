import React, {useState, useEffect} from 'react';
import {
  Safe<PERSON>reaView,
  ScrollView,
  View,
  TextInput,
  TouchableHighlight,
  TouchableOpacity,
  Text,
  TouchableWithoutFeedback,
  Keyboard,
  Button,
  Platform,
  Modal,
  StyleSheet,
  Alert,
  Image,
} from 'react-native';
import {useMutation} from 'react-query';
import CountryPicker from 'react-native-country-picker-modal';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Toast from 'react-native-simple-toast';
import { resetRegisterSuccess } from '../../redux/Auth/AuthSlice';

import {SignUpStyle} from '../../styles/SignUp';
import {CommonFontstyles} from '../../styles/CommonFontStyles';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {createAccount} from '../../apis/mock';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import {register} from '../../apis/register';
import register2 from '../../apis/register2';
import {useDispatch, useSelector} from 'react-redux';
import {register} from '../../redux/Auth/AuthAction';
import RNPickerSelect from 'react-native-picker-select';
import {Picker} from '@react-native-picker/picker';
import {store, useAppSelector} from '../../redux/Store';
import {Provider} from 'react-redux';
import {
  CONFIRM_PASSWORD_PLACEHOLDER,
  DOB_PLACEHOLDER,
  EMAIL_PLACEHOLDER,
  FIRST_NAME_PLACEHOLDER,
  LAST_NAME_PLACEHOLDER,
  PASSWORD_PLACEHOLDER,
  PHONE_NO_PLACEHOLDER,
  SIGN_UP_ERROR_MSG,
} from '../../constants/signup';
import DatePicker from 'react-native-date-picker';
import {Checkbox} from 'react-native-paper';
import moment from 'moment-timezone';

export function SignUpScreen({navigation}: any) {
  const [fName, setFName] = useState('');
  const [checked, setChecked] = useState(false);
  const [lName, setLName] = useState('');
  const [phNum, setPhNum] = useState('**********');
  const [countryCode, setCountryCode] = useState('US');
  const [callingCode, setCallingCode] = useState('+1');
  const [date, setDate] = useState(new Date()); // Default current date
  const [openDatePicker, setOpenDatePicker] = useState(false); // State to control the modal visibility
  const [bday, setBDay] = useState('');
  const [gender, setGender] = useState('None');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [cPassword, setCPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [fNameError, setFNameError] = useState('');
  const [lNameError, setLNameError] = useState('');
  const [phNumError, setPhNumError] = useState('');
  const [callingCodeError, setCallingCodeError] = useState('');
  const [bdayError, setBdayError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [cPasswordError, setCPasswordError] = useState('');
  let values: any;

  const dispatch = useDispatch();
  const authState = useAppSelector(state => state.auth);
  const {isRegisterSuccess, isStart, message} = authState;

  // Format the date to "YYYY-MM-DD"
  const formatDate = (date: any) => {
    return moment(date).format('YYYY-MM-DD');
  };

  // Validation helper functions
  const isFieldEmpty = (field: any) => field.trim() === '';
  const isValidEmail = (email: any) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  // Validation functions for each field
  const validateFName = () => {
    if (isFieldEmpty(fName)) {
      setFNameError('*First name is required.');
      return false;
    }
    setFNameError('');
    return true;
  };

  const validateLName = () => {
    if (isFieldEmpty(lName)) {
      setLNameError('*Last name is required.');
      return false;
    }
    setLNameError('');
    return true;
  };

  const validateEmail = () => {
    if (!isValidEmail(email)) {
      setEmailError('*Please enter a valid email address.');
      return false;
    }
    setEmailError('');
    return true;
  };

  const validatePassword = () => {
    const lengthRegex = /^.{8,}$/; // Ensures the password is at least 8 characters long
    // Ensures a mix of letters, numbers, and symbols
    const alphaNumericRegex =
      /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{1,}$/;

    if (isFieldEmpty(password)) {
      setPasswordError('*Password is required.');
      return false;
    }

    if (!lengthRegex.test(password)) {
      setPasswordError('*Password must be at least 8 characters long.');
      return false;
    }

    if (!alphaNumericRegex.test(password)) {
      setPasswordError(
        '*Password must contain letters, numbers, and symbols (“!@#$%^&*).',
      );
      return false;
    }

    setPasswordError('');
    return true;
  };

  const validateCPassword = () => {
    if (password !== cPassword) {
      setCPasswordError('*Passwords do not match.');
      return false;
    }
    setCPasswordError('');
    return true;
  };

  const validatePhoneNumber = () => {
    // Example: Simple length check, modify regex as needed
    if (phNum.length < 10 || phNum.length > 12) {
      setPhNumError('*Check Phone number or country code.');
      return false;
    }
    setPhNumError('');
    return true;
  };
  const validateCountryCode = () => {
    // Example: Simple length check, modify regex as needed
    if (callingCode.length < 2 || callingCode.length > 4) {
      setCallingCodeError('*Country code must be 2-4 digits.');
      return false;
    }
    setCallingCodeError('');
    return true;
  };

  const validateBirthday = () => {
    const bdayRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
    const match = bday.match(bdayRegex);

    if (!match) {
      setBdayError('*Please enter a valid date of birth');
      return false;
    }

    const year = parseInt(match[1], 10);
    const month = parseInt(match[2], 10);
    const day = parseInt(match[3], 10);

    if (month < 1 || month > 12) {
      setBdayError('Month must be between 01 and 12.');
      return false;
    }

    if (day < 1 || day > 31) {
      setBdayError('Day must be between 01 and 31.');
      return false;
    }

    if (
      (month === 4 || month === 6 || month === 9 || month === 11) &&
      day > 30
    ) {
      setBdayError('Invalid date. This month has only 30 days.');
      return false;
    }

    if (month === 2) {
      const isLeapYear =
        year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
      if ((isLeapYear && day > 29) || (!isLeapYear && day > 28)) {
        setBdayError(
          'Invalid date. February does not have ' + day + ' days in this year.',
        );
        return false;
      }
    }

    const birthDate = new Date(year, month - 1, day);
    if (
      birthDate.getFullYear() !== year ||
      birthDate.getMonth() + 1 !== month ||
      birthDate.getDate() !== day
    ) {
      setBdayError('Invalid date. Please enter a valid date.');
      return false;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const adjustedBirthDate = new Date(
      today.getFullYear(),
      birthDate.getMonth(),
      birthDate.getDate(),
    );

    if (today < adjustedBirthDate) {
      age--;
    }

    if (age < 18) {
      setBdayError('You must be at least 18 years old to sign up.');
      return false;
    }

    setBdayError('');
    return true;
  };

  const validateAllFields = () => {
    const isFNameValid = validateFName();
    const isLNameValid = validateLName();
    const isEmailValid = validateEmail();
    const isPasswordValid = validatePassword();
    const isCPasswordValid = validateCPassword();
    const isPhoneNumberValid = validatePhoneNumber();
    const isCountryCodeValid = validateCountryCode();
    const isBirthdayValid = validateBirthday();

    return (
      isFNameValid &&
      isLNameValid &&
      isEmailValid &&
      isPasswordValid &&
      isCountryCodeValid &&
      isCPasswordValid &&
      isPhoneNumberValid &&
      isBirthdayValid
    );
  };

  useEffect(() => {
    if (isRegisterSuccess) {
      console.log('1 its a success', authState, isRegisterSuccess);
      //navigation.navigate('LoginScreen'); // to Register
      // navigation.navigate('WelcomeStack', {screen: 'Interest'});
      Toast.show('Verification email has been to your registerd email ID. Verify to login.', Toast.LONG);
      navigation.reset({
        index: 0,
        routes: [{name: 'WelcomeStack'}],
      });
      //reset isUpdateSuccess here
            dispatch(resetRegisterSuccess());
    } else if (isStart) {
      console.log('First run');
    } else {
      console.log('2 not a sucesss', authState, isRegisterSuccess);
      // setErrorMessage(SIGN_UP_ERROR_MSG);
    }
  }, [isRegisterSuccess, isStart]);

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const onTermsClick = () => {
    navigation.navigate('TermsScreen');
  };

  const onPrivacyClick = () => {
    navigation.navigate('PrivacyScreen');
  };

  return (
    <View style={acrossAllScreens.ScreenBackground}>
      <KeyboardAwareScrollView
        contentContainerStyle={SignUpStyle.scrollViewContent}
        extraScrollHeight={100} // Adjusts the screen when keyboard is shown
        enableOnAndroid={true}
        keyboardShouldPersistTaps="handled">
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={[acrossAllScreens.ScreenBorders]}>
            <View style={SignUpStyle.backButtonPos}>
              <TouchableOpacity
                style={acrossAllScreens.backImageContainer}
                onPress={() => {
                  console.log('Login Screen');
                  navigation.reset({
                    index: 0,
                    routes: [
                      {name: 'WelcomeStack', params: {screen: 'LoginScreen'}},
                    ],
                  });
                }}>
                <Image
                  style={acrossAllScreens.backImage}
                  source={require('../../assets/images/back.png')}
                />
              </TouchableOpacity>

              <Text
                style={[
                  acrossAllScreens.ScreenHeaderText,
                  SignUpStyle.headerPos,
                ]}>
                Create Account
              </Text>
              {/* <View style={{width: 20, height: 20}}></View> */}
            </View>

            {/* <Text style={acrossAllScreens.Napoz}>Napoz Sign Up!</Text> */}
            <View style={[SignUpStyle.signupContainer]}>
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    // autoFocus={true}
                    placeholder={FIRST_NAME_PLACEHOLDER}
                    placeholderTextColor="grey"
                    underlineColorAndroid="transparent"
                    onChangeText={text => setFName(text)}
                  />
                </View>
                {fNameError ? (
                  <Text style={acrossAllScreens.ErrorText}>{fNameError}</Text>
                ) : null}
              </View>
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder={LAST_NAME_PLACEHOLDER}
                    placeholderTextColor="grey"
                    underlineColorAndroid="transparent"
                    onChangeText={text => setLName(text)}
                  />
                </View>
                {lNameError ? (
                  <Text style={acrossAllScreens.ErrorText}>{lNameError}</Text>
                ) : null}
              </View>
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder={EMAIL_PLACEHOLDER}
                    placeholderTextColor="grey"
                    keyboardType="email-address"
                    autoCapitalize="none"
                    underlineColorAndroid="transparent"
                    autoCapitalize="none" // Prevents automatic capitalization
                    autoCorrect={false} // Disables auto-correction
                    onChangeText={text => setEmail(text.toLowerCase())}
                  />
                </View>
                {emailError ? (
                  <Text style={acrossAllScreens.ErrorText}>{emailError}</Text>
                ) : null}
              </View>
              {/* <View style={SignUpStyle.inputContainer}>
              <TextInput
                style={SignUpStyle.inputs}
                placeholder="Username"
                placeholderTextColor="grey"
                keyboardType="email-address"
                underlineColorAndroid="transparent"
                onChangeText={text => setUserName(text)}
              />
              </View> */}
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.inputContainer}>
                  {/* Clickable Date of Birth Field */}
                  <TouchableOpacity
                    style={[SignUpStyle.inputs, {justifyContent: 'center'}]}
                    onPress={() => setOpenDatePicker(true)}>
                    <Text style={{color: bday ? 'black' : 'grey'}}>
                      {bday || DOB_PLACEHOLDER}
                    </Text>
                  </TouchableOpacity>
                </View>
                <DatePicker
                  modal
                  mode="date"
                  open={openDatePicker}
                  date={date}
                  onConfirm={date => {
                    setOpenDatePicker(false);
                    setDate(date);
                    setBDay(formatDate(date));
                  }}
                  onCancel={() => {
                    setOpenDatePicker(false);
                  }}
                  maximumDate={new Date()}
                />
                {bdayError ? (
                  <Text style={acrossAllScreens.ErrorText}>{bdayError}</Text>
                ) : null}
              </View>
              {/* Gender Button removed */}
              {/* <View style={[SignUpStyle.genderButton]}>
              <TouchableHighlight
                style={[
                  gender === 'male'
                    ? SignUpStyle.activeButton
                    : SignUpStyle.inactiveButton,
                  SignUpStyle.buttonContainer,
                ]}
                onPress={() => {
                  console.log('Male pressed');
                  setGender('male');
                }}>
                <Text style={SignUpStyle.signUpText}>Male</Text>
              </TouchableHighlight>
              <TouchableHighlight
                style={[
                  gender === 'female'
                    ? SignUpStyle.activeButton
                    : SignUpStyle.inactiveButton,
                  SignUpStyle.buttonContainer,
                ]}
                onPress={() => {
                  console.log('Female pressed');
                  setGender('female');
                }}>
                <Text style={SignUpStyle.signUpText}>Female</Text>
              </TouchableHighlight>
              </View> */}

              {/* <View style={SignUpStyle.inputWithErrorContainer}>
                <View
                  style={[
                    SignUpStyle.inputContainer,
                    {flexDirection: 'row', alignItems: 'center'},
                  ]}>
                  <CountryPicker
                    withFilter
                    withFlag
                    withCallingCode
                    withCallingCodeButton
                    countryCode={countryCode}
                    onSelect={(country) => {
                      setCountryCode(country.cca2);
                      setCallingCode(`+${country.callingCode[0]}`);
                    }}
                    containerButtonStyle={{ marginLeft: 8 }}
                  />

                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder={PHONE_NO_PLACEHOLDER}
                    keyboardType="phone-pad"
                    placeholderTextColor="grey"
                    underlineColorAndroid="transparent"
                    onChangeText={text => setPhNum(text)}
                  />
                </View>
                {/* {phNumError ? (
                  <Text style={acrossAllScreens.ErrorText}>{phNumError}</Text>
                ) : null} */}
              {/* callingCodeError ? (
                  <Text style={acrossAllScreens.ErrorText}>
                    {callingCodeError}
                  </Text>
                ) : null */}
              {/* </View> */}
              <View style={SignUpStyle.inputWithErrorContainer}>
                <View style={SignUpStyle.passwordInputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder={PASSWORD_PLACEHOLDER}
                    placeholderTextColor="grey"
                    secureTextEntry={true}
                    underlineColorAndroid="transparent"
                    onChangeText={text => setPassword(text)}
                  />
                </View>
                {passwordError ? (
                  <Text
                    style={[
                      acrossAllScreens.ErrorText,
                      {
                        width: 250,
                        position: 'relative',
                        marginTop: 10,
                        marginBottom: 10,
                      },
                    ]}>
                    {passwordError}
                  </Text>
                ) : (
                  <View style={{height: 22}} />
                )}
              </View>
              <View style={[SignUpStyle.inputWithErrorContainer]}>
                <View style={SignUpStyle.inputContainer}>
                  <TextInput
                    style={SignUpStyle.inputs}
                    placeholder={CONFIRM_PASSWORD_PLACEHOLDER}
                    placeholderTextColor="grey"
                    secureTextEntry={true}
                    underlineColorAndroid="transparent"
                    onChangeText={text => setCPassword(text)}
                  />
                </View>
                {cPasswordError ? (
                  <Text style={acrossAllScreens.ErrorText}>
                    {cPasswordError}
                  </Text>
                ) : null}
              </View>

              {errorMessage ? (
                <Text style={styles.signUpErrorMsg}>{errorMessage}</Text>
              ) : null}

              <View style={styles.checkBoxContainer}>
                <Checkbox.Android
                  status={checked ? 'checked' : 'unchecked'}
                  onPress={() => setChecked(!checked)}
                  color="#C3E7F5"
                  disabled={false}
                />
                <Text style={styles.label}>
                  By logging in you are agreeing to our{' '}
                  <Text onPress={onTermsClick} style={{fontWeight: 'bold'}}>
                    Terms
                  </Text>{' '}
                  and{' '}
                  <Text onPress={onPrivacyClick} style={{fontWeight: 'bold'}}>
                    Privacy Policy
                  </Text>
                </Text>
              </View>

              <TouchableHighlight
                style={[
                  SignUpStyle.signupButton,
                  {
                    marginTop: 10,
                    backgroundColor: checked ? '#C3E7F5' : '#D2E6EE',
                  },
                ]}
                underlayColor="#DDDDDD"
                disabled={!checked}
                onPress={() => {
                  console.log('Register pressed');
                  if (validateAllFields()) {
                    values = {
                      firstName: fName,
                      lastName: lName,
                      email: email,
                      password: password,
                      phoneNumber: phNum,
                      gender: gender,
                      dateOfBirth: bday + 'T00:00:00Z',
                    };
                    console.log(values);
                    dispatch(register(values));
                  } else console.log('Missing Fields for signup');
                }}>
                <Text style={SignUpStyle.signUpText}>Sign up</Text>
              </TouchableHighlight>
              <TouchableOpacity
                style={SignUpStyle.screenRedirectButton}
                onPress={() => {
                  console.log('Login Screen');
                  // navigation.push('LoginScreen');
                  navigation.reset({
                    index: 0,
                    routes: [
                      {name: 'WelcomeStack', params: {screen: 'LoginScreen'}},
                    ],
                  });
                }}>
                <Text style={[acrossAllScreens.H2]}>
                  Already have an account?
                </Text>
                <Text style={[acrossAllScreens.H3]}> Log In here!</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  label: {
    fontSize: 16,
    color: '#333',
  },
  checkBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    width: 225,
  },
  signUpErrorMsg: {
    color: 'red',
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 10,
    paddingHorizontal: 10,
  },
});

export default SignUpScreen;
