import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, ScrollView, FlatList, Dimensions, ImageBackground } from 'react-native';
import Stories from 'react-insta-stories';
import { TouchableHighlight, TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
const {width, height} = Dimensions.get("window");

// source={require("../../static/Images/mountain.jpg")}
function SeeMore () {
    return (
        <View style={StoryScreenStyles.container}>
        <ImageBackground style={StoryScreenStyles.imageContainer} source={require("../../static/Images/mountain.jpg")} >
            <TouchableOpacity 
                style={StoryScreenStyles.touchableHighlightContainer} 
                onPress={() => console.log('previus pressed')} >    
            </TouchableOpacity>
        
        </ImageBackground>
        </View>
    )
}

const stories = [
    { 
        url: 'https://picsum.photos/1080/1920', 
        // seeMore: <SeeMore />, 
        header: { 
            heading: '<PERSON><PERSON>', 
            subheading: 'Posted 5h ago', 
            profileImage: 'https://picsum.photos/1000/1000' 
        } 
    }, 
    { 
        url: 'https://fsa.zobj.net/crop.php?r=dyJ08vhfPsUL3UkJ2aFaLo1LK5lhjA_5o6qEmWe7CW6P4bdk5Se2tYqxc8M3tcgYCwKp0IAyf0cmw9yCmOviFYb5JteeZgYClrug_bvSGgQxKGEUjH9H3s7PS9fQa3rpK3DN3nx-qA-mf6XN',
        header: { 
            heading: 'Mohit Karekar',
            subheading: 'Posted 32m ago', 
            profileImage: 'https://picsum.photos/1080/1920' 
        } 
    }, 
    { 
        url: 'https://media.idownloadblog.com/wp-content/uploads/2016/04/iPhone-wallpaper-abstract-portrait-stars-macinmac.jpg', 
        header: { 
            heading: 'mohitk05/react-insta-stories', 
            subheading: 'Posted 32m ago', 
            profileImage: 'https://avatars0.githubusercontent.com/u/24852829?s=400&v=4' 
        } 
    }, 
    { 
        url: 'https://storage.googleapis.com/coverr-main/mp4/Footboys.mp4', 
        type: 'video', 
        duration: 1000 
    }, 
    { 
        url: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4', 
        type: 'video', 
        // seeMore: <SeeMore /> 
    }, 
    { 
        url: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4', 
        type: 'video' 
    }
]
export function StoriesScreen () {
    const [count, setCount] = useState(0);
    const onPress = () => setCount(prevCount => prevCount + 1);

    return (
        <View >
            <ImageBackground style={StoryScreenStyles.imageContainer} source={require("../../static/Images/mountain.jpg")}>
            <TouchableWithoutFeedback onPress={()=> console.log('press story')}>
            {/* <ImageBackground style={StoryScreenStyles.imageContainer} source={require("../../static/Images/mountain.jpg")}/> */}
            </TouchableWithoutFeedback>
            </ImageBackground>
            <TouchableHighlight />
        </View>    
    )
}

export default StoriesScreen;

export const StoryScreenStyles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
    },
    imageContainer: {
        width: width,
        height: height,
        padding: 2
    },
    touchableHighlightContainer: {
        width: width/5,
        height: height,
        flex: 1
    }
})

/*
    <View >
        <TouchableWithoutFeedback onPress={()=> console.log('press story')}>
        <Image style={StoryScreenStyles.imageContainer} source={require("../../static/Images/mountain.jpg")}/>
        </TouchableWithoutFeedback>
        <TouchableHighlight />
    </View>
*/