import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import SearchBox from '../Common/SearchBox';

export function TagFriendsScreen({navigation, onFriendSelect}: any) {
  const friends = require('../../static/FriendsStatic/FrequentFriendsList.json');
  const [selectedItems, setSelectedItems] = useState([]);

  const onClick = category => {
    if (selectedItems.includes(category)) {
      setSelectedItems(prevItems =>
        prevItems.filter(item => item !== category),
      );
    } else {
      setSelectedItems(prevItems => [...prevItems, category]);
    }
  };

  useEffect(() => {
    onFriendSelect(selectedItems);
    console.log(selectedItems);
  }, [selectedItems]);
  // const handleSelectFriend = () => {
  //   // Call the callback function passed from the parent component
  //   onFriendSelect(selectedItems);
  // };
  return (
    <ScrollView
      style={{
        backgroundColor: 'white',
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,
        paddingBottom: 20,
      }}>
      <Text
        style={[
          // styles.MarginStyle,
          styles.SubHeaderStyle,
          {marginBottom: 10, alignSelf: 'center'},
        ]}>
        Search friends to Tag
      </Text>
      <View style={styles.MarginStyle}>
        <SearchBox />
      </View>
      <Text
        style={[styles.MarginStyle, styles.SubHeaderStyle, {marginBottom: 15}]}>
        Frequent Friends
      </Text>
      <View style={styles.ViewFriendsStyle}>
        {friends.Data.map(fren => {
          return (
            <TouchableOpacity
              onPress={() => {
                onClick(fren.userName);
                // handleSelectFriend();
                console.log(fren.userName);
              }}
              key={fren.userName}
              style={[
                styles.FriendsStyle,
                selectedItems.includes(fren.userName) && styles.isClicked,
              ]}>
              <Text>{fren.userName}</Text>
            </TouchableOpacity>
          );
        })}
      </View>
      {/* <TouchableOpacity onPress={() => navigation.push('AllFriends')}>
        <Text>Done</Text>
      </TouchableOpacity> */}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  MarginStyle: {marginLeft: 10},
  FriendsStyle: {
    borderWidth: 1.5,
    borderColor: 'black',
    marginLeft: 10,
    marginTop: 5,
    marginBottom: 5,
    borderRadius: 15,
    padding: 6,
    fontSize: 16,
  },
  ViewFriendsStyle: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginLeft: 2,
    marginRight: 2,
  },
  SubHeaderStyle: {
    fontSize: 24,
  },
  isClicked: {
    backgroundColor: 'yellow',
  },
});

export default TagFriendsScreen;
