import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
  Platform,
  Image,
  Linking,
  AppState,
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { useDispatch } from 'react-redux';
import { deleteUser } from '../../redux/User/UserAction';
import Toast from 'react-native-simple-toast';
import AppleHealthKit from 'react-native-health';
import {
  initialize,
  requestPermission,
  getGrantedPermissions,
  revokeAllPermissions,
  readRecords,
} from 'react-native-health-connect';
import { useAppSelector } from '../../redux/Store';
import { useFocusEffect } from '@react-navigation/native';
import moment from 'moment-timezone';
import AsyncStorage from '@react-native-async-storage/async-storage';


export function AccountSettingsScreen({ route, navigation }: any) {
  let title: string;
  let message: string;
  const authState = useAppSelector(state => state.auth);
  const { userId } = authState;
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteText, setDeleteText] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [selectedHealthApp, setSelectedHealthApp] = useState<any>(null);
  const healthPermission: any = {
    permissions: {
      read: [
        AppleHealthKit.Constants.Permissions.StepCount,
        AppleHealthKit.Constants.Permissions.Steps,
        AppleHealthKit.Constants.Permissions.DistanceWalkingRunning,
        AppleHealthKit.Constants.Permissions.DistanceCycling,
        AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
        AppleHealthKit.Constants.Permissions.Workout,
        AppleHealthKit.Constants.Permissions.FlightsClimbed,
        AppleHealthKit.Constants.Permissions.BasalEnergyBurned,
        AppleHealthKit.Constants.Permissions.ActivitySummary,
      ],
      write: [],
    },
  };
  useFocusEffect(
    useCallback(() => {
      // run immediately on focus
      checkHealthConnection();
  
      // subscribe to app coming back to foreground
      const sub = AppState.addEventListener('change', nextState => {
        if (nextState === 'active') {
          checkHealthConnection();
        }
      });
  
      // cleanup on blur
      return () => {
        sub.remove();
      };
    }, [])
  );

  const checkHealthConnection = async () => {
    if (Platform.OS === 'ios') {
      AppleHealthKit.getAuthStatus(healthPermission, (err, result) => {
        if (err) {
          console.log('Error getting Apple Health auth status:', err);
          return;
        }

        const grantedIosResult = result.permissions.read.filter(
          item => item === 0, 
        ).length;
        const deniedIosResult = result.permissions.read.filter(
          item => item === 1,
        ).length;
        // if (grantedResult > 0) {
        //   setSelectedHealthApp('Apple Health');
        //   setIsConnected(true);
        // } else {
        //   setIsConnected(false);
        // }
        console.log('Apple Health permissions:', result.permissions.read);
        console.log('Apple Health permissions granted:', grantedIosResult);
        console.log('Apple Health permissions denied:', deniedIosResult);
        if (grantedIosResult > 0 && deniedIosResult === 0) {
         
          setIsConnected(false);
       
        } 
        else {
          setSelectedHealthApp('Apple Health');

          setIsConnected(true);
        
        }
      });
    } else {
      try {
        console.log('Initializing Health Connect...');
        await initialize();
        console.log('Health Connect initialized successfully');

        const permissions = await getGrantedPermissions();
        console.log('Granted permissions:', permissions);

        if (permissions.length > 0) {
          setSelectedHealthApp('Health Connect');
          setIsConnected(true);
        } else {
          setIsConnected(false);
        }
      } catch (error) {
        console.log('Health Connect initialization failed:', error);
        setIsConnected(false);

        // Check if it's a specific initialization error
        if (error && typeof error === 'object' && 'message' in error) {
          const errorMessage = (error as Error).message;
          if (errorMessage.includes('not initialized') || errorMessage.includes('not available')) {
            console.log('Health Connect app may not be installed or available');
          }
        }
      }
    }
  };

  const revokePermissions = async () => {
      if (Platform.OS === 'ios') {
        Alert.alert(
          'Manage Permissions',
          'Please go to Settings > Health > Sharing > Apps > NAPOZ to manage permissions on iOS.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Update',
              onPress: () => {
                Linking.openURL('x-apple-health://');
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Manage Permissions',
          'Please go to the Health Connect app to manage permissions on Android.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Update',
              onPress: () => {
                Linking.openURL('https://play.google.com/store/apps/details?id=com.google.android.apps.healthdata').catch(() => {
                  Alert.alert('Error', 'Unable to open Health Connect app.');
                });
              },
            },
          ]
        );
      }
    };

  const requestPermissions = async () => {
    if (Platform.OS === 'ios') {
      AppleHealthKit.initHealthKit(healthPermission, err => {
        if (err) {
          Alert.alert('Permission Denied', 'Could not connect to Apple Health');
          return;
        }
        setSelectedHealthApp('Apple Health');
        // setIsConnected(true);
      });
    } else {
      try {
        // Ensure Health Connect is initialized before requesting permissions
        await initialize();
        console.log('Initializing Health Connect before requesting permissions...');

        const granted = await requestPermission([
          {
            accessType: 'read',
            recordType: 'Steps',
          },
          {
            accessType: 'write',
            recordType: 'Steps',
          },
          {
            accessType: 'write',
            recordType: 'ActiveCaloriesBurned',
          },
          {
            accessType: 'read',
            recordType: 'ActiveCaloriesBurned',
          },
          {
            accessType: 'write',
            recordType: 'TotalCaloriesBurned',
          },
          {
            accessType: 'read',
            recordType: 'TotalCaloriesBurned',
          },
          {
            accessType: 'write',
            recordType: 'Distance',
          },
          {
            accessType: 'read',
            recordType: 'Distance',
          },
          {
            accessType: 'read',
            recordType: 'ExerciseSession',
          },
          {
            accessType: 'write',
            recordType: 'ExerciseSession',
          },
        ]);

        if (granted) {
          setSelectedHealthApp('Health Connect');
          console.log('Health Connect permissions granted:', granted);
        
          setIsConnected(true);
        } else {
          Alert.alert(
            'Permission Denied',
            'Could not connect to Health Connect. Please grant the necessary permissions in the Health Connect app.',
          );
        }
      } catch (error: any) {
        console.log('Error requesting Health Connect permissions:', error);

        let errorMessage = 'Please install Google Health Connect app.';
        if (error && error.message) {
          if (error.message.includes('not initialized')) {
            errorMessage = 'Health Connect is not properly initialized. Please restart the app and try again.';
          } else if (error.message.includes('not available')) {
            errorMessage = 'Health Connect is not available on this device. Please install Google Health Connect app from the Play Store.';
          }
        }

        Alert.alert('Error', errorMessage);
      }
    }
  };

  const dispatch = useDispatch();

  const deleteHandler = async () => {
    try {
      await dispatch(deleteUser(userId)).unwrap(); // Ensures the logout action completes

      console.log('User deleted and successfully logged out');
      Toast.show('User Account Deleted Successfully', Toast.LONG);
      navigation.reset({
        index: 0,
        routes: [{ name: 'WelcomeStack', params: { screen: 'LoginScreen' } }],
      });
    } catch (error) {
      Toast.show('User Account Deletion Failed', Toast.LONG);
      console.log('Error during delete and logout:', error);
    }
  };

  const confirmAlert = (title: string, message: string) => {
    Alert.alert(title, message, [
      {
        text: 'Cancel',
        onPress: () => console.log(`${title} canceled`),
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: () => {
          console.log(`${title}`);
          if (title == 'Delete Account') {
            // deleteHandler();
            setDeleteModalVisible(true);
          }
        },
      },
    ]);
  };

  return (
    <View style={styles.container}>
      <View style={[styles.HeaderStyle]}>
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>Account</Text>
        </View>
      </View>
      {/* <TouchableOpacity
              style={[styles.Button, styles.ButtonGap]}
              onPress={() => {
                navigation.push('InterestScreen')
              }}>
              <Text style={acrossAllScreens.H2}>Update User Interests</Text>
              <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity> */}
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
          navigation.push('ChangePasswordScreen');
        }}>
        <Text style={acrossAllScreens.H2}>Change Account Password</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
          navigation.push('BaseUnitsScreen');
        }}>
        <Text style={acrossAllScreens.H2}>Change Base Units</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
          title = 'Delete Account';
          message = 'Are you sure you want to Delete this account?';
          confirmAlert(title, message);
        }}>
        <Text style={acrossAllScreens.H2}>Delete Account</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <View style={isConnected? [ styles.ButtonGap]:[ styles.ButtonGap, styles.Button]}>
        <Text style={acrossAllScreens.H2}>Connect Health App</Text>
        <TouchableOpacity
          style={isConnected ?[styles.button, styles.buttonAlign] :styles.button}
          onPress={() => {
            if (isConnected) {
              revokePermissions();
            
            }
            else {
              requestPermissions();
            }
          }}>
          <Text style={styles.buttonText}>
            {isConnected ? `Check ${selectedHealthApp} for permissions (Tap to Update)` : 'Tap to Connect'}
            {/* {isConnected ? `${selectedHealthApp} Connected` : (isAllDenied ? 'All Permissions Denied (Tap to Update)' :(isSomeConnected?`Some ${selectedHealthApp} Permissions Granted(Tap to Update)` :'Tap to Connect'))} */}
          </Text>
        </TouchableOpacity>
      </View>
      <Modal
        animationType="slide"
        transparent
        visible={deleteModalVisible}
        onRequestClose={() => setDeleteModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Confirm Account Deletion</Text>
            <Text style={styles.modalText}>Type "delete" to confirm:</Text>
            <TextInput
              style={styles.input}
              placeholder="Type here..."
              onChangeText={setDeleteText}
              value={deleteText}
              autoCapitalize="none"
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setDeleteText('');
                  setDeleteModalVisible(false);
                }}>
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={() => {
                  if (deleteText.toLowerCase() === 'delete') {
                    setDeleteModalVisible(false);
                    deleteHandler();
                  } else {
                    Toast.show(
                      'Error! You must type "delete" to proceed.',
                      Toast.LONG,
                    );
                  }
                }}>
                <Text style={styles.modalButtonText}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#000',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
    },
    buttonAlign:{marginTop: 8, 
      alignSelf: 'flex-start',    },
  buttonText: {
    color: 'white',
    fontSize: 12,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 25,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editProfileText: {
    marginTop: 10,
    // color: 'black',
    // fontWeight:
  },
  inputField: {
    borderWidth: 1,
    borderColor: 'black',
    padding: 10,
    marginVertical: 10,
    borderRadius: 8,
    color: 'black',
  },
  inputLine: {
    borderBottomWidth: 0.2,
    borderColor: 'black',
    paddingBottom: 0.5,
    paddingTop: 2,
    marginBottom: 10,
    color: 'black',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },

  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ButtonPos: {
    marginTop: 70,
    alignItems: 'center',
  },
  Button: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ButtonGap: { marginBottom: 40 },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  modalText: {
    fontSize: 14,
    marginBottom: 10,
  },
  input: {
    width: '100%',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    padding: 10,
    alignItems: 'center',
    borderRadius: 5,
  },
  cancelButton: {
    backgroundColor: '#ccc',
    marginRight: 5,
  },
  confirmButton: {
    backgroundColor: '#c3e7f5',
    marginLeft: 5,
  },
  modalButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default AccountSettingsScreen;
