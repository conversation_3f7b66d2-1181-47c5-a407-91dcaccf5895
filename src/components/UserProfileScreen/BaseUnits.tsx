import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
  Image,Platform
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import SignUpStyle from '../../styles/SignUp';
import {useDispatch} from 'react-redux';
import Toast from 'react-native-simple-toast';
import {useSelector} from 'react-redux';
import {setSelectedUnit} from '../../redux/Auth/AuthSlice';

export function BaseUnitsScreen({route, navigation}: any) {
  
const reduxUnit = useSelector((state: any) => state.auth.selectedUnit);
const [unit, setUnit] = useState(reduxUnit);
const dispatch = useDispatch();


  

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={acrossAllScreens.ScreenBackground}>
        <View style={acrossAllScreens.ScreenBorders}>
          <View style={styles.HeaderStyle}>
            <TouchableOpacity
              style={[
                acrossAllScreens.backImageContainer,
                styles.HeaderbackButton,
              ]}
              onPress={() => navigation.goBack()}>
              <Image
                style={acrossAllScreens.backImage}
                source={require('../../assets/images/back.png')}
              />
            </TouchableOpacity>
            <View style={styles.HeaderHorizontalPosition}>
              <Text style={acrossAllScreens.ScreenHeaderText}>
                Base Units
              </Text>
            </View>
          </View>

          <View style={styles.unitSelectorContainer}>
            <Text style={styles.unitSelectorLabel}>
              Choose your preferred unit system:
            </Text>
            <View style={styles.unitOptionsRow}>
              <TouchableOpacity
                style={[
                  styles.unitButton,
                  unit === 'METRIC' && styles.unitButtonSelected,
                ]}
                onPress={() => setUnit('METRIC')}
              >
                <Text style={unit === 'METRIC' ? styles.unitTextSelected : styles.unitText}>
                  Metric
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.unitButton,
                  unit === 'IMPERIAL' && styles.unitButtonSelected,
                ]}
                onPress={() => setUnit('IMPERIAL')}
              >
                <Text style={unit === 'IMPERIAL' ? styles.unitTextSelected : styles.unitText}>
                  Imperial
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.bottomButtonContainer}>
            <TouchableOpacity
              style={SignUpStyle.signupButton}
              onPress={() => {
                dispatch(setSelectedUnit(unit));
                Toast.show('Units updated');
                navigation.goBack();
              }}>
              <Text style={SignUpStyle.signUpText}> Update Units</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  inputLine: {
    borderBottomWidth: 0.2,
    borderColor: 'black',
    paddingBottom: 0.5,
    // paddingTop: 2,
    marginBottom: 16,
    color: 'black',
    paddingTop: Platform.OS === 'android' ? 8 : 12,
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ButtonPos: {
    marginTop: 70,
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4, // Add space above error text
    marginBottom: 10, // Ensure spacing between error text and next input field
  },  
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 32,
    width: '100%',
    alignItems: 'center',
  },
  unitButton: {
    paddingVertical: 10,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderColor: 'gray',
    borderRadius: 8,
  },
  unitButtonSelected: {
    backgroundColor: '#C3E7F5',
    borderColor: 'black',
  },
  unitText: {
    color: 'black',
    fontSize: 16,
  },
  unitTextSelected: {
    // color: '#007AFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  unitSelectorContainer: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  unitSelectorLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  unitOptionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
});

export default BaseUnitsScreen;
