import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import SignUpStyle from '../../styles/SignUp';
import {useDispatch} from 'react-redux';
import {
  addUserImage,
  deleteUserImage,
  updateUser,
} from '../../redux/User/UserAction';
import {resetUpdateSuccess} from '../../redux/User/UserSlice';
import ChangePasswordScreen from './changePassword';
import {useAppSelector} from '../../redux/Store';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import FastImage from 'react-native-fast-image';
import {baseUrl} from '../../utils/Utils';
import {Asset, launchImageLibrary} from 'react-native-image-picker';
import Toast from 'react-native-toast-message';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {compressImage} from '../../utils/media';

export function EditProfileScreen({route, navigation}: any) {
  const authState = useAppSelector(state => state.auth);
  const {token, userId} = authState;
  const userState = useAppSelector(state => state.user);
  const {isUpdateSuccess} = userState;
  const [fName, setFName] = useState(route.params?.firstName);
  const [lName, setLName] = useState(route.params?.lastName);
  const [bio, setBio] = useState(route.params?.bio);
  const [image, setImage] = useState(route.params?.imageReference);
  const [localImage, setLocalImage] = useState<Asset | null>(null);
  const [imageLoading, setImageLoading] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    if (isUpdateSuccess) {
      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTabsNavigator', params: {screen: 'Profile'}}],
      });

      //reset isUpdateSuccess here
      dispatch(resetUpdateSuccess());
    } else {
      console.log(
        'User profile Update failed or user working on updating porfile',
      );
    }
  }, [isUpdateSuccess]);

  const onChangeProfileImage = () => {
    launchImageLibrary(
      {
        selectionLimit: 1,
        mediaType: 'photo',
        includeExtra: true,
      },
      async response => {
        console.log('Image response:', response, response.assets[0]?.uri);
        if (response?.assets && response.assets.length > 0) {
          setImageLoading(true);
          setLocalImage(response.assets[0]);
          console.log(
            'Image selected:',localImage)
          const file = response.assets[0];
          let outputPath = `${file.uri}_compress.jpg`;
          try {
            await compressImage(file.uri + '', outputPath);
          } catch (error) {
            outputPath = response.assets[0].uri + '';
          }

          try {
            const result = await dispatch(
              addUserImage({
                uri: outputPath,
                name:
                  response.assets[0].fileName ||
                  `profile_${userId}_${new Date().getTime()}.png`,
                type: 'image/jpeg',
              }),
            ).unwrap();

            if (result.code == 200) {
              if (result.body.data.imageReference) {
                setImage(result.body.data.imageReference);
                setLocalImage(null);
              } else {
                setImage(null);
              }
              Toast.show({
                type: 'success',
                text1: 'Image uploaded',
                text2: 'Profile picture upload successfully!',
              });
            } else {
              Toast.show({
                type: 'error',
                text1: 'Failed to upload profile picture.',
                text2: 'Something went wrong.',
              });
            }
          } catch (error) {
            Toast.show({
              type: 'error',
              text1: 'Failed to upload profile picture.',
              text2: 'Something went wrong.',
            });
          }
          setImageLoading(false);
        }
      },
    );
  };

  const onSaveUpdateProfile = async () => {
    const userData = {
      firstName: fName,
      lastName: lName,
      // imageReference: image,
      bio: bio,
      gender: 'none',
    };
    try {
      const result = await dispatch(updateUser(userData)).unwrap();
      if (result && result.id) {
        Toast.show({
          type: 'success',
          text1: 'Profile updated',
          text2: 'Profile updated successfully!',
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to update profile.',
          text2: 'Something went wrong.',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Failed to update profile.',
        text2: 'Something went wrong.',
      });
    }
  };
  const onDeleteImageClick = async () => {
    Alert.alert(
      'Delete Profile Picture',
      'Are you sure you want to delete profile picture?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            onDeleteImage();
          },
          style: 'destructive',
        },
      ],
    );
  };

  const onDeleteImage = async () => {
    setImageLoading(true);
    try {
      const result = await dispatch(deleteUserImage('')).unwrap();
      if (result.code == 200) {
        Toast.show({
          type: 'success',
          text1: 'Profile picture deleted',
          text2: 'Profile picture deleted successfully!',
        });
        setImage(null);
        setLocalImage(null);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to delete profile picture.',
          text2: 'Something went wrong.',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Failed to delete profile picture.',
        text2: 'Something went wrong.',
      });
    }
    setImageLoading(false);
  };

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={{flexGrow: 1, backgroundColor: 'white'}}
      enableOnAndroid={true}
      showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <View style={[styles.HeaderStyle]}>
          <TouchableOpacity
            style={[
              acrossAllScreens.backImageContainer,
              styles.HeaderbackButton,
            ]}
            onPress={() => navigation.goBack()}
            disabled={imageLoading}>
            <Image
              style={[
                acrossAllScreens.backImage,
                {tintColor: imageLoading ? 'gray' : 'black'},
              ]}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <View style={styles.HeaderHorizontalPosition}>
            <Text style={[acrossAllScreens.ScreenHeaderText]}>Profile</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.profileImageContainer}
          disabled={imageLoading}
          onPress={() => {
            onChangeProfileImage();
          }}>
          <View style={styles.profileImage}>
            {image && image !== 'imageReference' && (
              <TouchableOpacity
                disabled={imageLoading}
                onPress={onDeleteImageClick}
                style={styles.deleteIconContainer}>
                <MaterialCommunityIcons
                  name="delete-circle"
                  size={30}
                  color="red"
                />
              </TouchableOpacity>
            )}
            <FastImage
              source={
                localImage
                  ? {uri: localImage.uri}
                  : image && image !== 'imageReference'
                  ? {
                      uri: image,
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }
                  : require('../../static/Images/user.png')
              }
              style={styles.profileImage}
            />
            {imageLoading && (
              <View style={styles.imageLoadingView}>
                <ActivityIndicator size={'large'} />
              </View>
            )}
          </View>
          <Text style={[styles.editProfileText, acrossAllScreens.H3]}>
            Edit picture
          </Text>
        </TouchableOpacity>

        <Text style={acrossAllScreens.H2}>First Name</Text>

        <TextInput
          value={fName}
          onChangeText={setFName}
          style={[styles.inputLine, acrossAllScreens.InputTextRegular]}
          placeholder="First Name"
          placeholderTextColor="grey"
          underlineColorAndroid="transparent"
        />
        <Text style={acrossAllScreens.H2}>Last Name</Text>
        <TextInput
          value={lName}
          onChangeText={setLName}
          style={[styles.inputLine, acrossAllScreens.InputTextRegular]}
          placeholder="Last Name"
          placeholderTextColor="grey"
          underlineColorAndroid="transparent"
        />
        <Text style={acrossAllScreens.H2}>Bio</Text>
        <TextInput
          value={bio}
          onChangeText={setBio}
          placeholderTextColor="grey"
          style={[styles.inputLine, acrossAllScreens.InputTextRegular]}
          placeholder="Bio"
          underlineColorAndroid="transparent"
        />
        {/*update password API yet to be integrated */}
        {/* <View>
          <TouchableOpacity
            style={styles.PasswordButton}
            onPress={() => navigation.navigate(ChangePasswordScreen)}>
            <Text style={acrossAllScreens.H2}>Change Password</Text>
            <AntDesign name="right" size={20} color="black" />
          </TouchableOpacity>
        </View> */}
        <View style={styles.ButtonPos}>
          <TouchableOpacity
            style={[
              SignUpStyle.signupButton,
              imageLoading && {backgroundColor: 'gray'},
            ]}
            disabled={imageLoading}
            onPress={onSaveUpdateProfile}>
            <Text style={SignUpStyle.signUpText}> Save Changes</Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
}

const styles = StyleSheet.create({
  deleteIconContainer: {
    position: 'absolute',
    top: -5,
    right: 5,
    zIndex: 200,
    backgroundColor: 'white',
    borderRadius: 100,
  },
  imageLoadingView: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    zIndex: 10,
    borderRadius: 50,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 25,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editProfileText: {
    marginTop: 10,
    // color: 'black',
    // fontWeight:
  },
  inputField: {
    borderWidth: 1,
    borderColor: 'black',
    padding: 10,
    marginVertical: 10,
    borderRadius: 8,
    color: 'black',
  },
  inputLine: {
    borderBottomWidth: 0.2,
    borderColor: 'black',
    paddingBottom: 0.5,
    paddingTop: 2,
    marginBottom: 10,
    color: 'black',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ButtonPos: {
    marginTop: 70,
    alignItems: 'center',
  },
  PasswordButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default EditProfileScreen;
