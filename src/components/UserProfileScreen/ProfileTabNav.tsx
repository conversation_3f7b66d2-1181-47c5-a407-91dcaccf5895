import React,{ useState, ReactElement} from 'react';
import { <PERSON>, Text, Button, ScrollView, StyleSheet, Dimensions, Image } from 'react-native';
import { TouchableHighlight } from 'react-native-gesture-handler';

import { ChallengeHomeScreen } from '../ChallengeHomeScreen/ChallengeHomeScreen';

import { UserProfileScreenStyle } from '../../styles/UserProfileStyles';



function ConditionalRender(activeTab: any): ReactElement {
    if (activeTab === 0) {
        return (
            <View style={UserProfileScreenStyle.contentContainer}>
                {images.map((image,index) => {
                    return (
                        <TouchableHighlight onPress={() => NaN}>
                            <View key={index} style={UserProfileScreenStyle.imageContainer}>
                                <Image 
                                    style={UserProfileScreenStyle.image}
                                    source={image}/>
                            </View>
                        </TouchableHighlight>   
                )})}
            </View>
        )
    };
    if (activeTab === 1) {
        return (
            <View>
                <ChallengeHomeScreen />
            </View>
        )
    };
    return (
        <View style={UserProfileScreenStyle.contentContainer}>
            {images.map((image,index) => {
                return (
                    <View key={index} style={UserProfileScreenStyle.imageContainer}>
                        <Image 
                            style={UserProfileScreenStyle.image}
                            source={image}/>
                    </View>
            )})}
        </View>
    )
}

export function ProfileTabNav() {
    const [activeTab, setActivetab] = useState<number>(0);

    const onTabSelect = (tab:number) => {
        setActivetab(tab);
    }
    
    return (
        <View>
            <View style={UserProfileScreenStyle.profileTab}>
                <TouchableHighlight 
                    underlayColor='grey'
                    activeOpacity={.85}
                    onPress={() => {
                        setActivetab(0)
                    }}> 
                    <Text style={UserProfileScreenStyle.profileTabText}>Posts</Text>
                </TouchableHighlight>
                <TouchableHighlight 
                    underlayColor='grey'
                    activeOpacity={.85}
                    onPress={() =>{
                        setActivetab(1)
                    }}> 
                    <Text style={UserProfileScreenStyle.profileTabText}>Challenges</Text>
                </TouchableHighlight>
                <TouchableHighlight 
                    underlayColor='grey'
                    activeOpacity={.85}
                    onPress={() =>{
                        setActivetab(2)
                    }}> 
                    <Text style={UserProfileScreenStyle.profileTabText}>Stats</Text>
                </TouchableHighlight>
            </View>
            {ConditionalRender(activeTab)}   
        </View>    
    )
}

export default ProfileTabNav;

const styles = StyleSheet.create({

});

const images = [
    require('../../static/Images/beach.jpg'),
    require('../../static/Images/forest.jpg'),
    require('../../static/Images/forest.jpg'),
    require('../../static/Images/mountain.jpg'),
    require('../../static/Images/beach.jpg'),
    require('../../static/Images/mountain.jpg'),
    require('../../static/Images/beach.jpg'),
    require('../../static/Images/forest.jpg'),
    require('../../static/Images/mountain.jpg'),
];