import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import Icon from 'react-native-vector-icons/Ionicons';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {
  getChallengeCreatedByUser,
  getChallengeFeed,
} from '../../redux/Challenge/ChallengeAction';
import {getJoinedLeaderBoard} from '../../redux/LeaderBoard/LeaderBoardAction';
import {useFocusEffect} from '@react-navigation/native';
import Video from 'react-native-video';
import FastImage from 'react-native-fast-image';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import acrossAllScreens from '../../styles/acrossAllScreens';

const Tab = createMaterialTopTabNavigator();

function SearchableListScreen({
  initialData,
  isChallenge,
  onEndReached,
  ListFooterComponent,
  navigation,
}: any) {
  const [data, setData] = useState(initialData);
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;

  console.log(data, 'itemitemitem');

  useEffect(() => {
    setData(initialData);
  }, [initialData]);

  const onItemClick = (item: any) => {
    if (isChallenge) {
      navigation.navigate('ChallengeDetails', {challengeId: item.challengeId});
    } else {
      navigation.navigate('LeaderboardDetailsScreen', {
        name: item.pointsTitle,
        creator: item.userId,
        challengeId: item?.challengeId,
      });
    }
  };

  return (
    <View style={{flex: 1}}>
      <FlatList
        data={data}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.1}
        ListFooterComponent={ListFooterComponent}
        renderItem={({item}) => (
          <TouchableOpacity
            onPress={() => onItemClick(item)}
            style={styles.userContainer}>
            {item.mediaRefs &&
              item.mediaRefs.length > 0 &&
              (isVideoLink(item.mediaRefs[0]) ? (
                <Video
                  source={{
                    uri: item.mediaRefs[0],
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  useTextureView={false}
                  bufferConfig={{
                    minBufferMs: 2500,
                    maxBufferMs: 50000,
                    bufferForPlaybackMs: 2500,
                    bufferForPlaybackAfterRebufferMs: 2500,
                  }}
                  onError={error => console.log(error, 'errorerrorerrorerror')}
                  style={[styles.userImage]}
                  resizeMode="cover"
                  repeat
                  paused={true}
                />
              ) : (
                <FastImage
                  source={{
                    uri: item.mediaRefs[0],
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  style={[styles.userImage]}
                />
              ))}
            {isChallenge ? (
              <Text style={styles.activityTitle}> {item.title}</Text>
            ) : (
              <Text style={styles.activityTitle}>
                {item.title} : {item.points} {item.pointsTitle}
              </Text>
            )}
          </TouchableOpacity>
        )}
        keyExtractor={(item, index) => index.toString()}
      />
    </View>
  );
}

let bottomLoad = false;
function UserActivity({route, navigation}: any) {
  const {selectedTab, userId} = route.params;
  const dispatch = useAppDispatch();
  const [dataLoading, setDataLoading] = useState(false);
  const challengesFeed = useAppSelector(
    state => state.challenge.challengesFeed.data,
  );
  const nextPageToken = useAppSelector(
    state => state.challenge.challengesFeed.nextPageToken,
  );
  const hasNext = useAppSelector(
    state => state.challenge.challengesFeed.hasNext,
  );
  const leaderBoardList = useAppSelector(
    state => state.leaderBoard.leaderBoardList.data,
  );
  const nextPageLeaderboardToken = useAppSelector(
    state => state.leaderBoard.leaderBoardList.nextPageToken,
  );
  const hasLeaderboardNext = useAppSelector(
    state => state.leaderBoard.leaderBoardList.hasNext,
  );
  const [loading, setLoading] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        setDataLoading(true);
        dispatch(getChallengeFeed({userId})).finally(() => {
          setDataLoading(false);
        });
        dispatch(getJoinedLeaderBoard({userId: userId}));
      }
    }, [userId]),
  );

  const onExploreFeedEnd = useCallback(() => {
    if (challengesFeed.length > 0 && !loading && !bottomLoad && hasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeFeed({userId: userId, nextPageToken: nextPageToken}),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, challengesFeed, nextPageToken, loading, hasNext]);

  const onLeaderBoardFeedEnd = useCallback(() => {
    if (
      leaderBoardList.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasLeaderboardNext
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getJoinedLeaderBoard({
          userId: userId,
          nextPageToken: nextPageLeaderboardToken,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [
    dispatch,
    leaderBoardList,
    nextPageLeaderboardToken,
    loading,
    hasLeaderboardNext,
  ]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={{flex: 1, backgroundColor: 'white'}}>
      <View style={styles.headerView}>
        <TouchableOpacity
          style={acrossAllScreens.backImageContainer}
          onPress={onBackPress}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Activity</Text>
        <View style={{width: 30}} />
      </View>
      <Tab.Navigator
        initialRouteName={
          selectedTab === 'Challenges'
            ? 'ChallengesScreen'
            : 'LeaderboardsScreen'
        }>
        <Tab.Screen
          name="ChallengesScreen"
          children={() => (
            <SearchableListScreen
              initialData={challengesFeed}
              isChallenge={true}
              onEndReached={onExploreFeedEnd}
              navigation={navigation}
              ListFooterComponent={renderFooter}
            />
          )}
          options={{title: 'Challenges'}}
        />
        <Tab.Screen
          name="LeaderboardsScreen"
          children={() => (
            <SearchableListScreen
              initialData={leaderBoardList}
              isChallenge={false}
              navigation={navigation}
              onEndReached={onLeaderBoardFeedEnd}
              ListFooterComponent={renderFooter}
            />
          )}
          options={{title: 'Leaderboards'}}
        />
      </Tab.Navigator>
      {dataLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size={'large'} color="#87CEEB" />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  backImage: {
    width: 21,
    height: 21,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    paddingTop: 16,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 10,
    margin: 10,
    borderColor: '#ccc',
    borderWidth: 1,
    alignItems: 'center',
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
  },
  userContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  userImage: {
    width: 40,
    height: 40,
    borderRadius: 40,
    marginRight: 10,
    overflow: 'hidden',
  },
  button: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  buttonText: {
    color: '#333',
  },
});

export default UserActivity;
