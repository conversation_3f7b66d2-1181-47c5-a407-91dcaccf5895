import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import Icon from 'react-native-vector-icons/Ionicons';
import {useDispatch} from 'react-redux';
import {
  getFollowers,
  getFollowing,
  removeUser,
  unFollowUser,
} from '../../redux/User/UserAction';
import {useAppSelector} from '../../redux/Store';
import {useFocusEffect} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import FastImage from 'react-native-fast-image';
import {baseUrl} from '../../utils/Utils';
import acrossAllScreens from '../../styles/acrossAllScreens';

const Tab = createMaterialTopTabNavigator();

function SearchableListScreen({
  initialData,
  isFollowingTab,
  onEndReached,
  ListFooterComponent,
  onUnFollow,
  onRemove,
  navigation,
  userId,
  refreshing,
  onRefresh,
}: any) {
  const [data, setData] = useState(initialData);
  const userImage = require('../../static/Images/user.png');
  const currentUserId = useAppSelector(state => state.auth.userId);
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;

  useEffect(() => {
    setData(initialData);
  }, [initialData]);

  const onUserClick = (user: any) => {
    const userId = user.userId;
    if (currentUserId === userId) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('OtherUserProfileScreen', {userId: userId});
    }
  };

  return (
    <View style={{flex: 1}}>
      <FlatList
        data={data}
        renderItem={({item}) => (
          <TouchableOpacity
            onPress={() => onUserClick(item)}
            style={styles.userContainer}>
            <FastImage
              source={
                item?.imageReference &&
                item?.imageReference !== 'imageReference'
                  ? {
                      uri: item?.imageReference,
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }
                  : userImage
              }
              style={styles.userImage}
            />
            <Text style={{flex: 1}}>
              {`${item.firstName}  ${item.lastName}`}
            </Text>
            {isFollowingTab && currentUserId === userId ? (
              <TouchableOpacity
                style={styles.button}
                onPress={() => onUnFollow(item)}>
                <Text style={styles.buttonText}>Unfollow</Text>
              </TouchableOpacity>
            ) : (
              currentUserId === userId && (
                <TouchableOpacity
                  style={styles.button}
                  onPress={() => onRemove(item)}>
                  <Text style={styles.buttonText}>Remove</Text>
                </TouchableOpacity>
              )
            )}
          </TouchableOpacity>
        )}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.1}
        onRefresh={onRefresh}
        refreshing={refreshing}
        ListFooterComponent={ListFooterComponent}
        keyExtractor={(item, index) => index.toString()}
      />
    </View>
  );
}

let bottomLoad = false;
function UserConnectionsScreen({route, navigation}: any) {
  const {selectedTab, userId} = route.params;
  const followers = useAppSelector(state => state.user.follower.data);
  const nextPageToken = useAppSelector(
    state => state.user.follower.nextPageToken,
  );
  const hasNext = useAppSelector(state => state.user.follower.hasNext);
  const following = useAppSelector(state => state.user.following.data);
  const followingNextPageToken = useAppSelector(
    state => state.user.following.nextPageToken,
  );
  const followingHasNext = useAppSelector(
    state => state.user.following.hasNext,
  );
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [isFetching, setFetching] = useState(false);
  const currentUserId = useAppSelector(state => state.auth.userId);

  const dispatch = useDispatch();

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        setDataLoading(true);
        dispatch(getFollowers({userId})).finally(() => {
          setDataLoading(false);
        });
        dispatch(getFollowing({userId})).finally(() => {
          setDataLoading(false);
        });
      }
    }, [userId]),
  );

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onFollowingEnd = useCallback(() => {
    if (following.length > 0 && !loading && !bottomLoad && followingHasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getFollowing({userId: userId, nextPageToken: followingNextPageToken}),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, following, followingNextPageToken, loading, followingHasNext]);

  const onFollowEnd = useCallback(() => {
    if (followers.length > 0 && !loading && !bottomLoad && hasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getFollowers({userId: userId, nextPageToken: nextPageToken}),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, followers, nextPageToken, loading, hasNext]);

  const onBackPress = () => {
    navigation.goBack();
  };

  const onRemove = async (item: any) => {
    const body = {followerId: item.userId};
    const result = await dispatch(removeUser(body));
    if (removeUser.fulfilled.match(result)) {
      if (result.payload.data) {
        setDataLoading(true);
        dispatch(getFollowers({userId})).finally(() => {
          setDataLoading(false);
        });
        dispatch(getFollowing({userId})).finally(() => {
          setDataLoading(false);
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to remove user.',
          text2: 'Something went wrong.',
        });
      }
    } else if (removeUser.rejected.match(result)) {
      Toast.show({
        type: 'error',
        text1: 'Failed to remove user.',
        text2: 'Something went wrong.',
      });
    }
  };

  const onUnFollow = async (item: any) => {
    const body = {followerId: currentUserId, followedId: item.userId};
    const result = await dispatch(unFollowUser(body));
    if (unFollowUser.fulfilled.match(result)) {
      if (result.payload.data) {
        setDataLoading(true);
        dispatch(getFollowers({userId})).finally(() => {
          setDataLoading(false);
        });
        dispatch(getFollowing({userId})).finally(() => {
          setDataLoading(false);
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Failed to remove user.',
          text2: 'Something went wrong.',
        });
      }
    } else if (unFollowUser.rejected.match(result)) {
      Toast.show({
        type: 'error',
        text1: 'Failed to remove user.',
        text2: 'Something went wrong.',
      });
    }
  };

  const onRefresh = () => {
    setFetching(true);
    dispatch(getFollowers({userId})).finally(() => {
      setFetching(false);
    });
    dispatch(getFollowing({userId})).finally(() => {
      setFetching(false);
    });
  };

  return (
    <View style={{flex: 1, backgroundColor: 'white'}}>
      <View style={styles.headerView}>
        <TouchableOpacity
          style={acrossAllScreens.backImageContainer}
          onPress={onBackPress}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Connections</Text>
        <View style={{width: 30}} />
      </View>
      <Tab.Navigator
        initialRouteName={
          selectedTab === 'Followers' ? 'FollowersScreen' : 'FollowingScreen'
        }>
        <Tab.Screen
          name="FollowersScreen"
          children={() => (
            <SearchableListScreen
              initialData={followers}
              isFollowingTab={false}
              onEndReached={onFollowEnd}
              ListFooterComponent={renderFooter}
              onRemove={onRemove}
              userId={userId}
              onUnFollow={onUnFollow}
              navigation={navigation}
              onRefresh={onRefresh}
              refreshing={isFetching}
            />
          )}
          options={{title: 'Followers'}}
        />
        <Tab.Screen
          name="FollowingScreen"
          children={() => (
            <SearchableListScreen
              initialData={following}
              isFollowingTab={true}
              onEndReached={onFollowingEnd}
              ListFooterComponent={renderFooter}
              onRemove={onRemove}
              userId={userId}
              onUnFollow={onUnFollow}
              navigation={navigation}
              onRefresh={onRefresh}
              refreshing={isFetching}
            />
          )}
          options={{title: 'Following'}}
        />
      </Tab.Navigator>
      {dataLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size={'large'} color="#87CEEB" />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  headerTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  backImage: {
    width: 21,
    height: 21,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    paddingTop: 16,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 10,
    margin: 10,
    borderColor: '#ccc',
    borderWidth: 1,
    alignItems: 'center',
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
  },
  userContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  userImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  button: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  buttonText: {
    color: '#333',
  },
});

export default UserConnectionsScreen;
