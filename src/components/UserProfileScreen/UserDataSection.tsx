import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {UserProfileScreenStyle} from '../../styles/UserProfileStyles';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {CountAndDescription} from './countAndDescription';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {baseUrl} from '../../utils/Utils';
import {useAppSelector} from '../../redux/Store';
import FastImage from 'react-native-fast-image';

const userData = {
  name: '<PERSON>',
  username: '@Jon<PERSON><PERSON><PERSON>',
  about: '<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>',
  statCount: {
    followers: 234,
    following: 534,
    points: 248,
    challenges: 4,
    leaderboards: 3,
  },
};

export function UserDataSection(props: any) {
  const authState = useAppSelector(state => state.auth);
  const {token} = authState;

  const navigateToConnections = (tabName: string, userId: string) => {
    props.navigation.navigate('UserConnectionsScreen', {
      selectedTab: tabName,
      userId: userId,
    });
  };

  const navigateToEditProfile = () => {
    props.navigation.navigate('EditProfileScreen', {
      imageSource: require('../../static/Images/forest.jpg'),
      firstName: props?.firstName,
      lastName: props?.lastName,
      bio: props?.bio,
      imageReference: props?.imageReference,
    });
  };

  const followData = [
    {count: props.userProfile?.followersCount, description: 'Followers'},
    {count: props.userProfile?.followingCount, description: 'Following'},
  ];
  const miscellaneousData = [
    // {count: userData.statCount.points, description: 'Points'},
    {
      count: props.userProfile?.activeChallengesCount,
      description: 'Active Challenges',
    },
    {
      count: props.userProfile?.activeLeaderboardsCount,
      description: 'Active Leaderboards',
    },
  ];

  const onActiveItemClick = (desc: string) => {
    if (desc == 'Active Challenges') {
      if (props.isOwnProfile) {
        props.navigation.navigate('Challenges', {
          screen: 'ChallengeHome',
          params: {active: true},
        });
      } else {
        props.navigation.navigate('UserActivity', {
          userId: props.userId,
          selectedTab: 'Challenges',
        });
      }
    } else {
      if (props.isOwnProfile) {
        props.navigation.navigate('Leaderboard', {
          screen: 'LeaderboardHome',
          params: {active: true},
        });
      } else {
        props.navigation.navigate('UserActivity', {
          userId: props.userId,
          selectedTab: 'leaderBoard',
        });
      }
    }
  };

  return (
    <View
      style={[
        UserProfileScreenStyle.container,
        acrossAllScreens.ScreenBorders,
      ]}>
      {!props.isOwnProfile && (
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
          onPress={() => props.navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
      )}
      {props.isOwnProfile && (
        <TouchableOpacity
          style={{alignSelf: 'flex-end'}}
          onPress={() => props.navigation.navigate('UserSettingsScreen')}>
          <Ionicons name="settings-sharp" size={25} color="#b6dffc" />
        </TouchableOpacity>
      )}
      <FastImage
        source={
          props?.imageReference && props?.imageReference !== 'imageReference'
            ? {
                uri: props?.imageReference,
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            : require('../../static/Images/user.png')
        }
        style={UserProfileScreenStyle.displayPic}
      />
      <Text style={UserProfileScreenStyle.fullName}>{props.name}</Text>
      {/* <Text style={UserProfileScreenStyle.userName}>{userData.username}</Text> */}
      <Text style={UserProfileScreenStyle.description}>{props?.bio}</Text>
      {props.isOwnProfile ? (
        <TouchableOpacity onPress={navigateToEditProfile}>
          <Text style={styles.editText}>Edit Profile</Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          onPress={() => {
            props.isFollowing ? props.onUnFollowClick() : props.onFollowClick();
          }} style={styles.followButton}>
          <Text style={styles.followNowText}>
            {props.isFollowing ? 'Unfollow' : 'Follow Now'}
          </Text>
        </TouchableOpacity>
      )}
      <View style={UserProfileScreenStyle.followContainer}>
        {followData.map((data, index) => {
          return (
            <TouchableOpacity
              key={data.description}
              onPress={() =>
                navigateToConnections(data.description, props.userId)
              }
              activeOpacity={1}>
              <CountAndDescription
                count={data.count}
                description={data.description}
              />
            </TouchableOpacity>
          );
        })}
      </View>
      <View style={UserProfileScreenStyle.followContainer}>
        {miscellaneousData.map(data => {
          return (
            <CountAndDescription
              count={data.count}
              onPress={() => onActiveItemClick(data.description)}
              description={data.description}
              key={data.description}
            />
          );
        })}
      </View>
    </View>
  );
}

export default UserDataSection;

const styles = StyleSheet.create({
  followNowText: {
    lineHeight: 20,
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  editText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  HeaderbackButton: {
    // position: 'absolute',
    // left: 0,
    // top: 0,
    // bottom: 0,
    // justifyContent: 'center',
    // paddingHorizontal: 0,
    alignSelf: 'flex-start',
  },
  followButton: {
    backgroundColor: '#C3E7F5',
    borderRadius: 15,
    paddingHorizontal: 23,
    paddingVertical: 5,
     margin: 5,
  }
});
