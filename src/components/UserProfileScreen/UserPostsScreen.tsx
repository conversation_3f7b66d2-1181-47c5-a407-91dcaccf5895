import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  FlatList,
  Image,
  StyleSheet,
  Dimensions,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  AppState,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {
  ViewPortDetector,
  ViewPortDetectorProvider,
} from 'react-native-viewport-detector';
import Video from 'react-native-video';
import {isVideoLink} from '../../utils/Utils';
import FastImage from 'react-native-fast-image';
import moment from 'moment-timezone';
import {FlashList} from '@shopify/flash-list';
import {
  deletePost,
  getPostOfUser,
  likeUnlikeChallengeFromPost,
  likeUnlikePost,
  removeLikeUnlikeChallengeFromPost,
  removeLikeUnlikePost,
} from '../../redux/Post/PostAction';
import Popover from 'react-native-popover-view';
import {useIsFocused} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/Ionicons';
import {muteUnMuteVideo} from '../../redux/Post/PostSlice';
import convertToProxyURL from 'react-native-video-cache';
import acrossAllScreens from '../../styles/acrossAllScreens';
import DebounceTouchable from '../HomeScreen/DebounceTouchable';

const screenWidth = Dimensions.get('window').width;
let bottomLoad = false;
const UsertPostItem = ({item, userProfile, navigation, appState}: any) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [inViewPort, setInViewPort] = useState(false);
  const userState = useAppSelector(state => state.auth);
  const {token} = userState;
  const popoverRef = useRef<Popover>(null);
  const dispatch = useAppDispatch();
  const isOwnPost = userProfile?.userId == userState.userId;
  const mute = useAppSelector(state => state.post.mute);
  const [isMuted, setIsMuted] = useState(mute);
  const focus = useIsFocused();

  useEffect(() => {
    setIsMuted(mute);
  }, [mute]);

  const toggleMute = () => {
    dispatch(muteUnMuteVideo(!isMuted));
  };

  const handleScroll = (event: any) => {
    const position = event.nativeEvent.contentOffset.x;
    const activeIndex = Math.round(position / screenWidth);
    setActiveIndex(activeIndex);
  };

  const Dots = ({length, activeIndex}: any) => (
    <View style={styles.dotsContainer}>
      {new Array(length).fill(0).map((_, idx) => (
        <View
          key={idx}
          style={[
            styles.dot,
            idx === activeIndex ? styles.activeDot : styles.inactiveDot,
          ]}
        />
      ))}
    </View>
  );

  const onReportClick = () => {
    popoverRef.current?.requestClose();
  };

  const onEditClick = () => {
    popoverRef.current?.requestClose();
    navigation.navigate('CreatePostScreenStack', {
      screen: 'UpdatePostScreen',
      params: {postId: item.id, from: 'user_post'},
    });
  };

  const onChallnegePress = (item: any) => {
    if (item.startDate) {
      navigation.navigate('ChallengeDetails', {challengeId: item.id});
    } else if (item.challengeId) {
      navigation.navigate('ChallengeDetails', {challengeId: item.challengeId});
    }
  };

  const onDeleteClick = () => {
    popoverRef.current?.requestClose();
    Alert.alert('Delete Post', 'Are you sure you want to delete this post?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Delete',
        onPress: async () => {
          const result = await dispatch(deletePost(item?.id));
          if (result.payload.code == 200) {
          } else {
            Toast.show({
              type: 'error',
              text1: 'Failed to delete popst.',
              text2: 'Something went wrong.',
            });
          }
        },
        style: 'destructive',
      },
    ]);
  };

  const onLikeClick = () => {
    if (item.isLeaderboardCreated == undefined) {
      if (item.isLiked) {
        dispatch(
          removeLikeUnlikePost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: item.id,
          }),
        );
      } else {
        dispatch(
          likeUnlikePost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: item.id,
          }),
        );
      }
    } else {
      if (item.isLiked) {
        dispatch(
          removeLikeUnlikeChallengeFromPost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: item.id,
          }),
        );
      } else {
        dispatch(
          likeUnlikeChallengeFromPost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: item.id,
          }),
        );
      }
    }
  };
  const onLikeCountPress = () => {
    if (item.isLeaderboardCreated == undefined) {
      navigation.navigate('PostLikeUser', {postId: item.id});
    } else {
      navigation.navigate('ChallengeLikeUser', {challengeId: item.id});
    }
  };
  return (
    <ViewPortDetector
      onChange={setInViewPort}
      percentHeight={0.8}
      frequency={300}>
      <View style={styles.postContainer}>
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <Image
              source={
                userProfile?.imageReference &&
                userProfile?.imageReference !== 'imageReference'
                  ? {
                      uri: userProfile?.imageReference,
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }
                  : require('../../static/Images/user.png')
              }
              defaultSource={require('../../static/Images/user.png')}
              style={styles.profilePic}
            />
            <View>
              <Text
                style={
                  styles.userName
                }>{`${userProfile.firstName} ${userProfile.lastName}`}</Text>
            </View>
          </View>
          <Popover
            ref={popoverRef}
            from={
              <TouchableOpacity>
                <Image
                  source={require('../../assets/images/more.png')}
                  resizeMode="contain"
                  style={styles.moreIcon}
                />
              </TouchableOpacity>
            }
            popoverStyle={styles.popoverStyle}
            backgroundStyle={{opacity: 0}}>
            {isOwnPost ? (
              <>
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={onEditClick}>
                  <Text>Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={onDeleteClick}>
                  <Text>Delete</Text>
                </TouchableOpacity>
              </>
            ) : (
              <TouchableOpacity
                style={styles.optionItem}
                onPress={onReportClick}>
                <Text>Report</Text>
              </TouchableOpacity>
            )}
          </Popover>
        </View>
        <FlatList
          data={item.mediaRefs ? item.mediaRefs : []}
          horizontal
          showsHorizontalScrollIndicator={false}
          pagingEnabled
          onScroll={handleScroll}
          scrollEventThrottle={16}
          keyExtractor={(img, idx) => idx.toString()}
          renderItem={({item: imageSrc, index}) => (
            <View style={styles.imageContainer}>
              {isVideoLink(imageSrc) ? (
                <View style={styles.videoWrapper}>
                  <Video
                    source={{
                      uri: convertToProxyURL(imageSrc),
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }}
                    useTextureView={false}
                    repeat
                    paused={!(inViewPort && activeIndex === index && focus)}
                    style={styles.image}
                    muted={isMuted || appState != 'active'}
                    ignoreSilentSwitch="ignore"
                    bufferConfig={{
                      minBufferMs: 2500,
                      maxBufferMs: 50000,
                      bufferForPlaybackMs: 2500,
                      bufferForPlaybackAfterRebufferMs: 2500,
                    }}
                    resizeMode="contain"
                  />
                  <TouchableOpacity
                    onPress={toggleMute}
                    style={styles.muteButton}>
                    <Icon
                      name={isMuted ? 'volume-mute' : 'volume-high'} // Use icons for mute/unmute
                      size={20}
                      color="white"
                    />
                  </TouchableOpacity>
                </View>
              ) : (
                <FastImage
                  source={{
                    uri: imageSrc,
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }}
                  style={styles.image}
                  resizeMode="contain"
                />
              )}
            </View>
          )}
        />
        {item.mediaRefs && item.mediaRefs.length > 1 && (
          <Dots
            length={item.mediaRefs ? item.mediaRefs.length : 0}
            activeIndex={activeIndex}
          />
        )}
        {false && (
          <View style={styles.iconsContainer}>
            <View style={styles.leftIconsContainer}>
              <MaterialCommunityIcons
                name="heart-outline"
                size={28}
                color="#000"
              />
              <MaterialCommunityIcons
                name="share-outline"
                size={28}
                color="#000"
                style={styles.iconSpacing}
              />
            </View>
            <MaterialCommunityIcons
              name="bookmark-outline"
              size={28}
              color="#000"
            />
          </View>
        )}
        {false && (
          <View style={styles.statsContainer}>
            <Text style={styles.statsText}>{item.views || 0} Views</Text>
            <Text style={styles.statsText}>{item.likes || 0} Likes</Text>
          </View>
        )}
        <View style={styles.titleContainer}>
          <TouchableOpacity onPress={() => onChallnegePress(item)}>
            <View style={styles.titleView}>
              {item.isLeaderboardCreated != undefined && (
                <Image
                  style={{width: 15, height: 15, marginEnd: 5}}
                  resizeMode={'contain'}
                  source={require('../../assets/images/challenge.png')}
                />
              )}
              {item.title ? <Text style={styles.postTitle}>{item.title}</Text> :<Text style={styles.description2}>{item.text || item.description}</Text>}
            </View>
          </TouchableOpacity>
          <DebounceTouchable
            delay={5000}
            style={styles.likeButton}
            onPress={onLikeClick}>
            <Image
              style={styles.likeImage}
              source={
                item.isLiked
                  ? require('../../assets/images/like.png')
                  : require('../../assets/images/unlike.png')
              }
            />
            {isOwnPost && (
              <TouchableOpacity onPress={onLikeCountPress}>
                <Text style={styles.likeCount}>{item.likes}</Text>
              </TouchableOpacity>
            )}
          </DebounceTouchable>
        </View>
       {item.title && <Text style={styles.description}>{item.text || item.description}</Text> }
        {item.tags && <Text style={styles.tags}>{item.tags}</Text>}
        <Text style={styles.createDate}>
          {moment(item.updatedAt).format('MMMM DD, YYYY')}
        </Text>
      </View>
    </ViewPortDetector>
  );
};

const UserPostsScreen = ({route, navigation}: any) => {
  const {initialIndex, userProfile} = route.params;
  const userPost = useAppSelector(state => state.post.userPost.data);
  const nextPageTokenUser = useAppSelector(
    state => state.post.userPost.nextPageToken,
  );
  const hasNextUser = useAppSelector(state => state.post.userPost.hasNext);
  const userId = route.params?.userId;
  const [dataLoading, setDataLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const [appState, setAppState] = useState('active');
  const flatLisrRef = useRef<any>(null);

  useEffect(() => {
    if (flatLisrRef.current) {
      setTimeout(() => {
        flatLisrRef.current?.scrollToIndex({
          index: initialIndex,
          animated: false,
        });
      }, 400);
    }
  }, [initialIndex, flatLisrRef.current]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const onPostFeedEnd = useCallback(() => {
    if (userPost.length > 0 && !loading && !bottomLoad && hasNextUser) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getPostOfUser({
          userId: userId,
          nextPageToken: nextPageTokenUser,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, userPost, nextPageTokenUser, loading, hasNextUser]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onBackPress = () => {
    navigation.goBack();
  };

  return (
    <ViewPortDetectorProvider flex={1}>
      <View style={styles.container}>
        <View style={styles.headerView}>
          <TouchableOpacity
            style={acrossAllScreens.backImageContainer}
            onPress={onBackPress}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Posts</Text>
          <View style={{width: 20}} />
        </View>
        <FlashList
          data={userPost}
          ref={flatLisrRef}
          estimatedItemSize={535}
          keyExtractor={(item, index) => index.toString()}
          onEndReached={onPostFeedEnd}
          ListFooterComponent={renderFooter}
          extraData={appState}
          onEndReachedThreshold={0.1}
          renderItem={({item}) => (
            <UsertPostItem
              item={item}
              userProfile={userProfile}
              navigation={navigation}
              appState={appState}
            />
          )}
        />
      </View>
    </ViewPortDetectorProvider>
  );
};

const styles = StyleSheet.create({
  likeCount: {
    fontSize: 17,
    color: '#000000',
    fontWeight: '400',
    fontFamily: 'Helvetica Neue',
    marginLeft: 2,
  },
  likeImage: {
    width: 28,
    height: 28,
    top: 2,
  },
  likeButton: {
    marginTop: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginEnd: 10,
  },
  postTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    flex: 1, // This allows the title to take up the remaining space
    fontFamily: 'Helvetica Neue',
    alignItems: 'center',
    lineHeight: 18,
  },
  titleView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 10,
    paddingVertical: 3,
    width: Dimensions.get('window').width - 60,
  },
  videoWrapper: {
    position: 'relative',
  },
  muteButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 5,
  },
  muteIcon: {
    width: 20,
    height: 20,
    tintColor: 'white',
  },
  moreIcon: {
    width: 18,
    height: 18,
  },
  optionItem: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    minWidth:40,
  },
  popoverStyle: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
    paddingVertical: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  backImage: {
    width: 21,
    height: 21,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    paddingTop: 16,
    backgroundColor: 'white',
    paddingBottom: 10,
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  postContainer: {
    marginBottom: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profilePic: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
  },
  userName: {
    marginRight: 10,
  },
  location: {
    color: 'gray',
  },
  optionsIcon: {
    marginLeft: 10,
  },
  imageContainer: {
    width: screenWidth,
    alignItems: 'center',
  },
  image: {
    width: screenWidth,
    height: screenWidth,
    resizeMode: 'cover',
    backgroundColor: '#000000',
  },
  iconsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
  },
  leftIconsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconSpacing: {
    marginLeft: 10,
    marginRight: 10,
  },
  description: {
    paddingHorizontal: 10,
    paddingVertical: 3,
  }, description2: {
    paddingHorizontal: 0,
    paddingVertical: 3,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
  activeDot: {
    backgroundColor: 'black',
  },
  inactiveDot: {
    backgroundColor: 'gray',
  },
  tags: {
    padding: 10,
    color: 'blue',
  },
  createDate: {
    paddingHorizontal: 10,
    color: 'gray',
    fontSize: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 4,
    justifyContent: 'flex-start',
  },
  statsText: {
    marginRight: 15,
    color: 'gray',
  },
});

export default UserPostsScreen;
