import React, { ReactChild } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TouchableHighlight, ScrollView } from 'react-native-gesture-handler';

import { getHomeFeed } from '../../apis/mock';
import { UserProfileScreenStyle } from '../../styles/UserProfileStyles';
import { CommonFontstyles } from '../../styles/CommonFontStyles';
import { UserDataSection } from './UserDataSection';
import { ProfileTabNav } from './ProfileTabNav';

const profileTab = createBottomTabNavigator();

export function UserProfileScreen({navigation}:any) {

    return (
        <ScrollView>
            <View style={UserProfileScreenStyle.container}>
                <Text style={[CommonFontstyles.Napoz]}>Napoz</Text> 
            </View>
            <ProfileTabNav />
        </ScrollView>
    )
}

export default UserProfileScreen;

const styles = StyleSheet.create({

})