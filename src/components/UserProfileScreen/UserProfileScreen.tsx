import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {TouchableHighlight} from 'react-native-gesture-handler';
import {useDispatch} from 'react-redux';
import {UserProfileScreenStyle} from '../../styles/UserProfileStyles';
import {UserDataSection} from './UserDataSection';
import {
  followUser,
  getUserDetails,
  unFollowUser,
} from '../../redux/User/UserAction';
import {Tabs} from 'react-native-collapsible-tab-view';
import {useFocusEffect} from '@react-navigation/native';
import {useAppSelector} from '../../redux/Store';
import {getChallengeCreatedByUser} from '../../redux/Challenge/ChallengeAction';
import FastImage from 'react-native-fast-image';
import Video from 'react-native-video';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import {getPostOfUser} from '../../redux/Post/PostAction';
import Toast from 'react-native-toast-message';

let bottomLoad = false;
export function UserProfileScreen({navigation, route}: any) {
  const dispatch = useDispatch();
  const [name, setName] = useState('');
  const authState = useAppSelector(state => state.auth);
  const {token} = authState;
  const [userId, setUserId] = useState(
    route.params?.userId ? route.params?.userId : authState.userId,
  );

  const userProfileState = useAppSelector(state => state.user);
  const {userProfile, isGetUserSuccess}: any = userProfileState;
  const [dataLoading, setDataLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const challengeCreatedByUser = useAppSelector(
    state => state.challenge.challengeCreatedByUser.data,
  );
  const nextPageToken = useAppSelector(
    state => state.challenge.challengeCreatedByUser.nextPageToken,
  );
  const hasNext = useAppSelector(
    state => state.challenge.challengeCreatedByUser.hasNext,
  );
  const userPost = useAppSelector(state => state.post.userPost.data);
  const nextPageTokenUser = useAppSelector(
    state => state.post.userPost.nextPageToken,
  );
  const hasNextUser = useAppSelector(state => state.post.userPost.hasNext);
  const isOwnProfile = useMemo(() => {
    return userId === authState.userId;
  }, [authState, userId]);

  useEffect(() => {
    if (route.params?.userId) {
      setUserId(route.params?.userId);
    }
  }, [route.params?.userId]);

  useEffect(() => {
    if (userId) {
      setDataLoading(true);
      dispatch(getChallengeCreatedByUser({userId})).finally(() => {
        setDataLoading(false);
      });
      dispatch(getPostOfUser({userId}));
    }
  }, [userId]);

  const onPostFeedEnd = useCallback(() => {
    if (userPost.length > 0 && !loading && !bottomLoad && hasNextUser) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getPostOfUser({
          userId: userId,
          nextPageToken: nextPageTokenUser,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, userPost, nextPageTokenUser, loading, hasNextUser]);

  const onChallengeFeedEnd = useCallback(() => {
    if (
      challengeCreatedByUser.length > 0 &&
      !loading &&
      !bottomLoad &&
      hasNext
    ) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        getChallengeCreatedByUser({
          userId: userId,
          nextPageToken: nextPageToken,
        }),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, challengeCreatedByUser, nextPageToken, loading, hasNext]);

  useFocusEffect(
    React.useCallback(() => {
      dispatch(getUserDetails(userId));
      setDataLoading(true);
      dispatch(getPostOfUser({userId}));
      dispatch(getChallengeCreatedByUser({userId})).finally(() => {
        setDataLoading(false);
      });
    }, [userId, dispatch]),
  );
  useEffect(() => {
    setName(userProfile.firstName + ' ' + userProfile.lastName);
  }, [userProfile]);

  const onFollowClick = async () => {
    const result = await dispatch(
      followUser({
        followerId: authState.userId,
        followedId: userId,
      }),
    ).unwrap();
    if (result.data) {
      dispatch(getUserDetails(userId));
      Toast.show({
        type: 'success',
        text1: 'Follow success',
        text2: 'User Followed Successfully!',
      });
    } else {
      Toast.show({
        type: 'error',
        text1: 'Follow failed',
        text2: 'Something went wrong.',
      });
    }
  };

  const onUnFollowClick = async () => {
    const result = await dispatch(
      unFollowUser({
        followerId: authState.userId,
        followedId: userId,
      }),
    ).unwrap();
    if (result.data) {
      dispatch(getUserDetails(userId));
      Toast.show({
        type: 'success',
        text1: 'Unfollow success',
        text2: 'User Unfollowed Successfully!',
      });
    } else {
      Toast.show({
        type: 'error',
        text1: 'Unfollow failed',
        text2: 'Something went wrong.',
      });
    }
  };

  const UserProfileHeader = () => {
    return (
      <View>
        {/* <UserDataSection /> */}
        <UserDataSection
          name={name}
          navigation={navigation}
          bio={userProfile.bio}
          isOwnProfile={isOwnProfile}
          userId={userId}
          onFollowClick={onFollowClick}
          userProfile={userProfile}
          isFollowing={userProfile.isFollowed}
          onUnFollowClick={onUnFollowClick}
          firstName={userProfile.firstName}
          lastName={userProfile.lastName}
          imageReference={userProfile.imageReference}
        />
      </View>
    );
  };

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  if (dataLoading) {
    return (
      <View style={styles.loaderContain}>
        <ActivityIndicator size={'large'} />
      </View>
    );
  }

  return (
    <Tabs.Container renderHeader={UserProfileHeader}>
      <Tabs.Tab name="Posts">
        <Tabs.FlatList
          data={userPost}
          numColumns={3}
          onEndReached={onPostFeedEnd}
          ListFooterComponent={renderFooter}
          onEndReachedThreshold={0.1}
          renderItem={({item, index}: any) => {
            if (!item.mediaRefs || item.mediaRefs.length == 0) {
              return (
                <View
                  key={index}
                  style={UserProfileScreenStyle.imageContainer}
                />
              );
            }
            return (
              <TouchableHighlight
                onPress={() =>
                  navigation.navigate('UserPostsScreen', {
                    initialIndex: index,
                    userId: userId,
                    userProfile: userProfile,
                  })
                }>
                <View key={index} style={UserProfileScreenStyle.imageContainer}>
                  {isVideoLink(item.mediaRefs[0]) ? (
                    <Video
                      source={{
                        uri: item.mediaRefs[0],
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }}
                      useTextureView={false} 
                      paused={true}
                      resizeMode="contain"
                      style={UserProfileScreenStyle.image}
                      controls={false}
                    />
                  ) : (
                    <FastImage
                      source={{
                        uri: item.mediaRefs[0],
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }}
                      style={UserProfileScreenStyle.image}
                      resizeMode="contain"
                    />
                  )}
                </View>
              </TouchableHighlight>
            );
          }}
        />
      </Tabs.Tab>
      <Tabs.Tab name="Challenges">
        <Tabs.FlatList
          data={challengeCreatedByUser}
          numColumns={3}
          onEndReached={onChallengeFeedEnd}
          ListFooterComponent={renderFooter}
          onEndReachedThreshold={0.1}
          renderItem={({item, index}: any) => {
            if (!item.mediaRefs || item.mediaRefs.length == 0) {
              return (
                <View
                  key={index}
                  style={UserProfileScreenStyle.imageContainer}
                />
              );
            }
            return (
              <TouchableHighlight
                onPress={() => {
                  navigation.navigate('ChallengeDetails', {
                    challengeId: item.challengeId,
                  });
                }}>
                <View key={index} style={UserProfileScreenStyle.imageContainer}>
                  {isVideoLink(item.mediaRefs[0]) ? (
                    <Video
                      source={{
                        uri: item.mediaRefs[0],
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }}
                      useTextureView={false} 
                      paused={true}
                      resizeMode="contain"
                      style={UserProfileScreenStyle.image}
                      controls={false}
                    />
                  ) : (
                    <FastImage
                      source={{
                        uri: item.mediaRefs[0],
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }}
                      style={UserProfileScreenStyle.image}
                      resizeMode="contain"
                    />
                  )}
                </View>
              </TouchableHighlight>
            );
          }}
        />
      </Tabs.Tab>
      {/* <Tabs.Tab name="Stats"></Tabs.Tab> */}
    </Tabs.Container>
  );
}

export default UserProfileScreen;

const styles = StyleSheet.create({
  loaderContain: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

const images = [
  require('../../static/Images/beach.jpg'),
  require('../../static/Images/mountain.jpg'),
  require('../../static/Images/forest.jpg'),
  require('../../static/Images/lake.jpeg'),
  require('../../static/Images/spaceneedle.jpg'),
  require('../../static/Images/vader.png'),
  require('../../static/Images/canyon.jpg'),
  require('../../static/Images/desert.jpeg'),
  require('../../static/Images/waterfall.jpg'),
  require('../../static/Images/jellyfish.jpg'),
  require('../../static/Images/wolf.png'),
  require('../../static/Images/merc.jpg'),
];

//This is currently generating dummy data. This should fetch the data from Posts table
const generateUserPostsData = () => {
  return images.map(image => ({
    text: 'This is the description of the post',
    location: 'Seattle',
    views: 123,
    likes: 91,
    tags: '#test #test1 #test2 #test3',
    mediaRefs: new Array(5).fill(image),
    createDate: 'September 30, 2023',
  }));
};
