import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import UserPostsScreen from './UserPostsScreen';
import UserProfileScreen from './UserProfileScreen';
import {Button, TouchableOpacity} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import UserConnectionsScreen from './UserConnectionsScreen';
import UserDataSection from './UserDataSection';
import EditProfileScreen from './EditProfileScreen';
import ChangePasswordScreen from './changePassword';
import UserSettingsScreen from './UserSettings';
import ChallengeDetailsScreen from '../ChallengeDetailsScreen/ChallengeDetailsScreen';
import ChallengeParticipant from '../ChallengeDetailsScreen/ChallengeParticipant';
import LeaderboardDetailsScreen from '../LeaderboardDetailsScreen/LeaderboardDetailsScreen';
import AccountSettingsScreen from './AccountSettings';
import TutorialScreen1 from '../welcomeScreens/TutorialScreen1';
import TutorialScreen2 from '../welcomeScreens/TutorialScreen2';
import TutorialScreen3 from '../welcomeScreens/TutorialScreen3';
import InterestScreen from '../Interest/InterestScreen';
import PostLikeUser from '../Posts/PostLikeUser';
import ChallengeLikeUser from '../ChallengeHomeScreen/ChallengeLikeUser';
import BaseUnits from './BaseUnits';

const userProfileStack = createStackNavigator();

export function UserProfileScreenStack() {
  return (
    <userProfileStack.Navigator
      initialRouteName="UserProfileScreen"
      screenOptions={{
        gestureEnabled: false,
        // headerShown: false,
      }}>
      <userProfileStack.Screen
        name="UserProfileScreen"
        component={UserProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <userProfileStack.Screen
        name="UserPostsScreen"
        component={UserPostsScreen}
        options={() => ({
          title: 'Posts',
          header: () => null,
        })}
      />
      <userProfileStack.Screen
        name="PostLikeUser"
        component={PostLikeUser}
        options={({route}) => ({title: 'PostLikeUser', header: () => null})}
      />
      <userProfileStack.Screen
        name="ChallengeLikeUser"
        component={ChallengeLikeUser}
        options={({route}) => ({
          title: 'ChallengeLikeUser',
          header: () => null,
        })}
      />
      <userProfileStack.Screen
        name="UserDataSection"
        component={UserDataSection}
      />
      <userProfileStack.Screen
        name="UserConnectionsScreen"
        component={UserConnectionsScreen}
        options={({navigation}) => ({
          title: 'Connections',
          header: () => null,
        })}
      />

      <userProfileStack.Screen
        name="EditProfileScreen"
        component={EditProfileScreen}
        options={({navigation}) => ({
          title: 'Edit profile',
          headerShown: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <MaterialCommunityIcons
                name="arrow-left"
                size={28}
                color="#000"
              />
            </TouchableOpacity>
          ),
          headerRight: () => <Button title="Save" />,
        })}
      />
      <userProfileStack.Screen
        name="ChangePasswordScreen"
        component={ChangePasswordScreen}
        options={({navigation}) => ({
          title: 'Change Password',

          headerShown: false,

          headerLeft: () => (
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <MaterialCommunityIcons
                name="arrow-left"
                size={28}
                color="#000"
              />
            </TouchableOpacity>
          ),
          headerRight: () => <Button title="Save" />,
        })}
      />
      <userProfileStack.Screen
        name="UserSettingsScreen"
        component={UserSettingsScreen}
        options={({navigation}) => ({
          title: 'User Settings',
          headerShown: false,
        })}
      />
      <userProfileStack.Screen
        name="AccountSettingsScreen"
        component={AccountSettingsScreen}
        options={({navigation}) => ({
          title: 'Account Settings',
          headerShown: false,
        })}
      />
      <userProfileStack.Screen
        name="TutorialScreen1"
        component={TutorialScreen1}
        options={({navigation}) => ({
          title: 'Tutorial Screen 1',
          headerShown: false,
        })}
      />
      <userProfileStack.Screen
        name="TutorialScreen2"
        component={TutorialScreen2}
        options={({navigation}) => ({
          title: 'Tutorial Screen 2',
          headerShown: false,
        })}
      />
      <userProfileStack.Screen
        name="TutorialScreen3"
        component={TutorialScreen3}
        options={({navigation}) => ({
          title: 'Tutorial Screen 3',
          headerShown: false,
        })}
      />
      <userProfileStack.Screen
        name="ChallengeDetails"
        component={ChallengeDetailsScreen}
        options={({route}) => ({title: 'Challenge', header: () => null})}
      />
      <userProfileStack.Screen
        name="ChallengeParticipant"
        component={ChallengeParticipant}
        options={({route}: any) => ({
          title: route.params.name,
          header: () => null,
        })}
      />
      <userProfileStack.Screen
        name="LeaderboardDetailsScreen"
        component={LeaderboardDetailsScreen}
        options={({route}: any) => ({
          title: route.params?.name,
          headerShown: false,
        })}
      />
      <userProfileStack.Screen
        name="InterestScreen"
        component={InterestScreen}
        options={({navigation}) => ({
          title: 'Interest',
          headerShown: false,
        })}
      />
       <userProfileStack.Screen
        name="BaseUnitsScreen"
        component={BaseUnits}
        options={({navigation}) => ({
          title: 'Base Units',
          headerShown: false,
        })}
      />
    </userProfileStack.Navigator>
  );
}

export default UserProfileScreenStack;
