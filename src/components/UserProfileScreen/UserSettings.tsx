import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Button,
  TouchableHighlight,
  Alert,
  Linking,
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import SignUpStyle from '../../styles/SignUp';
import {useDispatch, useSelector} from 'react-redux';
import {deleteUser, logout} from '../../redux/User/UserAction';
import {resetDeleteSuccess} from '../../redux/User/UserSlice';

export function UserSettingsScreen({route, navigation}: any) {
  let userData: any;
  let title: string;
  let message: string;
  const userState = useSelector(state => state.user);
  const {isDeleteSuccess} = userState;
  const authState = useSelector(state => state.auth);
  const {userId} = authState;
  const [arrow, setArrow] = useState(true);
  const toggleArrow = () => {
    console.log('Arrow button press');
    setArrow(prevState => !prevState);
  };
  const dispatch = useDispatch();
  const logoutHandler = async () => {
    try {
      await dispatch(logout()).unwrap(); // Ensures the logout action completes
      navigation.reset({
        index: 0,
        routes: [{name: 'WelcomeStack', params: {screen: 'LoginScreen'}}],
      });
      console.log('User successfully logged out');
    } catch (error) {
      console.log('Error during logout:', error);
    }
  };
  // const deleteHandler = async () => {
  //   try {
  //     await dispatch(deleteUser(userId)).unwrap(); // Ensures the logout action completes

  //     console.log('User deleted and successfully logged out');
  //     navigation.reset({
  //       index: 0,
  //       routes: [{name: 'WelcomeStack', params: {screen: 'LoginScreen'}}],
  //     });
  //   } catch (error) {
  //     console.log('Error during delete and logout:', error);
  //   }
  // };

  const confirmAlert = (title: string, message: string) => {
    Alert.alert(title, message, [
      {
        text: 'Cancel',
        onPress: () => console.log(`${title} canceled`),
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: () => {
          console.log(`${title}`);
          if (title == 'Logout') {
            logoutHandler();
          }
        },
      },
    ]);
  };

  const handleEmailPress = () => {
    const email = '<EMAIL>';
    const subject = '[Napoz]Support request from "{Csutomer name}"'; // Optional
    const body = ''; // Optional
    const mailto = `mailto:${email}?subject=${encodeURIComponent(
      subject,
    )}&body=${encodeURIComponent(body)}`;

    Linking.openURL(mailto).catch(err => {
      console.error('Error:', err);
      Alert.alert('Error', 'Unable to open mail app');
    });
  };
  return (
    <View style={styles.container}>
      <View style={[styles.HeaderStyle]}>
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>Settings</Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
          title = 'Logout';
          message = 'Are you sure you want to Logout of this account?';
          confirmAlert(title, message);
        }}>
        <Text style={acrossAllScreens.H2}>Logout</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
          navigation.push('AccountSettingsScreen');
        }}>
        <Text style={acrossAllScreens.H2}>Account</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
          navigation.push('TutorialScreen1');
        }}>
        <Text style={acrossAllScreens.H2}>App Tutorials</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <TouchableOpacity style={styles.Button} onPress={toggleArrow}>
        <Text style={acrossAllScreens.H2}>About</Text>
        <AntDesign name={arrow ? 'right' : 'down'} size={20} color="black" />
      </TouchableOpacity>
      {arrow ? null : (
        <View>
          <Text style={acrossAllScreens.H2}>
            Napoz is a challenge based social app! contact us at :
          </Text>
          <TouchableOpacity onPress={handleEmailPress}>
            <Text style={acrossAllScreens.H3}><EMAIL></Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 25,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editProfileText: {
    marginTop: 10,
    // color: 'black',
    // fontWeight:
  },
  inputField: {
    borderWidth: 1,
    borderColor: 'black',
    padding: 10,
    marginVertical: 10,
    borderRadius: 8,
    color: 'black',
  },
  inputLine: {
    borderBottomWidth: 0.2,
    borderColor: 'black',
    paddingBottom: 0.5,
    paddingTop: 2,
    marginBottom: 10,
    color: 'black',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },

  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ButtonPos: {
    marginTop: 70,
    alignItems: 'center',
  },
  Button: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ButtonGap: {marginBottom: 40},
});

export default UserSettingsScreen;
