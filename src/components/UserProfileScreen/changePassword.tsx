import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback,
  Image,Platform
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import SignUpStyle from '../../styles/SignUp';
import {useDispatch} from 'react-redux';
import {updatePassword} from '../../redux/Auth/AuthAction';
import Toast from 'react-native-simple-toast';

export function ChangePasswordScreen({route, navigation}: any) {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [cNewPassword, setCNewPassword] = useState('');
  const [oldPasswordError, setOldPasswordError] = useState('');
  const [cNewPasswordError, setCNewPasswordError] = useState('');
  const [newPasswordError, setNewPasswordError] = useState('');
  let passwordBody: any;

  const dispatch = useDispatch();

  // Validation helper functions
  const isFieldEmpty = field => field.trim() === '';

  // Validation functions for each field
  const validateOldPassword = () => {
    if (isFieldEmpty(oldPassword)) {
      setOldPasswordError('*Old Password is required.');
      return false;
    }
    setOldPasswordError('');
    return true;
  };
  const validateNewPassword = () => {
    if (isFieldEmpty(newPassword)) {
      setNewPasswordError('*New Password is required.');
      return false;
    }
    setNewPasswordError('');
    return true;
  };
  const validateCNewPassword = () => {
    if (newPassword !== cNewPassword) {
      setCNewPasswordError('*Passwords do not match.');
      return false;
    }
    setCNewPasswordError('');
    return true;
  };
  const validateAllFields = () => {
    const isPasswordValid = validateOldPassword();
    const isNewPasswordValid = validateNewPassword();
    const isCNewPasswordValid = validateCNewPassword();

    return isPasswordValid && isCNewPasswordValid && isNewPasswordValid;
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={acrossAllScreens.ScreenBackground}>
        <View style={acrossAllScreens.ScreenBorders}>
          <View style={styles.HeaderStyle}>
            <TouchableOpacity
              style={[
                acrossAllScreens.backImageContainer,
                styles.HeaderbackButton,
              ]}
              onPress={() => navigation.goBack()}>
              <Image
                style={acrossAllScreens.backImage}
                source={require('../../assets/images/back.png')}
              />
            </TouchableOpacity>
            <View style={styles.HeaderHorizontalPosition}>
              <Text style={acrossAllScreens.ScreenHeaderText}>
                Change Password
              </Text>
            </View>
          </View>
          <View>
            <Text style={acrossAllScreens.H2}>Old Password</Text>
            <TextInput
              value={oldPassword}
              style={[styles.inputLine, acrossAllScreens.InputTextRegular]}
              secureTextEntry={true}
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
              onChangeText={setOldPassword}
            />
            {oldPasswordError ? (
              <Text style={styles.errorText}>{oldPasswordError}</Text>
            ) : null}
          </View>

          <View>
            <Text style={acrossAllScreens.H2}>New Password</Text>
            <TextInput
              value={newPassword}
              style={[styles.inputLine, acrossAllScreens.InputTextRegular]}
              onChangeText={setNewPassword}
              secureTextEntry={true}
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
            />
            {newPasswordError ? (
              <Text style={styles.errorText}>{newPasswordError}</Text>
            ) : null}
          </View>
          <View>
            <Text style={acrossAllScreens.H2}>Confirm Password</Text>
            <TextInput
              value={cNewPassword}
              style={[styles.inputLine, acrossAllScreens.InputTextRegular]}
              onChangeText={setCNewPassword}
              secureTextEntry={true}
              placeholderTextColor="grey"
              underlineColorAndroid="transparent"
            />
            {cNewPasswordError ? (
              <Text style={styles.errorText}>{cNewPasswordError}</Text>
            ) : null}
          </View>

          <View style={styles.bottomButtonContainer}>
            <TouchableOpacity
              style={SignUpStyle.signupButton}
              onPress={() => {
                console.log('save changes');
                if (validateAllFields()) {
                  passwordBody = {
                    oldPassword: oldPassword,
                    newPassword: newPassword,
                  };
                  console.log(passwordBody);
                  const changePassword = async () => {
                    try {
                      const resultAction = await dispatch(
                        updatePassword(passwordBody),
                      );
                      if (updatePassword.fulfilled.match(resultAction)) {
                        Toast.show('Password is updated!', Toast.LONG);
                        navigation.reset({
                          index: 0,
                          routes: [
                            {
                              name: 'BottomTabsNavigator',
                              params: {screen: 'Profile'},
                            },
                          ],
                        });
                      } else {
                        // Thunk was rejected
                        Toast.show(
                          'Error resetting password!Try Again',
                          Toast.LONG,
                        );
                        console.error(
                          'Update password failed:',
                          resultAction.payload,
                        );
                      }
                    } catch (error) {
                      Toast.show('Unexpected error occurred!', Toast.LONG);
                      console.error('Unexpected error:', error);
                    }
                  };

                  changePassword();
                } else console.log('Missing Fields for Password reset');
              }}>
              <Text style={SignUpStyle.signUpText}> Change Password</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  inputLine: {
    borderBottomWidth: 0.2,
    borderColor: 'black',
    paddingBottom: 0.5,
    // paddingTop: 2,
    marginBottom: 16,
    color: 'black',
    paddingTop: Platform.OS === 'android' ? 8 : 12,
  },
  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ButtonPos: {
    marginTop: 70,
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4, // Add space above error text
    marginBottom: 10, // Ensure spacing between error text and next input field
  },  bottomButtonContainer: {
    position: 'absolute',
    bottom: 32,
    width: '100%',
    alignItems: 'center',
  },
});

export default ChangePasswordScreen;
