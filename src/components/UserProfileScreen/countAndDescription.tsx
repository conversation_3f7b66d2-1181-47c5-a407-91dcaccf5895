import React, { ReactElement } from 'react';
import { View, Text } from 'react-native';

import UserProfileScreenStyle from '../../styles/UserProfileStyles';
import { TouchableOpacity } from 'react-native-gesture-handler';

type CountAndDescriptionProps = {
    count: number, 
    description: string
    onPress?: () => void;
};

export function CountAndDescription(props: CountAndDescriptionProps){
    return (
            <TouchableOpacity onPress={props.onPress}>
                <View style={UserProfileScreenStyle.countContainer}>
                    <Text style={UserProfileScreenStyle.count}>{props.count}</Text>
                    <Text style={UserProfileScreenStyle.countDescription}>{props.description}</Text>
                </View>   
            </TouchableOpacity>
    )
}

export default CountAndDescription;