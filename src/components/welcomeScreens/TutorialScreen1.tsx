import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  Switch,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {SignUpStyle} from '../../styles/SignUp';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';

export function TutorialScreen1() {
  const navigation = useNavigation();
  // Get Navigation State
  const state = navigation.getState();

  // Get the previous screen name
  const previousRoute = state?.routes[state.index - 1]?.name;

  return (
    <View style={[acrossAllScreens.ScreenBackground]}>
      <View style={[acrossAllScreens.ScreenBorders, styles.container]}>
        {previousRoute === 'UserSettingsScreen' ? (
          <View style={styles.headerRow}>
            <TouchableOpacity
              style={[acrossAllScreens.backImageContainer, styles.backButton]}
              onPress={() => navigation.goBack()}>
              <Image
                style={acrossAllScreens.backImage}
                source={require('../../assets/images/back.png')}
              />
            </TouchableOpacity>
            <Text style={[acrossAllScreens.ScreenHeaderText, styles.header]}>
              It’s easy to create on
            </Text>
          </View>
        ) : (
          <Text style={[acrossAllScreens.ScreenHeaderText, styles.header]}>
            It’s easy to create on
          </Text>
        )}
        <Text style={[acrossAllScreens.ScreenHeaderText, styles.subHeader]}>
          Napoz
        </Text>

        {/* Progress Bar with Partial Fill */}
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, {width: '33.33%'}]} />
        </View>

        {/* Image Section */}
        <View style={styles.imageRow}>
          <Image
            style={styles.imageStyle}
            source={require('../../static/Images/TutorialScreen1Img1.jpg')}
          />
          <Image
            style={styles.imageStyle}
            source={require('../../static/Images/TutorialScreen1Img2.png')}
          />
          {/* <View style={styles.switchContainer}>
        <Text style={styles.switchText}>Link Challenge</Text>
        <Switch
          trackColor={{ false: '#D3D3D3', true: '#B3E5FC' }}
          thumbColor={isEnabled ? '#81D4FA' : '#f4f3f4'}
          ios_backgroundColor="#D3D3D3"
          onValueChange={() => setIsEnabled((prev) => !prev)}
          value={isEnabled}
        />
      </View>  */}
        </View>

        {/* Title & Description */}
        <Text style={styles.title}>Make a post</Text>
        <Text style={styles.description}>
          The floating button lets you create anything on this app! Upload media
          to create a post, and you can choose to link it to a challenge that
          you’re a part of.
        </Text>

        {/* Next Button */}
        <View style={styles.bottomButtonContainer}>
          <TouchableOpacity
            style={[SignUpStyle.signupButton]}
            onPress={() => navigation.navigate('TutorialScreen2')}>
            <Text style={SignUpStyle.signUpText}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

// Styles
const styles = StyleSheet.create({
  container: {
    // flex: 1,
    // backgroundColor: '#FFFFFF',
    alignItems: 'center',
    // paddingHorizontal: 20,
    // paddingTop: 40,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // Ensures text stays centered
    width: '100%',

    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subHeader: {
    textAlign: 'center',
    marginBottom: 10,
    fontSize: 22,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    width: '80%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 10,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#B3E5FC',
  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 10,
    paddingTop: 10,
    width: '100%',
  },
  imageStyle: {
    width: 180,
    height: 200,
    resizeMode: 'contain',
    // marginHorizontal: 5,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  switchText: {
    fontSize: 16,
    marginRight: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 10,
    textAlign: 'center',
    // paddingTop: 20,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 15,
    marginVertical: 10,
    paddingBottom: 20,
    // lineHeight: 26,
    includeFontPadding: false, // Fixes text height issues on Android
    allowFontScaling: false, // Disables system font scaling inconsistencies
  },
  nextButton: {
    backgroundColor: '#B3E5FC',
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 8,
    marginTop: 20,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
  },
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 32,
    width: '100%',
    alignItems: 'center',
  },
});

export default TutorialScreen1;
