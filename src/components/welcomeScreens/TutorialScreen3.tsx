import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {SignUpStyle} from '../../styles/SignUp';
import acrossAllScreens from '../../styles/acrossAllScreens';

export function TutorialScreen3() {
  const navigation = useNavigation();

  return (
    <View style={[acrossAllScreens.ScreenBackground]}>
      <View style={[acrossAllScreens.ScreenBorders, styles.container]}>
        {/* Header Row: Back Button + Title */}
        <View style={styles.headerRow}>
          <TouchableOpacity
            style={[acrossAllScreens.backImageContainer, styles.backButton]}
            onPress={() => navigation.goBack()}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <Text style={[acrossAllScreens.ScreenHeaderText, styles.header]}>
            It’s easy to create on
          </Text>
        </View>

        <Text style={[acrossAllScreens.ScreenHeaderText, styles.subHeader]}>
          Napoz
        </Text>

        {/* Progress Bar (60%) */}
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, {width: '100%'}]} />
        </View>

        {/* Image Section */}
        <View style={styles.imageRow}>
          <Image
            style={styles.imageStyle}
            source={require('../../static/Images/TutorialScreen3Img1.jpg')}
          />
          <Image
            style={styles.imageStyle}
            source={require('../../static/Images/TutorialScreen3Img2.png')}
          />
        </View>

        {/* Title & Description */}
        <Text style={styles.title}>
          Add a Leaderboard and specify its rules
        </Text>
        <Text style={styles.description}>
          Automated mode awards a single point for each post uploaded to the
          challenge and Manual mode asks the challenge participant to specify
          the points themselves.
        </Text>
        <Text style={styles.description2}>
          Choose an exciting title for the points column and set some fun
          scoring rules.
        </Text>

        {/* Next Button */}
        <View style={styles.bottomButtonContainer}>
          <TouchableOpacity
            style={[SignUpStyle.signupButton]}
            onPress={() => {
              console.log('Home Screen');

              navigation.reset({
                index: 0,
                routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
              });
            }}>
            <Text style={SignUpStyle.signUpText}>Start Using the App</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

// Styles
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // Ensures text stays centered
    width: '100%',

    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subHeader: {
    textAlign: 'center',
    marginBottom: 10,
    fontSize: 22,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    width: '80%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 10,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#B3E5FC',
  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 10,
    paddingTop: 10,
    width: '100%',
  },
  imageStyle: {
    width: 180,
    height: 165,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 10,
    textAlign: 'center',
    // paddingTop: 10,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 15,
    marginVertical: 10,
    // lineHeight: 26,
    includeFontPadding: false, // Fixes text height issues on Android
    allowFontScaling: false, // Disables system font scaling inconsistencies
  },
  description2: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 15,
    paddingBottom: 20,
  },  bottomButtonContainer: {
    position: 'absolute',
    bottom: 32,
    width: '100%',
    alignItems: 'center',
  },
});

export default TutorialScreen3;

