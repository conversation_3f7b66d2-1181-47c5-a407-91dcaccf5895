import {createAsyncThunk} from '@reduxjs/toolkit';
import {AuthService} from './AuthService';

export const register: any = createAsyncThunk(
  'auth/signup',
  async (userData, thunkAPI) => {
    try {
      console.log('Register action service was run', 'userdata:', userData);
      return AuthService.register(userData);
    } catch (error) {
      console.log('Register action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const login: any = createAsyncThunk(
  'auth/signin',

  async (userData, thunkAPI) => {
    try {
      console.log('Login action service was run', 'userdata:', userData);
      const response = await AuthService.login(userData);
      console.log('authaction response', response);
      // return AuthService.login(userData);
      return response;
    } catch (error) {
      console.log('Login action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const refreshToken: any = createAsyncThunk(
  'auth/refreshToken',

  async (userData, thunkAPI) => {
    try {
      console.log('Refresh action service was run', 'userdata:', userData);
      const response = await AuthService.refreshToken(userData);
      console.log(' Refreshauthaction response', response);
      // return AuthService.login(userData);
      return response;
    } catch (error) {
      console.log('Refresh action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const verifyEmail: any = createAsyncThunk(
  'auth/verifyEmail',

  async (email, thunkAPI) => {
    try {
      console.log('verify email action service was run', 'email:', email);
      const response = await AuthService.verifyEmail(email);
      console.log('verify email authaction response', response);
      // return AuthService.login(userData);
      return response;
    } catch (error) {
      console.log('verify email action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const forgotPassword: any = createAsyncThunk(
  'auth/forgotPassword',

  async ({resetToken,password}:{resetToken:string,password:string}, thunkAPI) => {
    try {
      console.log('Forgot Password action service was run', 'resetToken:', resetToken,'password:',password);
      const response = await AuthService.forgotPassword(resetToken,password);
      console.log('Forgot Password action response', response);
      // return AuthService.login(userData);
      return response;
    } catch (error) {
      console.log('Forgot Password action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const updatePassword: any = createAsyncThunk(
  'auth/updatePassword',

  async (passwords, thunkAPI) => {
    try {
      console.log('Update Password action service was run', 'passwords:',passwords);
      const response = await AuthService.updatePassword(passwords);
      console.log('Update Password action response', response);
      
      return response;
    } catch (error) {
      console.log('Update Password action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);