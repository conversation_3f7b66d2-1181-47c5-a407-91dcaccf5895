import axios from 'axios';
import {baseUrl, config} from '../../utils/Utils';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {getUserFromLocalStorage} from '../../utils/Utils';

const register = async user => {
  try {
    // const response = await axios.post(`${baseUrl}/api/users`, user);
    const response = await axios.post(`${baseUrl}/api/v1/auth/user`, user);
    console.log(
      'Register service was run',
      'user:',
      user,
      'response:',
      response.data,
    );
    // await AsyncStorage.setItem('token', response.data.body.data.token);
    // await AsyncStorage.setItem('user', response.data.body.data.userId);
    return response.data;
  } catch (error) {
    console.log(error);
    console.log('Register service error');
    throw error;
  }
};

const login = async user => {
  try {
    // const response = await axios.post(`${baseUrl}/api/users/auth`, user);
    const response = await axios.post(`${baseUrl}/api/v1/auth/token`, user);
    console.log(
      'Login service was run',
      'user:',
      user,
      'response:',
      response,
      'response data:',
      response.data,
      'response data data token',
      response.data.body.data.token,
      'response data data id',
      response.data.body.data.userId,
    );
    if (response.data) {
      try {
        //await AsyncStorage.setItem('token', JSON.stringify(response.data.token));
        await AsyncStorage.setItem('token', response.data.body.data.token);
        //await AsyncStorage.setItem('userId', response.data.id);
        await AsyncStorage.setItem('user', response.data.body.data.userId);
        const tokenValue = await AsyncStorage.getItem('token');
        const userIdValue = await AsyncStorage.getItem('user');
        const userIdValue2 = await getUserFromLocalStorage();
        console.log('tokenvalue in storage', tokenValue);
        console.log('userIDvalue in storage', userIdValue);
        console.log('userIDvalue in storage2', userIdValue2);
        console.log('ID and Token saved');
      } catch (error) {
        console.log('saving error');
        throw error;
      }
    }
    return response.data;
  } catch (error: any) {
    console.log('Login Service error message', error);
    console.log(error.response.data, 'error.response.data');
    console.log(error.response.status, 'error.response.data');
    console.log(error.response.headers, 'error.response.data');
    console.log('Login service error');
    throw error;
  }
};

const refreshToken = async (user: any) => {
  try {
    // const response = await axios.post(`${baseUrl}/api/users/auth`, user);
    const response = await axios.patch(`${baseUrl}/api/v1/auth/token`, user, {
      headers: {
        id: user.id,
        'Content-Type': 'application/json', // Example: Specify the content type
      },
    });
    if (response.data) {
      try {
        await AsyncStorage.setItem('token', response.data.body.data.token);
        await AsyncStorage.setItem('user', response.data.body.data.userId);
      } catch (error) {
        console.log('saving error');
        throw error;
      }
    }
    return response.data;
  } catch (error) {
    console.log('Login Service error message', error);
    console.log('Login service error');
    throw error;
  }
};

const verifyEmail = async (email: string) => {
  try {
    const response = await axios.put(
      `${baseUrl}/api/v1/auth/reset?email=${email}`,
    );
    console.log(
      'Password reset email verification service was run',
      'email:',
      email,
    );

    return response;
  } catch (error) {
    console.log(error);
    console.log('Password reset email service error');
    throw error;
  }
};

const forgotPassword = async (resetToken: string, password: string) => {
  try {
    const response = await axios.post(
      `${baseUrl}/api/v1/auth/reset/${resetToken}`,
      password,
    );
    console.log(
      'Reset password service was run',
      'reset token:',
      resetToken,
      'password:',
      password,
    );

    return response;
  } catch (error) {
    console.log(error);
    console.log('Reset password service error');
    throw error;
  }
};

const updatePassword = async passwords => {
  try {
    let authConfig = await config();

    const response = await axios.patch(
      `${baseUrl}/api/v1/auth/update-password`,
      passwords,
      authConfig,
    );
    console.log('Reset password service was run', 'passwords:', passwords);

    return response.data.body;
  } catch (error) {
    console.log(error);
    console.log('Update password service error');
    throw error;
  }
};

export const AuthService = {
  register,
  login,
  refreshToken,
  verifyEmail,
  forgotPassword,
  updatePassword,
};
