import {createSlice} from '@reduxjs/toolkit';
import {getUserFromLocalStorage} from '../../utils/Utils';
import {register, login, refreshToken,verifyEmail, forgotPassword,updatePassword} from './AuthAction';
// import {logout} from '../Logout/LogoutAction';
import {deleteUser} from '../User/UserAction';
import {logout} from '../User/UserAction';
import Toast from 'react-native-toast-message';
import * as RNLocalize from 'react-native-localize';

const getDefaultUnit = () => {
  const countryCode = RNLocalize.getLocales()[0]?.countryCode ?? 'US'; // default to US if undefined
  const imperialCountries = ['US', 'LR', 'MM'];
  // return imperialCountries.includes('US') ? 'imperial' : 'metric';

  return imperialCountries.includes(countryCode) ? 'IMPERIAL' : 'METRIC';
};

const initialState = {
  isLoading: false,
  isError: false,
  isRegisterSuccess: false,
  isStart: true,
  message: '',
  user: {
    userId: '',
    token: '',
  },
  userId: '',
  token: '',
  isLoginSuccess: false,
  isLoginStart: true,
  selectedUnit: getDefaultUnit(),
};
export const authSlice = createSlice({
  name: 'auth',
  initialState: initialState,
  reducers: {resetRegisterSuccess: state => {
    state.isRegisterSuccess = false;
    state.isStart= true;
  },
  setSelectedUnit: (state, action) => {
    state.selectedUnit = action.payload;
  },},
  extraReducers: builder => {
    builder

      //Register
      .addCase(register.pending, state => {
        console.log('register pending');
        state.isLoading = true;
      })
      .addCase(register.fulfilled, (state, action) => {
        console.log(
          'register fulfilled',
          'action:',
          action,
          'state:',
          state,
        );
        state.isLoading = false;
        state.isError = false;
        state.isRegisterSuccess = true;
        state.isStart = false;
        // state.user = action.payload.body.data;
        // state.userId = action.payload.body.data.userId;
        // state.token = action.payload.body.data.token;
        // state.message = action.payload.msg;
        // console.log(
        //   'action payload:',
        //   action.payload,
        //   'action payload message:',
        //   action.payload.msg,
        // );

        console.log('state:', initialState);
      })
      .addCase(register.rejected, (state, action) => {
        console.log('register rejected');
        state.isLoading = false;
        state.isError = true;
        state.isRegisterSuccess = false;
        state.isStart = false;
        state.message = '';
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: 'Something went wrong. Please try again!',
          visibilityTime: 3000
        });
      })

      //Login
      .addCase(login.pending, state => {
        console.log('login pending');
        state.isLoading = true;
      })
      .addCase(login.fulfilled, (state, action) => {
        console.log('login fulfilled', 'action:', action, 'state', state);
        state.isLoading = false;
        state.isError = false;
        state.isRegisterSuccess = false;
        state.isLoginSuccess = true;
        state.isLoginStart = false;
        state.user = action.payload.body.data;
        state.userId = action.payload.body.data.userId;
        state.token = action.payload.body.data.token;

        state.message = action.payload.msg;
        console.log('its login fulfilled', 'action:', action, 'state', state);
        console.log(
          'action payload userId:',
          action.payload.body.data.userId,
          'action payload token:',
          action.payload.body.data.token,
        );
      })
      .addCase(login.rejected, (state, action) => {
        console.log('login rejected');
        state.isLoading = false;
        state.isError = true;
        state.isRegisterSuccess = false;
        state.isLoginSuccess = false;
        state.message = '';
        state.isLoginStart = false;
        Toast.show({
          type: 'error',
          text1: 'Login Failed',
          text2: 'Invalid Email or Password. Please try again!',
          visibilityTime: 3000
        });
      })
      .addCase(refreshToken.pending, state => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isRegisterSuccess = false;
        state.isLoginSuccess = true;
        state.isLoginStart = false;
        state.user = action.payload.body.data;
        state.userId = action.payload.body.data.userId;
        state.token = action.payload.body.data.token;
        state.message = action.payload.msg;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isRegisterSuccess = false;
        state.isLoginSuccess = false;
        state.message = '';
        state.isLoginStart = false;
      })
      .addCase(verifyEmail.pending, state => {
        state.isLoading = true;
        console.log('Verify email is pending');
      })
      .addCase(verifyEmail.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        console.log('Verify email is fulfilled');
      })
      .addCase(verifyEmail.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        console.log('Verify email is rejected');
      })
      .addCase(forgotPassword.pending, state => {
        state.isLoading = true;
        console.log('Forgot password is pending');
      })
      .addCase(forgotPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        console.log('Forgot password is fulfilled');
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        console.log('Forgot password is rejected');
      })
      .addCase(updatePassword.pending, state => {
        state.isLoading = true;
        console.log('Update password is pending');
      })
      .addCase(updatePassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        console.log('Update password is fulfilled');
      })
      .addCase(updatePassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        console.log('Update password is rejected');
      })
      .addCase(logout.fulfilled, () => {
        console.log('user logout fulfilled from auth slice');
        return initialState;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        console.log('user delete fulfilled from auth slice');
        return initialState;
      });
  },
});
export const {resetRegisterSuccess, setSelectedUnit} =
  authSlice.actions;

export default authSlice.reducer;
