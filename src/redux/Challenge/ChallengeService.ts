import axios from 'axios';
import {baseUrl, config} from '../../utils/Utils';
import {compressImage, compressVideo} from '../../utils/media';
import {Platform} from 'react-native';
import {FileSystem} from 'react-native-file-access';

const getChallenge = async (challengeId: any) => {
  try {
    let authConfig = await config();
    console.log('Challenge Services : ', authConfig.headers.Authorization);
    const response = await axios.get(
      `${baseUrl}/api/v1/challenge/${challengeId}`,
      authConfig,
    );

    return response.data.body.data;
  } catch (error) {
    console.log('get challengedetail service error message', error);
    console.log(' get challengedetail service error');
    throw error;
  }
};

const getLikeUserList = async ({
  challengeId,
  nextPageToken,
}: {
  challengeId: string;
  nextPageToken: string;
}) => {
  let authConfig = await config();
  let url = `${baseUrl}/api/v1/challenge/${challengeId}/likes`;
  if (nextPageToken) {
    url = `${baseUrl}/api/v1/challenge/${challengeId}/likes?pageToken=${nextPageToken}`;
  }
  const response = await axios.get(url, authConfig);
  return response.data;
};

const createNewChallenge = async (newChallenge: any) => {
  try {
    let authConfig = await config();
    console.log('Challenge Services: ', authConfig.headers.Authorization);
    const response = await axios.post(
      `${baseUrl}/api/v1/challenge`,
      newChallenge,
      authConfig,
      // {
      //   headers: {
      //     Authorization:
      //       'Bearer cOSBf30hW7tYv5OfQXUbbiuxATZLClgpHsI2TSF9wPm+6DhS9yMN3HGXSvouIocgbfgOmfX6F5ekRtaO',
      //     'Content-Type': 'application/json',
      //   },
      // },
    );
    console.log('new challenge', newChallenge);
    // console.log('Challenge creation response:', response);
    console.log('Challenge creation response data:', response.data);

    return response.data;
  } catch (error) {
    console.log('service error message', error);
    console.log('createNewChallenge service error');
    throw error;
  }
};

const updateChallenge = async (updatedChallenge: any) => {
  try {
    let authConfig = await config();
    console.log('Challenge Services: ', authConfig.headers.Authorization);
    console.log('challenge data', updatedChallenge);
    // console.log('auth', authConfig);
    const response = await axios.put(
      `${baseUrl}/api/v1/challenge/${updatedChallenge.challengeId}`,
      updatedChallenge,
      authConfig,
    );
    console.log('updated challenge', updatedChallenge);
    console.log('Challenge update response:', response);
    console.log('Challenge update response data:', response.data);
    return response.data;
  } catch (error) {
    console.log('UpdateChallenge service error');
    console.log('Update service error message', error);

    throw error;
  }
};
//For Challenges joined by the user
const getChallengeFeed = async (userId: string, nextPageToken?: string) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/challenge/participant/`;
    if (userId) {
      url = `${baseUrl}/api/v1/challenge/participant/${userId}/challenges`;
    }
    if (nextPageToken) {
      url = `${baseUrl}/api/v1/challenge/participant/?pageToken=${nextPageToken}`;
      if (userId) {
        url = `${baseUrl}/api/v1/challenge/participant/${userId}/challenges?pageToken=${nextPageToken}`;
      }
    }

    const response = await axios.get(
      url,

      authConfig,
    );
    // console.log('Challenge Feed response:', response);
    // console.log('Challenge feed response data:', response.request._response);
    // return response.request._response;
    return response.data;
  } catch (error) {
    console.log('get user joined challengefeed service error message', error);
    console.log(' user joined challengefeed service error');
    throw error;
  }
};

//For Challenges joined by the user
const getChallengeCompletedFeed = async (
  userId: string,
  nextPageToken?: string,
) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/challenge/participant/?isActive=false`;
    if (userId) {
      url = `${baseUrl}/api/v1/challenge/participant/${userId}/challenges?isActive=false`;
    }
    if (nextPageToken) {
      url = `${baseUrl}/api/v1/challenge/participant/?pageToken=${nextPageToken}&isActive=false`;
      if (userId) {
        url = `${baseUrl}/api/v1/challenge/participant/${userId}/challenges?pageToken=${nextPageToken}&isActive=false`;
      }
    }

    const response = await axios.get(
      url,

      authConfig,
    );
    // console.log('Challenge Feed response:', response);
    // console.log('Challenge feed response data:', response.request._response);
    // return response.request._response;
    return response.data;
  } catch (error) {
    console.log(
      'get user joined completed challengefeed service error message',
      error,
    );
    console.log(' user joined completed challengefeed service error');
    throw error;
  }
};

const getChallengeCreatedByUser = async (
  userId: string,
  nextPageToken?: string,
  leaderboardCreated?: boolean,
) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/challenge/user/${userId}`;
    if (leaderboardCreated !== undefined) {
      url = `${url}?leaderboardCreated=${leaderboardCreated}`;
    }
    if (nextPageToken) {
      url = `${baseUrl}/api/v1/challenge/user/${userId}?pageToken=${nextPageToken}`;
      if (leaderboardCreated !== undefined) {
        url = `${url}&leaderboardCreated=${leaderboardCreated}`;
      }
    }

    const response = await axios.get(
      url,

      authConfig,
    );
    // console.log('Challenge Feed response:', response);
    // console.log('Challenge feed response data:', response.request._response);
    // return response.request._response;
    return response.data;
  } catch (error) {
    console.log('get user created challengefeed service error message', error);
    console.log(' user created challengefeed service error');
    throw error;
  }
};
const deleteChallenge = async (challengeId: any) => {
  try {
    let authConfig = await config();
    const response = await axios.delete(
      `${baseUrl}/api/v1/challenge?id=${challengeId}`,
      authConfig,
    );
    console.log('DeleteChallenge service Feed response:', response);

    return response.data;
  } catch (error) {
    console.log('delete challenge service error message', error);
    console.log(' delete challenge service error');
    throw error;
  }
};

const addChallengeMedia = async (challengeId: string, mediaFile: any) => {
  try {
    let authConfig = await config();
    const formData = new FormData();
    formData.append('challengeId', challengeId);

    await Promise.all(
      mediaFile.map(async (file: any) => {
        console.log('Appending file:', file); // Log each file object to ensure it's valid
        try {
          let outputPath = `${file.uri}_compress.jpg`;
          if (file.type?.startsWith('image/')) {
            outputPath = `${file.uri}_compress.jpg`;
            await compressImage(file.uri, outputPath);
            file.uri = outputPath;
          } else {
            outputPath = `${file.uri}_compress.mp4`;
            await compressVideo(file.uri, outputPath);
            file.uri = outputPath;
            file.name = `${file.name}_compress.mp4`;
          }
        } catch (error) {
          console.log(error, 'compressVideocompressVideo');
        }
        let uri = file.uri;
        if (Platform.OS === 'android' && !file.uri.includes('file://')) {
          uri = 'file://' + uri;
        }
        file.uri = uri;

        console.log(file, 'mediaFilemediaFilemediaFile');

        formData.append('files', {
          uri: file.uri,
           // name: file.name.replace(/\.[^/.]+$/, '.jpg'),
           name: file.name,
           // type: 'image/jpeg',
           type: file.type ,
        });
      }),
    );

    console.log('Challenge Services Auth: ', authConfig.headers.Authorization);
    console.log('Authconfig', authConfig);
    console.log('Authconfig.headers', authConfig.headers);

    const response = await axios.patch(
      `${baseUrl}/api/v1/challenge/media/add`, // Adjust URL for the media upload
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...authConfig.headers, // Include any necessary authentication headers
        },
      },
    );
    console.log('Form data', formData);
    console.log('Add Media to Challenge response :', response);
    console.log('Add Media to Challenge response data:', response.data);
    try {
      const filesArray = formData
        .getParts()
        .filter((item: any) => item.fieldName == 'files');
      filesArray.forEach(async (item: any) => {
        await FileSystem.unlink(item.uri);
      });
    } catch (error) {}
    return response.data;
  } catch (error) {
    console.log('Add Media to Challenge service error');

    console.log('service error message', error);
    throw error;
  }
};

//For Challenges joined by the user
const getChallengeExporeFeed = async (nextPageToken?: string, isActive?: boolean) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/feed/challenges/explore?isActive=${isActive}&sortBy=participant_count`;
    if (nextPageToken) {
      url = `${baseUrl}/api/v1/feed/challenges/explore?pageToken=${nextPageToken}&isActive=${isActive}&sortBy=participant_count`;
    }
    console.log('Challenge Services : ', url);
    const response = await axios.get(url, authConfig);
    console.log('Challenge Feed response:', response);
    // console.log('Challenge feed response data:', response.request._response);
    // return response.request._response;
    return response.data;
  } catch (error) {
    console.log('get user created challengefeed service error message', error);
    console.log(' user created challengefeed service error');
    throw error;
  }
};

const joinChallenge = async (body: {userId: string; challengeId: string}) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/challenge/participant/join`;
    const response = await axios.post(url, body, authConfig);
    return response.data;
  } catch (error) {
    console.log('get user created challengefeed service error message', error);
    console.log(' user created challengefeed service error');
    throw error;
  }
};

const exitChallenge = async (body: {userId: string; challengeId: string}) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/challenge/participant/exit`;
    const response = await axios.post(url, body, authConfig);
    return response.data;
  } catch (error) {
    console.log('get user created challengefeed service error message', error);
    console.log(' user created challengefeed service error');
    throw error;
  }
};

const searchChallenge = async (body: {challengeTitle: string}) => {
  try {
    let authConfig = await config();
    const response = await axios.post(
      `${baseUrl}/api/v1/search/challenge/title`,
      body,
      authConfig,
    );
    return response.data;
  } catch (error) {
    console.log('searchChallenge service error');
    console.log(error);
    throw error;
  }
};

const deleteChallengeMedia = async (updatedPost: any) => {
  try {
    let authConfig = await config();
    const response = await axios.patch(
      `${baseUrl}/api/v1/challenge/media/delete?challengeId=${updatedPost.id}`,
      updatedPost.media,
      authConfig,
    );
    console.log('deleteChallengeMedia:', response.data);
    return response.data;
  } catch (error) {
    console.log('deleteChallengeMedia service error');
    console.log('deleteChallengeMedia', error);

    throw error;
  }
};

const getParticipantsOfChallenge = async (challengeId: string) => {
  try {
    let authConfig = await config();
    const response = await axios.get(
      `${baseUrl}/api/v1/challenge/participant/${challengeId}`,
      authConfig,
    );
    console.log('getParticipantsOfChallenge:', response.data);
    return response.data;
  } catch (error) {
    console.log('getParticipantsOfChallenge service error');
    console.log('getParticipantsOfChallenge', error);

    throw error;
  }
};

const getPostLinkedToChallenge = async (data: {
  challengeId: string;
  nextPageToken?: string;
}) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/post/challenge/${data.challengeId}/posts`;
    if (data.nextPageToken) {
      url = `${baseUrl}/api/v1/post/challenge/${data.challengeId}/posts?pageToken=${data.nextPageToken}`;
    }
    const response = await axios.get(url, authConfig);
    console.log('getPostLinkedToChallenge:', response.data, url);
    return response.data;
  } catch (error) {
    console.log('getPostLinkedToChallenge service error');
    console.log('getPostLinkedToChallenge', error);

    throw error;
  }
};
const generateDetailsFromAI = async (data: {
  prompt: string;
  isPost: boolean;
}) => {
  try {
    let authConfig = await config();
    const type = data.isPost ? 'POST' : 'CHALLENGE';
    let url = `${baseUrl}/api/v1/ai/chat/init?prompt=${data.prompt}&feature=${type}`;
    const response = await axios.get(url, authConfig);
    console.log('generateDetailsFromAI:', response.data, url);
    return response.data;
  } catch (error) {
    console.log('generateDetailsFromAI error', error);
    throw error;
  }
};
const reGenerateDetailsFromAI = async (data: {
  type: string;
  sessionId: string;
  isPost: boolean;
}) => {
  try {
    let authConfig = await config();
    const type = data.isPost ? 'POST' : 'CHALLENGE';
    let url = `${baseUrl}/api/v1/ai/chat/${data.sessionId}?change=${data.type}&feature=${type}`;
    const response = await axios.get(url, authConfig);
    console.log('reGenerateDetailsFromAI:', response.data);
    return response.data;
  } catch (error) {
    console.log('reGenerateDetailsFromAI error', error);
    throw error;
  }
};
const finalizeChallenge = async (challengeId: any) => {
  try {
    let authConfig: any = await config();
    authConfig.headers['Content-Type'] = 'application/json';
    // console.log('Challenge Services: ', authConfig.headers.Authorization);
    console.log('challenge Id', challengeId);
    // console.log('auth', authConfig);
    const response = await axios.patch(
      `${baseUrl}/api/v1/challenge/finalize?challengeId=${challengeId}`,
      {},
      authConfig,
    );
    console.log('Challenge Finalize response:', response);
    console.log('Challenge Finalize response data:', response.data);
    return response;
  } catch (error) {
    console.log('Finalize Challenge service error');
    console.log('Finalize service error message', error);

    throw error;
  }
};

export default {
  getChallenge,
  addChallengeMedia,
  createNewChallenge,
  updateChallenge,
  getChallengeFeed,
  deleteChallenge,
  getChallengeExporeFeed,
  getChallengeCreatedByUser,
  joinChallenge,
  exitChallenge,
  getChallengeCompletedFeed,
  searchChallenge,
  deleteChallengeMedia,
  getParticipantsOfChallenge,
  getPostLinkedToChallenge,
  generateDetailsFromAI,
  reGenerateDetailsFromAI,
  getLikeUserList,
  finalizeChallenge,
};
