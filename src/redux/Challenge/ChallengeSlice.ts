import {createSlice} from '@reduxjs/toolkit';
import {
  getChallenge,
  createNew<PERSON>hallenge,
  updateChallenge,
  getChallengeFeed,
  deleteChallenge,
  addChallengeMedia,
  getChallengeFeedExplore,
  getChallengeCreatedByUser,
  getChallengeCompletedFeed,
  getChallengeForUpdate,
  deleteChallengeMedia,
  finalizeChallenge,
} from './ChallengeAction';
import {logout} from '../User/UserAction';
import {deleteUser} from '../User/UserAction';
const initialState = {
  challenges: {},
  challengesFeed: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  challengesFeedCompleted: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  status: 'idle',
  error: null,
  isRefresh: false,
  isCreateSuccess: false,
  isDeleteSuccess: false,
  isUpdateSuccess: false,
  isFinalizeSuccess: false,

  challengeDraft: {
    challengeId: ''
  },
  challengesExploreFeed: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  challengeCreatedByUser: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  isAddMediaSuccess: false
};

const challengeSlice = createSlice({
  name: 'challenge',
  initialState,
  reducers: {
    resetCreateSuccess: state => {
      state.isCreateSuccess = false;
    },
    resetFinalizeSuccess: state => {
      state.isFinalizeSuccess = false;
    },
    resetDeleteSuccess: state => {
      state.isDeleteSuccess = false;
    },
    resetUpdateSuccess: state => {
      state.isUpdateSuccess = false;
      state.challengeDraft = {
        challengeId: ''
      };
    },
    resetAddMediaSuccess: state => {
      state.isAddMediaSuccess = false;
    },
    updateChallengeData: (state, action) => {
      state.challengeDraft = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(getChallenge.pending, state => {
        console.log('Get challenge is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getChallenge.fulfilled, (state, action) => {
        console.log('Get challenge is fulfilled');
        state.challenges = action.payload;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getChallenge.rejected, (state, action) => {
        console.log('Get challenge is rejected');
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getChallengeForUpdate.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getChallengeForUpdate.fulfilled, (state, action) => {
        state.challengeDraft = action.payload;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getChallengeForUpdate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(deleteChallengeMedia.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(deleteChallengeMedia.fulfilled, (state, action) => {
        const challengeDraft: any = state.challengeDraft;
        challengeDraft.mediaRefs = challengeDraft.mediaRefs?.filter(
          (media: any) => media !== action.meta.arg.media[0],
        );
        state.challengeDraft = challengeDraft;
      })
      .addCase(deleteChallengeMedia.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getChallengeFeed.pending, state => {
        console.log('Get challengeFeed is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getChallengeFeed.fulfilled, (state, action) => {
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        // console.log('challengesFeed action payload', action.payload);
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.challengesFeed.data, ...action.payload.data]
          : action.payload.data;
        state.challengesFeed = action.payload;
        state.challengesFeed.hasNext = hasNext;

        console.log('Updated state.challengesFeed', state.challengesFeed);
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getChallengeFeed.rejected, (state, action) => {
        console.log('Get challengeFeed is rejected');
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getChallengeCompletedFeed.pending, state => {
        console.log('Get challengeFeed is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getChallengeCompletedFeed.fulfilled, (state, action) => {
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        // console.log('challengesFeedCompleted action payload', action.payload);
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.challengesFeedCompleted.data, ...action.payload.data]
          : action.payload.data;
        state.challengesFeedCompleted = action.payload;
        state.challengesFeedCompleted.hasNext = hasNext;

        console.log(
          'Updated state.challengesFeedCompleted',
          state.challengesFeedCompleted,
        );
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getChallengeCompletedFeed.rejected, (state, action) => {
        console.log('Get challengeFeed is rejected');
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(updateChallenge.pending, state => {
        console.log('Update challenge is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(updateChallenge.fulfilled, (state, action) => {
        console.log('Update challenge is fulfilled');
        state.status = 'succeeded';
        state.error = null;
        state.isUpdateSuccess = true;
        console.log('Update New Challenge action payload', action.payload);
      })
      .addCase(updateChallenge.rejected, (state, action) => {
        console.log('Update challenge is rejected');
        state.status = 'failed';
        state.error = action.payload;
        state.isUpdateSuccess = false;
      })

      .addCase(createNewChallenge.pending, state => {
        console.log('Create New challenge is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(createNewChallenge.fulfilled, (state, action) => {
        console.log('Create New challenge is fulfilled');
        console.log('Create New Challenge action payload', action.payload);
        // state.challengesFeed = [action.payload, ...state.challengesFeed];
        state.challengeDraft = action.payload;
        state.status = 'succeeded';
        state.isCreateSuccess = true;
        state.error = null;
        state.isRefresh = !state.isRefresh;
        console.log('createnewchallenge slice', state.isRefresh);
      })

      .addCase(createNewChallenge.rejected, (state, action) => {
        console.log('Create New challenge is rejected');
        console.log('action', action);
        state.status = 'failed';
        state.isCreateSuccess = false;

        state.error = action.payload;
      })

      .addCase(deleteChallenge.pending, state => {
        console.log('Delete challenge is pending');

        state.status = 'loading';
        state.error = null;
      })
      .addCase(deleteChallenge.fulfilled, (state, action) => {
        // state.posts = state.posts.filter(post => post.id !== action.payload.id);
        console.log('Delete challenge is fulfilled');
        console.log('action', action);
        state.challengeCreatedByUser.data = state.challengeCreatedByUser.data.filter((item: any) => action.meta.arg !== item.challengeId)
        state.challengesExploreFeed.data = state.challengesExploreFeed.data.filter((item: any) => action.meta.arg !== item.challengeId)
        state.challengesFeed.data =state.challengesFeed.data.filter((item: any) => action.meta.arg !== item.challengeId)
        state.challengesFeedCompleted.data = state.challengesFeedCompleted.data.filter((item: any) => action.meta.arg !== item.challengeId)
        state.isDeleteSuccess = true;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(deleteChallenge.rejected, (state, action) => {
        console.log('Delete challenge is rejected');

        state.status = 'failed';
        state.error = action.payload;
        state.isDeleteSuccess = false;
      })
      .addCase(addChallengeMedia.pending, state => {
        console.log('Add Media to challenge is pending');

        state.status = 'loading';
        state.error = null;
      })
      .addCase(addChallengeMedia.fulfilled, (state, action) => {
        console.log('Add media to challenge is fulfilled');
        state.status = 'succeeded';
        state.error = null;
        state.isAddMediaSuccess = true;
        console.log('Add media to challenge action payload', action.payload);
      })
      .addCase(addChallengeMedia.rejected, (state, action) => {
        console.log('Add media to challenge is rejected');
        state.status = 'failed';
        state.isAddMediaSuccess = false;
        state.error = action.payload;
      })
      .addCase(getChallengeFeedExplore.pending, state => {
        console.log('Get challengeFeed is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getChallengeFeedExplore.fulfilled, (state, action) => {
        console.log('Get challengeFeed is fulfilled');
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.challengesExploreFeed.data, ...action.payload.data]
          : action.payload.data;
        state.challengesExploreFeed = action.payload;
        state.challengesExploreFeed.hasNext = hasNext;
        console.log('Updated state.challengesFeed', state.challengesExploreFeed);
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getChallengeFeedExplore.rejected, (state, action) => {
        console.log('Get challengeFeed is rejected');
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getChallengeCreatedByUser.pending, state => {
        console.log('Get challengeFeed is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getChallengeCreatedByUser.fulfilled, (state, action) => {
        console.log('Get challengeFeed is fulfilled');
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.challengeCreatedByUser.data, ...action.payload.data]
          : action.payload.data;
        state.challengeCreatedByUser = action.payload;
        state.challengeCreatedByUser.hasNext = hasNext;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getChallengeCreatedByUser.rejected, (state, action) => {
        console.log('Get challengeFeed is rejected');
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(logout.fulfilled, () => {
        console.log('user logout fulfilled from challenge slice');
        return initialState;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        console.log('user delete fulfilled from auth slice');
        return initialState;
      })
      .addCase(finalizeChallenge.pending, state => {
              console.log('Finalize challenge is pending');
              state.status = 'loading';
              state.error = null;
            })
      .addCase(finalizeChallenge.fulfilled, (state, action) => {
              console.log('Finalize challenge is fulfilled');
              state.status = 'succeeded';
              state.error = null;
              state.isFinalizeSuccess = true;
              console.log('Finalize New challenge action payload', action.payload);
            })
      .addCase(finalizeChallenge.rejected, (state, action) => {
              console.log('Finalize challenge is rejected');
      
              state.status = 'failed';
              state.error = action.payload;
              state.isFinalizeSuccess = false;
            });
  },
});
export const {resetCreateSuccess, resetDeleteSuccess, resetUpdateSuccess, resetAddMediaSuccess, updateChallengeData,resetFinalizeSuccess} =
  challengeSlice.actions;

export default challengeSlice.reducer;
