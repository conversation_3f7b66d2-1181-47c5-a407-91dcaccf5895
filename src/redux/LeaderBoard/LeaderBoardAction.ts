import {createAsyncThunk} from '@reduxjs/toolkit';
import leaderBoardService from './LeaderBoardService';

export const getLeaderBoardById: any = createAsyncThunk(
  'challenge/getLeaderBoardById',
  async (data: any, thunkAPI) => {
    try {
      const response: any = await leaderBoardService.getLeaderBoardById(
        data,
      );
      return response;
    } catch (error) {
      console.log('Get getLeaderBoardById action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getJoinedLeaderBoard: any = createAsyncThunk(
  'challenge/getJoinedLeaderBoard',
  async (data: {userId: string; nextPageToken: string;}, thunkAPI) => {
    try {
      const response: any = await leaderBoardService.getJoinedLeaderBoard(
        data,
      );
      return response;
    } catch (error) {
      console.log('Get getJoinedLeaderBoard action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getLeaderBoardCompleted: any = createAsyncThunk(
  'challenge/getLeaderBoardCompleted',
  async (data: {userId: string; nextPageToken: string;}, thunkAPI) => {
    try {
      const response: any = await leaderBoardService.getLeaderBoardCompleted(
        data
      );
      return response;
    } catch (error) {
      console.log('Get getLeaderBoardCompleted action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const searchLeaderBoard: any = createAsyncThunk(
  'user/searchLeaderBoard',
  async (body: {challengeTitle: string}, thunkAPI) => {
    try {
      const response: any = await leaderBoardService.searchLeaderBoard(body);
      return response;
    } catch (error) {
      console.log('searchLeaderBoard action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const updatePointInBoard: any = createAsyncThunk(
  'user/updatePointInBoard',
  async (
    body: {challengeId: string; userId: string; points: number},
    thunkAPI,
  ) => {
    try {
      const response: any = await leaderBoardService.updatePointInBoard(body);
      return response;
    } catch (error) {
      console.log('searchLeaderBoard action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const deleteLeaderBoard: any = createAsyncThunk(
  'user/deleteLeaderBoard',
  async (
    body: {challengeId: string; userId: string;},
    thunkAPI,
  ) => {
    try {
      const response: any = await leaderBoardService.deleteLeaderBoard(body);
      return response;
    } catch (error) {
      console.log('deleteLeaderBoard action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
