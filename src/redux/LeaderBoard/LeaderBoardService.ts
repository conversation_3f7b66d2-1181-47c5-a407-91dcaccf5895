import axios from 'axios';
import {baseUrl, config} from '../../utils/Utils';

const getLeaderBoardById = async ({
  challengeId,
  nextPageToken,
}: {
  challengeId: string;
  nextPageToken: string;
}) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/leaderboard/${challengeId}`;
    if (nextPageToken) {
      url = `${baseUrl}/api/v1/leaderboard/${challengeId}?pageToken=${nextPageToken}`;
    }
    const response = await axios.get(url, authConfig);
    console.log('getLeaderBoardById response', response.data, typeof response.data);
    console.log('challenge created by', response.data.data[0].challengeCreatedBy);

    return response.data;
  } catch (error) {
    console.log('get getLeaderBoardById service error message', error);
    console.log(' get getLeaderBoardById service error');
    throw error;
  }
};
const getLeaderBoardCompleted = async (data: {
  userId: string;
  nextPageToken: string;
}) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/leaderboard/joined/${data.userId}?isActive=false`;
    if (data.nextPageToken) {
      url = `${baseUrl}/api/v1/leaderboard/joined/${data.userId}?isActive=false&pageToken=${data.nextPageToken}`;
    }
    const response = await axios.get(url, authConfig);
    return response.data;
  } catch (error) {
    console.log('get getJoinedLeaderBoard service error message', error);
    console.log(' get getJoinedLeaderBoard service error');
    throw error;
  }
};

const getJoinedLeaderBoard = async (data: {
  userId: string;
  nextPageToken: string;
}) => {
  try {
    let authConfig = await config();
    let url = `${baseUrl}/api/v1/leaderboard/joined/${data.userId}?isActive=true`;
    if (data.nextPageToken) {
      url = `${baseUrl}/api/v1/leaderboard/joined/${data.userId}?isActive=true&pageToken=${data.nextPageToken}`;
    }
    const response = await axios.get(url, authConfig);
    return response.data;
  } catch (error) {
    console.log('get getJoinedLeaderBoard service error message', error);
    console.log(' get getJoinedLeaderBoard service error');
    throw error;
  }
};

const searchLeaderBoard = async (body: {challengeTitle: string}) => {
  try {
    let authConfig = await config();
    const response = await axios.post(
      `${baseUrl}/api/v1/search/leaderboard/title`,
      body,
      authConfig,
    );
    return response.data;
  } catch (error) {
    console.log('searchLeaderBoard service error');
    console.log(error);
    throw error;
  }
};

const updatePointInBoard = async (body: {
  challengeId: string;
  userId: string;
  postId: string;
  points: number;
  leaderboardType: string;
}) => {
  try {
    let authConfig = await config();
    const response = await axios.put(
      `${baseUrl}/api/v1/leaderboard/points`,
      body,
      authConfig,
    );
    console.log('updatePointInBoard', body, response.data);
    return response.data;
  } catch (error) {
    console.log('updatePointInBoard service error', body);
    console.log(error);
    throw error;
  }
};

const deleteLeaderBoard = async (body: {
  challengeId: string;
  userId: string;
}) => {
  try {
    let authConfig = await config();
    const response = await axios.delete(
      `${baseUrl}/api/v1/leaderboard/delete/challenge`,
      {
        ...authConfig,
        data: body,
      },
    );
    console.log('deleteLeaderBoard', body, response.data);
    return response.data;
  } catch (error) {
    console.log('deleteLeaderBoard service error');
    console.log(error);
    throw error;
  }
};

export default {
  getLeaderBoardById,
  getJoinedLeaderBoard,
  getLeaderBoardCompleted,
  searchLeaderBoard,
  updatePointInBoard,
  deleteLeaderBoard,
};
