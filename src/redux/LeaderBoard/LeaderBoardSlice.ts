import {createSlice} from '@reduxjs/toolkit';
import {
  getJoinedLeaderBoard,
  getLeaderBoardById,
  getLeaderBoardCompleted,
} from './LeaderBoardAction';
import {logout, deleteUser} from '../User/UserAction';

export interface LeaderBoardDetails {
  challengeId: string;
  challengeCreatedBy: {
    firstName: string;
    lastName: string;
    points: string;
    userId: string;
    imageReference: string;
  };
  description: string;
  userPointsList: UserPointsList[];
  pointsTitle: string;
  ChallengeTitle: string;
  challengeTitle: string;
}

export interface UserPointsList {
  firstName: string;
  lastName: string;
  userId: string;
  points: number;
}

export interface LeaderBoard {
  challengeId: string;
  title: string;
  description: string;
  userId: string;
  location: any;
  tags: any;
  mediaRefs: string[];
  visibility: string;
  difficultyRating: string;
  postCount: any;
  participantCount: number;
  views: any;
  likes: any;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
  isLeaderboardCreated: boolean;
  pointsTitle: string;
  points: number;
  pointsDescription: string;
  isPointsAscending: boolean;
  isPointsAutomated: boolean;
}

type LeaderBoardStateType = {
  leaderBoard: LeaderBoardDetails | null;
  loading: boolean;
  leaderBoardList: {
      data: Array<LeaderBoard>;
      nextPageToken: string;
      hasNext: boolean;
  };
  leaderBoardCompletedList: {
    data: Array<LeaderBoard>;
    nextPageToken: string;
    hasNext: boolean;
}
};

const initialState: LeaderBoardStateType = {
  leaderBoard: null,
  loading: false,
  leaderBoardList: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  leaderBoardCompletedList: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
};

const challengeSlice = createSlice({
  name: 'challenge',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(getLeaderBoardById.pending, state => {
        console.log('Get challenge is pending');
        state.loading = true;
      })
      .addCase(getLeaderBoardById.fulfilled, (state, action) => {
        console.log('Get challenge is fulfilled');
        state.leaderBoard = action.payload;
        state.loading = false;
      })
      .addCase(getLeaderBoardById.rejected, (state, action) => {
        console.log('Get challenge is rejected');
        state.loading = false;
      })
      .addCase(getJoinedLeaderBoard.pending, state => {
        console.log('Get challenge is pending');
        state.loading = true;
      })
      .addCase(getJoinedLeaderBoard.fulfilled, (state, action) => {
        console.log('Get getJoinedLeaderBoard is fulfilled', action.payload);
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.leaderBoardList.data, ...action.payload.data]
          : action.payload.data;
        state.leaderBoardList = action.payload;
        state.leaderBoardList.hasNext = hasNext;

        state.loading = false;
      })
      .addCase(getJoinedLeaderBoard.rejected, (state, action) => {
        console.log('Get challenge is rejected');
        state.loading = false;
      })
      .addCase(getLeaderBoardCompleted.pending, state => {
        console.log('Get challenge is pending');
        state.loading = true;
      })
      .addCase(getLeaderBoardCompleted.fulfilled, (state, action) => {
        console.log('Get getLeaderBoardCompleted is fulfilled');
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.leaderBoardCompletedList.data, ...action.payload.data]
          : action.payload.data;
        state.leaderBoardCompletedList = action.payload;
        state.leaderBoardCompletedList.hasNext = hasNext;
        state.loading = false;
      })
      .addCase(getLeaderBoardCompleted.rejected, (state, action) => {
        console.log('Get challenge is rejected');
        state.loading = false;
      })
      .addCase(logout.fulfilled, () => {
        console.log('user logout fulfilled from leaderboard slice');
        return initialState;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        console.log('user delete fulfilled from leadderboard slice');
        return initialState;
      });
  },
});

export default challengeSlice.reducer;
