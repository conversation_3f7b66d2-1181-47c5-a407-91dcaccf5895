import {createAsyncThunk} from '@reduxjs/toolkit';
import postService from './PostService';
import ChallengeService from '../Challenge/ChallengeService';

export const fetchPublicPosts = createAsyncThunk(
  'posts/fetchPublicPosts',
  async ({nextPageToken}: {nextPageToken?: string}, thunkAPI) => {
    try {
      const response: any = await postService.getAllPublicPosts(nextPageToken);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const fetchFollowedPosts = createAsyncThunk(
  'posts/fetchFollowedPosts',
  async ({nextPageToken}: {nextPageToken?: string}, thunkAPI) => {
    try {
      const response: any = await postService.getAllFollowedPost(nextPageToken);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const fetchPosts: any = createAsyncThunk(
  'posts/fetchPosts',
  async (userId, thunkAPI) => {
    try {
      console.log('Get Post action service was run', 'userdata:', userId);
      const response: any = await postService.getAllPosts(userId);
      return response.data;
    } catch (error) {
      console.log('Get Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const createNewPost: any = createAsyncThunk(
  'posts/createNewPost',
  async (postData, thunkAPI) => {
    try {
      console.log('Create Post action service was run', 'postdata:', postData);
      const response = await postService.createNewPost(postData);
      return response;
    } catch (error) {
      console.log('Create Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const updatePost: any = createAsyncThunk(
  'posts/updatePost',
  async (updateData, thunkAPI) => {
    try {
      console.log(
        'Update Post action service was run',
        'postdata:',
        updateData,
      );
      const response = await postService.updatePost(updateData);
      return response;
    } catch (error) {
      console.log('Update Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const updateUserPost: any = createAsyncThunk(
  'posts/updateUserPost',
  async (updateData, thunkAPI) => {
    return updateData;
  },
);
export const deletePostMedia: any = createAsyncThunk(
  'posts/deletePostMedia',
  async (updateData, thunkAPI) => {
    try {
      console.log(
        'Update Post action service was run',
        'deletePostMedia:',
        updateData,
      );
      const response = await postService.deletePostMedia(updateData);
      return response;
    } catch (error) {
      console.log('Update Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const addPostMedia: any = createAsyncThunk(
  'posts/addPostMedia',
  async ({postId, mediaFile}: any, thunkAPI) => {
    try {
      console.log(
        'Add Post Media action service was run',
        'postId:',
        postId,
        'mediaFile',
        mediaFile,
      );
      const response = await postService.addPostMedia(postId, mediaFile);

      return response;
    } catch (error: any) {
      console.log(
        'Add Post Media action service error:',
        error.response?.data || error.message,
      );
      return thunkAPI.rejectWithValue(error.response?.data || error.message);
    }
  },
);
export const deletePost: any = createAsyncThunk(
  'posts/deletePost',
  async (postId, thunkAPI) => {
    try {
      console.log('Delete Post action service was run', 'postdata:', postId);
      const response = await postService.deletePost(postId);
      return response;
    } catch (error) {
      console.log('Delete Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const deletePublicFeedChallenge: any = createAsyncThunk(
  'posts/deletePublicFeedChallenge',
  async (postId, thunkAPI) => {
    try {
      console.log('Delete Post action service was run', 'postdata:', postId);
      const response = await ChallengeService.deleteChallenge(postId);
      return response;
    } catch (error) {
      console.log('Delete Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getPostById: any = createAsyncThunk(
  'posts/getPostById',
  async (postId: string, thunkAPI) => {
    try {
      console.log('Delete Post action service was run', 'postdata:', postId);
      const response = await postService.getPostById(postId);
      return response;
    } catch (error) {
      console.log('Delete Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getPostOfUser: any = createAsyncThunk(
  'posts/getPostOfUser',
  async (data: {userId: string; nextPageToken: string}, thunkAPI) => {
    try {
      const response = await postService.getPostOfUser(data);
      return response;
    } catch (error) {
      console.log('Delete Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const unlinkPostFromChallenge: any = createAsyncThunk(
  'posts/unlinkPostFromChallenge',
  async (updateData, thunkAPI) => {
    try {
      console.log(
        'Unlink Post from challenge action service was run',
        'postdata:',
        updateData,
      );
      const response = await postService.unlinkPostFromChallenge(updateData);
      return response;
    } catch (error) {
      console.log('Unlink Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const likeUnlikePost: any = createAsyncThunk(
  'posts/likeUnlikePost',
  async (data, thunkAPI) => {
    try {
      console.log('likeUnlikePost', 'postdata:', data);
      const response = await postService.likeUnlikePost(data);
      return response;
    } catch (error) {
      console.log('Unlink Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const removeLikeUnlikePost: any = createAsyncThunk(
  'posts/removeLikeUnlikePost',
  async (data, thunkAPI) => {
    try {
      console.log('removeLikeUnlikePost', 'postdata:', data);
      const response = await postService.removeLikeUnlikePost(data);
      return response;
    } catch (error) {
      console.log('removeLikeUnlikePost Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const likeUnlikeChallengeFromPost: any = createAsyncThunk(
  'posts/likeUnlikeChallengeFromPost',
  async (data, thunkAPI) => {
    try {
      console.log('likeUnlikeChallengeFromPost', 'postdata:', data);
      const response = await postService.likeUnlikeChallengeFromPost(data);
      return response;
    } catch (error) {
      console.log('likeUnlikeChallengeFromPost Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const removeLikeUnlikeChallengeFromPost: any = createAsyncThunk(
  'posts/removeLikeUnlikeChallengeFromPost',
  async (data, thunkAPI) => {
    try {
      console.log('removeLikeUnlikeChallengeFromPost', 'postdata:', data);
      const response = await postService.removeLikeUnlikeChallengeFromPost(data);
      return response;
    } catch (error) {
      console.log('removeLikeUnlikeChallengeFromPost Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const getLikeUserList: any = createAsyncThunk(
  'posts/getLikeUserList',
  async (data: any, thunkAPI) => {
    try {
      const response = await postService.getLikeUserList(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const finalizePost: any = createAsyncThunk(
  'posts/finalizePost',
  async (postId, thunkAPI) => {
    try {
      console.log(
        'Finalize Post action service was run',
        'postId:',
        postId,
      );
      const response = await postService.finalizePost(postId);
      return response;
    } catch (error) {
      console.log('Finalize Post action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);