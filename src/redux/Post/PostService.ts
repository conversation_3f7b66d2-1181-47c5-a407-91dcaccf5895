import axios from 'axios';
import {baseUrl, config} from '../../utils/Utils';
import {compressImage, compressVideo} from '../../utils/media';
import {Platform} from 'react-native';
import {FileSystem} from 'react-native-file-access';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';

const getAllPublicPosts = async (nextPageToken?: string) => {
  let authConfig: any = await config();
  authConfig.headers.id = '10';
  authConfig.headers['Content-Type'] = 'application/json';
  let url = `${baseUrl}/api/v1/feed/public`;
  if (nextPageToken) {
    url = `${baseUrl}/api/v1/feed/public?pageToken=${nextPageToken}`;
  }
  // I am using fetch because API need content type in GET request mandatory but GET request never have request content that's why axios not consider content type header in request end
  const response = await fetch(url, {
    method: 'GET',
    headers: authConfig.headers,
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  });

  return response;
};

const getAllPosts = async (userId: any) => {
  let authConfig = await config();
  console.log('Post Services : ', authConfig.headers.Authorization);
  const response = await axios.get(
    `${baseUrl}/api/post?id=${userId}`,
    authConfig,
  );
  return response.data;
};

const createNewPost = async (newPost: any) => {
  try {
    let authConfig = await config();
    console.log('Post Services: ', authConfig.headers.Authorization);
    const response = await axios.post(
      `${baseUrl}/api/v1/post`,
      newPost,
      authConfig,
    );
    console.log('new post', newPost);
    console.log('Post creation response :', response);
    console.log('Post creation response data:', response.data);

    return response.data.body.data;
  } catch (error) {
    console.log('service error message', error);
    console.log('createNewPost service error');
    throw error;
  }
};

const updatePost = async (updatedPost: any) => {
  try {
    let authConfig = await config();
    console.log('Post Services: ', authConfig.headers.Authorization);
    console.log('post data', updatedPost);
    // console.log('auth', authConfig);
    const response = await axios.put(
      `${baseUrl}/api/v1/post/`,
      updatedPost,
      authConfig,
    );
    console.log('updated post', updatedPost);
    console.log('Post update response:', response);
    console.log('Post update response data:', response.data);
    return response.data;
  } catch (error) {
    console.log('UpdatePost service error');
    console.log('Update service error message', error);

    throw error;
  }
};

const deletePostMedia = async (updatedPost: any) => {
  try {
    let authConfig = await config();
    const response = await axios.patch(
      `${baseUrl}/api/v1/post/media/delete?postId=${updatedPost.id}`,
      updatedPost.media,
      authConfig,
    );
    console.log('deletePostMediadeletePostMedia:', response.data);
    return response.data;
  } catch (error) {
    console.log('deletePostMedia service error');
    console.log('deletePostMedia', error);

    throw error;
  }
};

const addPostMedia = async (postId: string, mediaFile: any) => {
  try {
    let authConfig = await config();
    const formData = new FormData();
    formData.append('postId', postId); // Append the post ID
    // Append each file in the mediaFiles array
    await Promise.all(
      mediaFile.map(async (file: any) => {
        console.log('Appending file:', file); // Log each file object to ensure it's valid
        try {
          let outputPath = `${file.uri}_compress.jpg`;
          if (file.type?.startsWith('image/')) {
            outputPath = `${file.uri}_compress.jpg`;
            await compressImage(file.uri, outputPath);
            file.uri = outputPath;
          } else {
            outputPath = `${file.uri}_compress.mp4`;
            await compressVideo(file.uri, outputPath);
            file.uri = outputPath;
            file.name = `${file.name}_compress.mp4`;
          }
        } catch (error) {
          console.log(error, 'compressVideocompressVideo');
        }
        let uri = file.uri;
        if (Platform.OS === 'android' && !file.uri.includes('file://')) {
          uri = 'file://' + uri;
        }
        file.uri = uri;

        console.log(file, 'mediaFilemediaFilemediaFile');

        formData.append('files', {
          uri: file.uri,
          // name: file.name.replace(/\.[^/.]+$/, '.jpg'),
          name: file.name,
          // type: 'image/jpeg',
          type: file.type ,
        });
      }),
    );

    console.log('Post Services Auth: ', authConfig.headers.Authorization);
    console.log('Authconfig', authConfig);
    console.log('Authconfig.headers', authConfig.headers);

    const response = await axios.patch(
      `${baseUrl}/api/v1/post/media/add`, // Adjust URL for the media upload
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...authConfig.headers, // Include any necessary authentication headers
        },
      },
    );
    console.log('Form data', formData);
    console.log('Add Media to Post response :', response);
    console.log('Add Media to Post response data:', response.data);
    try {
      const filesArray = formData
        .getParts()
        .filter((item: any) => item.fieldName == 'files');
      filesArray.forEach(async (item: any) => {
        await FileSystem.unlink(item.uri);
      });
    } catch (error) {}
    return response.data;
  } catch (error) {
    console.log('Add Media to Post service error');

    console.log('service error message', error);
    throw error;
  }
};

const deletePost = async (postId: any) => {
  let authConfig = await config();
  console.log('Post Services: ', authConfig.headers.Authorization);
  const response = await axios.delete(
    `${baseUrl}/api/v1/post?id=${postId}`,
    authConfig,
  );
  return response.data;
};

const getPostById = async (postId: string) => {
  let authConfig = await config();
  const response = await axios.get(
    `${baseUrl}/api/v1/post/${postId}`,
    authConfig,
  );
  return response.data;
};

const getLikeUserList = async ({
  postId,
  nextPageToken,
}: {
  postId: string;
  nextPageToken: string;
}) => {
  let authConfig = await config();
  let url = `${baseUrl}/api/v1/post/${postId}/likes`;
  if (nextPageToken) {
    url = `${baseUrl}/api/v1/post/${postId}/likes?pageToken=${nextPageToken}`;
  }
  const response = await axios.get(url, authConfig);
  return response.data;
};

const getPostOfUser = async (data: {userId: string; nextPageToken: string}) => {
  let url = `${baseUrl}/api/v1/post/user/${data.userId}/posts`;
  if (data.nextPageToken) {
    url = `${baseUrl}/api/v1/post/user/${data.userId}/posts?pageToken=${data.nextPageToken}`;
  }
  let authConfig = await config();
  const response = await axios.get(url, authConfig);
  return response.data;
};

const getAllFollowedPost = async (nextPageToken?: string) => {
  let authConfig: any = await config();
  let url = `${baseUrl}/api/v1/feed/home`;
  if (nextPageToken) {
    url = `${baseUrl}/api/v1/feed/home?pageToken=${nextPageToken}`;
  }
  const response = await fetch(url, {
    method: 'GET',
    headers: authConfig.headers,
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  });

  return response;
};
const unlinkPostFromChallenge = async (unlinkchallenge: any) => {
  try {
    let authConfig = await config();
    console.log('Challenge Services: ', authConfig.headers.Authorization);
    console.log('post challenge data', unlinkchallenge);
    // console.log('auth', authConfig);
    const response = await axios.put(
      `${baseUrl}/api/v1/post/unlink`,
      unlinkchallenge,
      authConfig,
    );
    console.log('unlink challneg body', unlinkchallenge);
    console.log('Post unlink response:', response);
    console.log('Post unlink response data:', response.data);
    return response.data;
  } catch (error) {
    console.log('Unlink post service error');
    console.log('Unlink post service error message', error);

    throw error;
  }
};

const likeUnlikePost = async (newPost: any) => {
  try {
    let authConfig = await config();
    const response = await axios.put(
      `${baseUrl}/api/v1/post/activity`,
      newPost,
      authConfig,
    );
    console.log(response.data);

    return response.data;
  } catch (error) {
    console.log('service error message', error);
    console.log('likeUnlikePost service error');
    throw error;
  }
};

const removeLikeUnlikePost = async (newPost: any) => {
  try {
    let authConfig = await config();
    const response = await axios.delete(`${baseUrl}/api/v1/post/activity`, {
      ...authConfig,
      data: newPost,
    });
    console.log(response.data);

    return response.data;
  } catch (error) {
    console.log('service error message', error);
    console.log('removeLikeUnlikePost service error');
    throw error;
  }
};

const likeUnlikeChallengeFromPost = async (newPost: any) => {
  try {
    let authConfig = await config();
    const response = await axios.put(
      `${baseUrl}/api/v1/challenge/activity`,
      newPost,
      authConfig,
    );
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.log('service error message', error);
    console.log('likeUnlikeChallengeFromPost service error');
    throw error;
  }
};

const removeLikeUnlikeChallengeFromPost = async (newPost: any) => {
  try {
    let authConfig = await config();
    const response = await axios.delete(
      `${baseUrl}/api/v1/challenge/activity`,
      {
        ...authConfig,
        data: newPost,
      },
    );
    console.log(response.data);

    return response.data;
  } catch (error) {
    console.log('service error message', error);
    console.log('removeLikeUnlikePost service error');
    throw error;
  }
};

const finalizePost = async (postId: any) => {
  try {
    let authConfig: any = await config();
    authConfig.headers['Content-Type'] = 'application/json';

    // console.log('Post Services: ', authConfig.headers.Authorization);
    console.log('post id', postId);
    // console.log('auth', authConfig);
    const response = await axios.patch(
      `${baseUrl}/api/v1/post/finalize?postId=${postId}`,
      {},
      authConfig,
    );
    
    console.log('Finalize Post response:', response);
    console.log('Finalize Post  response data:', response.data);
    return response.data;
  } catch (error) {
    console.log('Finalize Post service error');
    console.log('Finalize Post service error message', error);

    throw error;
  }
};

export default {
  getAllPosts,
  createNewPost,
  updatePost,
  deletePostMedia,
  deletePost,
  addPostMedia,
  getAllPublicPosts,
  getPostById,
  getPostOfUser,
  getAllFollowedPost,
  unlinkPostFromChallenge,
  likeUnlikePost,
  likeUnlikeChallengeFromPost,
  removeLikeUnlikePost,
  removeLikeUnlikeChallengeFromPost,
  getLikeUserList,
  finalizePost,
};
