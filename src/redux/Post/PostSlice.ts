import {createSlice} from '@reduxjs/toolkit';
import {
  fetchPosts,
  createNewPost,
  updatePost,
  deletePost,
  addPostMedia,
  fetchPublicPosts,
  deletePostMedia,
  deletePublicFeedChallenge,
  getPostOfUser,
  fetchFollowedPosts,
  unlinkPostFromChallenge,
  updateUserPost,
  likeUnlikePost,
  likeUnlikeChallengeFromPost,
  removeLikeUnlikePost,
  removeLikeUnlikeChallengeFromPost,
  finalizePost,
} from './PostAction';
import {logout} from '../User/UserAction';
import {deleteUser} from '../User/UserAction';

export interface Post {
  id: string;
  title?: string;
  text: string;
  views: any;
  likes: any;
  tags?: string[];
  mediaRefs?: string[];
  challengeId: any;
  userId: string;
  visibility: string;
  draft: boolean;
  createdAt: string;
  updatedAt: string;
}

type StateType = {
  posts: Array<Post>;
  publicPost: {
    data: Array<Post>;
    nextPageToken: string;
    hasNext: boolean;
  };
  followPost: {
    data: Array<Post>;
    nextPageToken: string;
    hasNext: boolean;
  };
  userPost: {
    data: Array<Post>;
    nextPageToken: string;
    hasNext: boolean;
  };
  status: string;
  error: any;
  isCreateSuccess: boolean;
  isAddMediaSuccess: boolean;
  mute: boolean;
  isUpdateSuccess: boolean;
  isFinalizeSuccess: boolean;
  postDraft: any;
};

const initialState: StateType = {
  posts: [],
  publicPost: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  userPost: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  followPost: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  status: 'idle',
  error: null,
  isCreateSuccess: false,
  isUpdateSuccess: false,
  isAddMediaSuccess: false,
  postDraft: [],
  mute: false,
};

const postSlice = createSlice({
  name: 'posts',
  initialState,
  reducers: {
    resetCreateSuccess: state => {
      state.isCreateSuccess = false;
      state.postDraft = [];
    },
    updatePostData: (state, action) => {
      state.isCreateSuccess = false;
      state.postDraft = action.payload;
    },
    resetUpdateSuccess: state => {
      state.isUpdateSuccess = false;
    },
    resetFinalizeSuccess: state => {
      state.isFinalizeSuccess = false;
    },
    resetAddMediaSuccess: state => {
      state.isAddMediaSuccess = false;
    },
    muteUnMuteVideo: (state, action) => {
      state.mute = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchPublicPosts.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchPublicPosts.fulfilled, (state, action) => {
        console.log('fetchPublicPosts.fulfilled', action.payload.data);
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.publicPost.data, ...action.payload.data]
          : action.payload.data;
        state.publicPost = action.payload;
        state.publicPost.hasNext = hasNext;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(fetchPublicPosts.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(fetchFollowedPosts.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchFollowedPosts.fulfilled, (state, action) => {
        console.log('fetchPublicPosts.fulfilled', action.payload.data);
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.followPost.data, ...action.payload.data]
          : action.payload.data;
        state.followPost = action.payload;
        state.followPost.hasNext = hasNext;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(fetchFollowedPosts.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getPostOfUser.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(getPostOfUser.fulfilled, (state, action) => {
        console.log('fetchPublicPosts.fulfilled', action.payload.data);
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.userPost.data, ...action.payload.data]
          : action.payload.data;
        state.userPost = action.payload;
        state.userPost.hasNext = hasNext;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(getPostOfUser.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(fetchPosts.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchPosts.fulfilled, (state, action) => {
        state.posts = action.payload;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(fetchPosts.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(createNewPost.pending, state => {
        console.log('Create New post is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(createNewPost.fulfilled, (state, action) => {
        console.log('Create New post is fulfilled');
        // state.posts.push(action.payload);
        console.log('post create action ', action);
        console.log('post create action payload', action.payload);

        // console.log('post create', state.posts);
        state.isCreateSuccess = true;
        state.postDraft = action.payload;
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(createNewPost.rejected, (state, action) => {
        console.log('Create New post is rejected');
        console.log('action', action);
        state.isCreateSuccess = false;
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(updatePost.pending, state => {
        console.log('Update post is pending');

        state.status = 'loading';
        state.error = null;
      })
      .addCase(updatePost.fulfilled, (state, action) => {
        console.log('Update post is fulfilled');
        state.status = 'succeeded';
        state.error = null;
        state.isUpdateSuccess = true;
        console.log('Update New post action payload', action.payload);
      })
      .addCase(updatePost.rejected, (state, action) => {
        console.log('Update post is rejected');

        state.status = 'failed';
        state.error = action.payload;
        state.isUpdateSuccess = false;
      })
      .addCase(addPostMedia.pending, state => {
        console.log('Add Media to post is pending');

        state.status = 'loading';
        state.error = null;
      })
      .addCase(addPostMedia.fulfilled, (state, action) => {
        console.log('Add media to post is fulfilled');
        state.status = 'succeeded';
        state.error = null;
        state.isAddMediaSuccess = true;
        console.log('Update New post action payload', action.payload);
      })
      .addCase(addPostMedia.rejected, (state, action) => {
        console.log('Add media to post is rejected');

        state.status = 'failed';
        state.error = action.payload;
        state.isAddMediaSuccess = false;
      })
      .addCase(deletePost.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(deletePost.fulfilled, (state, action) => {
        if (action.payload.code == 200) {
          state.posts = state.posts.filter(
            (item: any) => action.meta.arg !== item.id,
          );
          state.publicPost.data = state.publicPost.data.filter(
            (item: any) => action.meta.arg !== item.id,
          );
          state.userPost.data = state.userPost.data.filter(
            (item: any) => action.meta.arg !== item.id,
          );
        }
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(deletePost.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(deletePublicFeedChallenge.pending, state => {
        state.status = 'loading';
        state.error = null;
        console.log('deletechallengeFeed is pending');

      })
      .addCase(deletePublicFeedChallenge.fulfilled, (state, action) => {
        state.publicPost.data = state.publicPost.data.filter(
          (item: any) => action.meta.arg !== item.id,
        );
        console.log('deletechallengeFeed is fulfilled');

        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(deletePublicFeedChallenge.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        console.log('deletechallengeFeed is rejected');

      })
      .addCase(deletePostMedia.pending, state => {
        console.log('deletePostMedia post is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(deletePostMedia.fulfilled, (state, action) => {})
      .addCase(deletePostMedia.rejected, (state, action) => {
        console.log('Create New post is rejected');
        console.log('action', action);
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(logout.fulfilled, () => {
        console.log('user logout fulfilled from post slice');
        return initialState;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        console.log('user delete fulfilled from post slice');
        return initialState;
      })
      .addCase(unlinkPostFromChallenge.rejected, (state, action) => {
        console.log('Unlink Post from challenge is rejected');
        console.log('action', action);
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(unlinkPostFromChallenge.fulfilled, (state, action) => {
        console.log('Unlink Post from challenge is fulfilled');
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(unlinkPostFromChallenge.pending, state => {
        console.log('Unlink Post from challenge is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(updateUserPost.fulfilled, (state, action) => {
        if (action.payload) {
          const userPost = state.userPost.data;
          const updatedPost = userPost.map((post: any) => {
            if (post.id === action.payload.id) {
              return action.payload;
            }
            return post;
          });
          state.userPost.data = updatedPost;
        }
      })
      .addCase(likeUnlikePost.pending, (state, action) => {
        if (action.meta) {
          const userPost = state.publicPost.data;
          const updatedPost = userPost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = true;
              post.likes = post.likes + 1;
              return post;
            }
            return post;
          });
          state.publicPost.data = updatedPost;

          const userPrivatePost = state.followPost.data;
          const updatedPrivatePost = userPrivatePost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = true;
              post.likes = post.likes + 1;
              return post;
            }
            return post;
          });
          state.followPost.data = updatedPrivatePost;

          const userPostProfile = state.userPost.data;
          const updatedPrivatePostProfile = userPostProfile.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = true;
              post.likes = post.likes + 1;
              return post;
            }
            return post;
          });
          state.userPost.data = updatedPrivatePostProfile;
        }
      })
      .addCase(likeUnlikeChallengeFromPost.pending, (state, action) => {
        if (action.meta) {
          const userPost = state.publicPost.data;
          const updatedPost = userPost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = true;
              post.likes = post.likes + 1;
              return post;
            }
            return post;
          });
          state.publicPost.data = updatedPost;

          const userPrivatePost = state.followPost.data;
          const updatedPrivatePost = userPrivatePost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = true;
              post.likes = post.likes + 1;
              return post;
            }
            return post;
          });
          state.followPost.data = updatedPrivatePost;

          const userPostProfile = state.userPost.data;
          const updatedPrivatePostProfile = userPostProfile.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = true;
              post.likes = post.likes + 1;
              return post;
            }
            return post;
          });
          state.userPost.data = updatedPrivatePostProfile;
        }
      })
      .addCase(removeLikeUnlikePost.pending, (state, action) => {
        if (action.meta) {
          const userPost = state.publicPost.data;
          const updatedPost = userPost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = false;
              post.likes = post.likes - 1;
              return post;
            }
            return post;
          });
          state.publicPost.data = updatedPost;

          const userPrivatePost = state.followPost.data;
          const updatedPrivatePost = userPrivatePost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = false;
              post.likes = post.likes - 1;
              return post;
            }
            return post;
          });
          state.followPost.data = updatedPrivatePost;

          const userPostProfile = state.userPost.data;
          const updatedPrivatePostProfile = userPostProfile.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = false;
              post.likes = post.likes - 1;
              return post;
            }
            return post;
          });
          state.userPost.data = updatedPrivatePostProfile;
        }
      })
      .addCase(removeLikeUnlikeChallengeFromPost.pending, (state, action) => {
        if (action.meta) {
          const userPost = state.publicPost.data;
          const updatedPost = userPost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = false;
              post.likes = post.likes - 1;
              return post;
            }
            return post;
          });
          state.publicPost.data = updatedPost;

          const userPrivatePost = state.followPost.data;
          const updatedPrivatePost = userPrivatePost.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = false;
              post.likes = post.likes - 1;
              return post;
            }
            return post;
          });
          state.followPost.data = updatedPrivatePost;

          const userPostProfile = state.userPost.data;
          const updatedPrivatePostProfile = userPostProfile.map((post: any) => {
            if (post.id === action.meta.arg.contentId) {
              post.isLiked = false;
              post.likes = post.likes - 1;
              return post;
            }
            return post;
          });
          state.userPost.data = updatedPrivatePostProfile;
        }
      })
      .addCase(finalizePost.pending, state => {
        console.log('Finalize post is pending');
        state.status = 'loading';
        state.error = null;
      })
      .addCase(finalizePost.fulfilled, (state, action) => {
        console.log('Finalize post is fulfilled');
        state.status = 'succeeded';
        state.error = null;
        state.isFinalizeSuccess = true;
        console.log('Finalize New post action payload', action.payload);
      })
      .addCase(finalizePost.rejected, (state, action) => {
        console.log('Finalize post is rejected');

        state.status = 'failed';
        state.error = action.payload;
        state.isFinalizeSuccess = false;
      });
  },
});
export const {
  resetCreateSuccess,
  resetUpdateSuccess,
  resetFinalizeSuccess,
  resetAddMediaSuccess,
  updatePostData,
  muteUnMuteVideo,
} = postSlice.actions;
export default postSlice.reducer;
