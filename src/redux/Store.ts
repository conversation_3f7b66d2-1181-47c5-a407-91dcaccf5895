import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Use localStorage for web
import authReducer from './Auth/AuthSlice';
import userReducer from './User/UserSlice';
import postReducer from './Post/PostSlice';
import challengeReducer from './Challenge/ChallengeSlice';
import leaderBoardReducer from './LeaderBoard/LeaderBoardSlice';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Configure persist for authReducer
const authPersistConfig = {
  key: 'auth',
  storage: AsyncStorage, // Use localStorage if using web
};

const persistedAuthReducer = persistReducer(authPersistConfig, authReducer);
// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Create typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export const store = configureStore({
  reducer: {
    auth: persistedAuthReducer, // Use persisted reducer for auth
    user: userReducer,
    post: postReducer,
    challenge: challengeReducer,
    leaderBoard: leaderBoardReducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Export persistor
export const persistor = persistStore(store);
