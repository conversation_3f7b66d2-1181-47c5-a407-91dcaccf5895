import UserService from './UserService';
import {createAsyncThunk} from '@reduxjs/toolkit';

//getUserDetails
export const getUserDetails: any = createAsyncThunk(
  'user/getUserDetails',
  async (userId: any, thunkAPI) => {
    try {
      console.log('Userdetails action service was run', 'userId:', userId);
      const response: any = await UserService.getUserDetails(userId);
      return response;
    } catch (error) {
      console.log('Userdetails action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const searchUser: any = createAsyncThunk(
  'user/searchUser',
  async (body: {firstName: string}, thunkAPI) => {
    try {
      const response: any = await UserService.searchUser(body);
      return response;
    } catch (error) {
      console.log('Userdetails action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const updateUser: any = createAsyncThunk(
  'user/updateUser',
  async (userData: any, thunkAPI) => {
    try {
      console.log('Update user action service was run');
      console.log('Updated user data', userData);
      const response = await UserService.updateUser(userData);
      return response;
    } catch (error) {
      console.log('Update user action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
//logout
export const logout = createAsyncThunk('user/logout', async (_, thunkAPI) => {
  try {
    await UserService.logout(); // Clears AsyncStorage
    return; // No payload needed for Redux
  } catch (error) {
    console.log('Error during logout:', error);
    return thunkAPI.rejectWithValue(error);
  }
});
//DeleteUser
export const deleteUser: any = createAsyncThunk(
  'user/deleteUser',
  async (userId: any, thunkAPI) => {
    try {
      console.log('Delete User action service was run', 'userId:', userId);
      const response: any = await UserService.deleteUser(userId);
      return response;
    } catch (error) {
      console.log('Delete User action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const followUser: any = createAsyncThunk(
  'user/followUser',
  async (userBody: {followedId: string; followerId: string}, thunkAPI) => {
    try {
      const response: any = await UserService.followUser(userBody);
      console.log('followUser action service was run', 'userId:', response);
      return response;
    } catch (error) {
      console.log('followUser action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const unFollowUser: any = createAsyncThunk(
  'user/unFollowUser',
  async (userBody: {followedId: string; followerId: string}, thunkAPI) => {
    try {
      const response: any = await UserService.unFollowUser(userBody);
      console.log('unFollowUser action service was run', 'userId:', response);
      return response;
    } catch (error) {
      console.log('unFollowUser action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const removeUser: any = createAsyncThunk(
  'user/removeUser',
  async (userBody: {followerId: string}, thunkAPI) => {
    try {
      const response: any = await UserService.removeUser(userBody);
      console.log('removeUser action service was run', 'userId:', response);
      return response;
    } catch (error) {
      console.log('removeUser action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getFollowing: any = createAsyncThunk(
  'user/getFollowing',
  async (data: {userId: string; nextPageToken: string}, thunkAPI) => {
    try {
      const response: any = await UserService.getFollowing(data);
      return response;
    } catch (error) {
      console.log('user followers action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getFollowers: any = createAsyncThunk(
  'user/getFollowers',
  async (data: {userId: string; nextPageToken: string}, thunkAPI) => {
    try {
      console.log('user following action service was run', 'userId:', data);
      const response: any = await UserService.getFollowers(data);
      return response;
    } catch (error) {
      console.log('user following action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const addUserImage: any = createAsyncThunk(
  'user/addUserImage',
  async (data: any, thunkAPI) => {
    try {
      const response: any = await UserService.addUserImage(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const deleteUserImage: any = createAsyncThunk(
  'user/deleteUserImage',
  async (empty: string, thunkAPI) => {
    try {
      const response: any = await UserService.deleteUserImage();
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const addFcmTokem: any = createAsyncThunk(
  'user/addFcmTokem',
  async (data: any, thunkAPI) => {
    try {
      const response: any = await UserService.addFcmTokem(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

