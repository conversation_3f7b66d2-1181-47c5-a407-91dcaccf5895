import axios, { AxiosError } from 'axios';
import {baseUrl, config} from '../../utils/Utils';
import AsyncStorage from '@react-native-async-storage/async-storage';

const logout = async () => {
  try {
    // Clear all data stored in AsyncStorage
    await AsyncStorage.clear();
    console.log('User logged out, local storage cleared.');
    return true; // Return success status
  } catch (error) {
    console.log('Error clearing local storage during logout:', error);
    throw error;
  }
};

const getUserDetails = async (userId: string) => {
  try {
    let authConfig = await config();
    //     console.log('UserServices Line 8: ', authConfig.headers.Authorization);
    const response = await axios.get(
      `${baseUrl}/api/v1/users/details/${userId}`,
      authConfig,
    );
    console.log('userdetails service was run', 'user:', userId);

    console.log('response.data.body.data', response.data.body.data);
    return response.data.body.data;
  } catch (error) {
    console.log('userdetails service error');

    console.log('Error message', error);
    throw error;
  }
};

const searchUser = async (body: {firstName: string}) => {
  try {
    let authConfig = await config();
    const response = await axios.post(
      `${baseUrl}/api/v1/search/user`,
      body,
      authConfig,
    );
    return response.data;
  } catch (error) {
    console.log('searchUser service error');
    console.log(error);
    throw error;
  }
};

const updateUser = async (updatedData: any) => {
  try {
    let authConfig = await config();

    console.log('user data', updatedData);
    // console.log('auth', authConfig);
    const response = await axios.put(
      `${baseUrl}/api/v1/users`,
      updatedData,
      authConfig,
    );

    console.log('User update response body data:', response.data.body.data);
    return response.data.body.data;
  } catch (error) {
    console.log('UpdateUser service error');
    console.log('Update service error message', error);

    throw error;
  }
};
const deleteUser = async (userId: string) => {
  try {
    let authConfig = await config();
    //     console.log('UserServices Line 8: ', authConfig.headers.Authorization);

    const response = await axios.delete(`${baseUrl}/api/v1/users`, authConfig);
    console.log('Delete User service was run', 'user:', userId);
    await AsyncStorage.clear();
    console.log('User logged out, local storage cleared.');
    console.log('response.data.body', response.data.body);
    return response.data.body;
  } catch (error) {
    console.log('Delete User service error');

    console.log('Error message', error);
    throw error;
  }
};
const followUser = async (body: {followedId: string; followerId: string}) => {
  try {
    let authConfig = await config();
    const response = await axios.put(
      `${baseUrl}/api/v1/connection/follow`,
      body,
      authConfig,
    );
    return response.data.body;
  } catch (error) {
    throw error;
  }
};

const addFcmTokem = async (body: {
  deviceToken: string;
  isNotificationsEnabled: boolean;
}) => {
  try {
    let authConfig = await config();
    const response = await axios.post(
      `${baseUrl}/api/v1/settings/register-device-token`,
      body,
      authConfig,
    );
    console.log('addFcmTokem', response.data);
    
    return response.data;
  } catch (error: any) {
    console.log('addFcmTokem', error);
    console.log('addFcmTokem', error.message);
    console.log('addFcmTokem', error.request);
    console.log('addFcmTokem', error.response);
    console.log('addFcmTokem', error.response?.data);
    throw error;
  }
};

const unFollowUser = async (body: {followedId: string; followerId: string}) => {
  try {
    let authConfig = await config();
    const response = await axios.put(
      `${baseUrl}/api/v1/connection/unfollow`,
      body,
      authConfig,
    );
    return response.data.body;
  } catch (error) {
    throw error;
  }
};
const getFollowers = async (data: {userId: string; nextPageToken: string}) => {
  try {
    let authConfig = await config();
    //     console.log('UserServices Line 8: ', authConfig.headers.Authorization);
    let url = `${baseUrl}/api/v1/connection/${data.userId}/followers`;
    if (data.nextPageToken) {
      url = `${baseUrl}/api/v1/connection/${data.userId}/followers?pageToken=${data.nextPageToken}`;
    }

    const response = await axios.get(url, authConfig);
    console.log('getFollowers service was run', 'user:', data.userId);

    // console.log('response.data.body.data', response.data.body.data);
    // console.log('response.data', response.data);
    console.log('response.data', response.data.data);
    return response.data;
  } catch (error) {
    console.log('getFollowers service error');

    console.log('Error message', error);
    throw error;
  }
};
const getFollowing = async (data: {userId: string; nextPageToken: string}) => {
  try {
    let authConfig = await config();
    //     console.log('UserServices Line 8: ', authConfig.headers.Authorization);
    let url = `${baseUrl}/api/v1/connection/${data.userId}/following`;
    if (data.nextPageToken) {
      url = `${baseUrl}/api/v1/connection/${data.userId}/following?pageToken=${data.nextPageToken}`;
    }

    const response = await axios.get(url, authConfig);
    console.log('getFollowing service was run', 'user:', data);

    // console.log('response.data.body.data', response.data.body.data);
    // console.log('response.data', response.data);
    console.log('response.data', response.data.data);
    return response.data;
  } catch (error) {
    console.log('getFollowing service error');

    console.log('Error message', error);
    throw error;
  }
};
const addUserImage = async (mediaFile: any) => {
  try {
    let authConfig = await config();
    const formData = new FormData();
    formData.append('file', {
      uri: mediaFile.uri,
      name: mediaFile.name,
      type: mediaFile.type,
    });

    const response = await axios.patch(
      `${baseUrl}/api/v1/users/image/add`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...authConfig.headers,
        },
      },
    );
    console.log('response.data addUserImage', response.data);
    return response.data;
  } catch (error) {
    console.log('response.data addUserImage', error);
    throw error;
  }
};
const deleteUserImage = async () => {
  try {
    let authConfig = await config();
    const response = await axios.patch(
      `${baseUrl}/api/v1/users/image/delete`,
      null,
      {
        headers: {
          ...authConfig.headers,
        },
      },
    );
    console.log('response.data deleteUserImage', response.data);
    return response.data;
  } catch (error) {
    console.log('response.data deleteUserImage', error);
    throw error;
  }
};
const removeUser = async (body: {followerId: string}) => {
  try {
    let authConfig = await config();
    const response = await axios.put(
      `${baseUrl}/api/v1/connection/remove`,
      body,
      authConfig,
    );
    return response.data.body;
  } catch (error) {
    throw error;
  }
};
export default {
  getUserDetails,
  searchUser,
  updateUser,
  logout,
  deleteUser,
  followUser,
  unFollowUser,
  getFollowers,
  getFollowing,
  addUserImage,
  deleteUserImage,
  removeUser,
  addFcmTokem,
};
