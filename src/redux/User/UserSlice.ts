import {createSlice} from '@reduxjs/toolkit';
import {getUserDetails, updateUser, logout, deleteUser, getFollowing, getFollowers,addUserImage,deleteUserImage} from './UserAction';
// import {logout} from '../Logout/LogoutAction';

const initialState = {
  user: [],
  isError: false,
  isLoading: false,
  isGetUserSuccess: false,
  isUpdateSuccess: false,
  isDeleteSuccess: false,
  message: '',
  userProfile: {},
  following: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
  follower: {
    data: [],
    nextPageToken: '',
    hasNext: false,
  },
};
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    resetUpdateSuccess: state => {
      state.isUpdateSuccess = false;
    },
    resetDeleteSuccess: state => {
      state.isDeleteSuccess = false;
    },
  },
  extraReducers: builder => {
    builder
      //get user details
      .addCase(getUserDetails.pending, state => {
        console.log('User details pending');
        state.isLoading = true;
      })
      .addCase(getUserDetails.fulfilled, (state, action) => {
        console.log(
          'User details is fulfilled ',
          'action:',
          action,
          'state:',
          state,
        );
        state.isLoading = false;
        state.isError = false;
        state.isGetUserSuccess = true;
        state.userProfile = action.payload;
        state.message = 'Success';
        console.log('action payload:', action.payload);
        console.log('userprofile', state.userProfile);
      })
      .addCase(getUserDetails.rejected, (state, action) => {
        console.log('User details is rejected');
        state.isLoading = false;
        state.isError = true;
        state.isGetUserSuccess = false;
        state.message = 'error';
      })
      .addCase(updateUser.pending, state => {
        console.log('Update user is pending');
        state.message = 'loading';
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        console.log('Update user is fulfilled');
        state.message = 'succeeded';
        state.isError = false;
        state.isUpdateSuccess = true;
        console.log('Update user action payload', action.payload);
      })
      .addCase(updateUser.rejected, (state, action) => {
        console.log('Update user is rejected');
        state.message = 'failed';
        state.isError = action.payload;
        state.isUpdateSuccess = false;
      })
      .addCase(deleteUser.pending, state => {
        console.log('Delete user is pending');
        state.message = 'loading';
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        console.log('user delete fulfilled from user slice');
        return initialState;
      })
      .addCase(deleteUser.rejected, (state, action) => {
        console.log('Delete user is rejected');
        state.message = 'failed';
        state.isError = action.payload;
        state.isDeleteSuccess = false;
      })
      .addCase(getFollowing.pending, state => {
        console.log('getFollowing user is pending');
        state.message = 'loading';
      })
      .addCase(getFollowing.fulfilled, (state, action) => {
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.following.data, ...action.payload.data]
          : action.payload.data;
        state.following = action.payload;
        state.following.hasNext = hasNext;
        state.message = 'succeeded';
        state.isError = false;
      })
      .addCase(getFollowing.rejected, (state, action) => {
        console.log('getFollowing user is rejected');
        state.message = 'failed';
        state.isError = action.payload;
        state.isDeleteSuccess = false;
      })
      .addCase(getFollowers.pending, state => {
        console.log('getFollowers user is pending');
        state.message = 'loading';
      })
      .addCase(getFollowers.fulfilled, (state, action) => {
        const nextPageTokenArg = action.meta.arg.nextPageToken;
        const hasNext = action.payload.data.length > 0;
        action.payload.data = nextPageTokenArg
          ? [...state.follower.data, ...action.payload.data]
          : action.payload.data;
        state.follower = action.payload;
        state.follower.hasNext = hasNext;
        state.message = 'succeeded';
        state.isError = false;
      })
      .addCase(getFollowers.rejected, (state, action) => {
        console.log('getFollowers user is rejected');
        state.message = 'failed';
        state.isError = action.payload;
        state.isDeleteSuccess = false;
      })
      .addCase(addUserImage.pending, (state) => {
        state.message = 'Uploading image...';
        console.log('addUserImage is pending');

      })
      .addCase(addUserImage.fulfilled, (state, action) => {
        state.message = 'Image uploaded successfully';
        console.log('addUserImage is fulfilled', action.payload);

      })
      .addCase(addUserImage.rejected, (state, action) => {
        state.isError = true;
        state.message = action.payload?.message || 'Failed to upload image';
        console.log('addUserImage is rejected' );

      })
      
      .addCase(deleteUserImage.pending, (state) => {
        state.message = 'Deleting image...';
        console.log('deleteUserImage is pending');

      })
      .addCase(deleteUserImage.fulfilled, (state) => {
        state.message = 'Image deleted successfully';
        console.log('deleteUserImage is fulfilled',);

      })
      .addCase(deleteUserImage.rejected, (state, action) => {
        state.isError = true;
        state.message = action.payload?.message || 'Failed to delete image';
        console.log('deleteUserImage is rejected');

      })
      .addCase(logout.fulfilled, () => {
        console.log('user logout fulfilled from user slice');
        return initialState;
      });
  },
});
export const {resetUpdateSuccess, resetDeleteSuccess} = userSlice.actions;
export default userSlice.reducer;
