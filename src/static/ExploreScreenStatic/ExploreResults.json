{"Data": [{"userName": "theRock", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": ""}, {"userName": "castlerockEvents", "firstName": "Castle Rock Events", "lastName": "", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": ""}, {"userName": "rocketleague", "firstName": "Rocket league", "lastName": "", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": ""}, {"userName": "rockstargames", "firstName": "Rockstar", "lastName": "Games", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": ""}, {"userName": "theRock", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "Rock'n'Roll Challenge", "hashTag": ""}, {"userName": "", "firstName": "", "lastName": "", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": "#RockOn"}, {"userName": "theHemsworth", "firstName": "<PERSON>", "lastName": "Hemsworth", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "Rock Plank Challenge", "hashTag": ""}, {"userName": "", "firstName": "", "lastName": "", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": "#Rockender"}, {"userName": "theRockadoodle", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "profileImage": "'../../static/Images/beach.jpg'", "challengeName": "", "hashTag": ""}]}