import {StyleSheet} from 'react-native';

export const SignUpStyle = StyleSheet.create({
  Colorcontainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollViewContent: {
    flexGrow: 1, // Ensures the content grows to fill the available space
  },
  loginContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 5,
    flex: 1,
  },
  signupContainer: {
    marginTop: 36,
    alignItems: 'center',
    padding: 5,
    flex: 1,
  },
  backButtonPos: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  headerPos: {
    position: 'absolute',
    width: '100%',
    textAlign: 'center',
    zIndex: -1,
  },
  inputWithErrorContainer: {
    // height: 68, // Increased height to accommodate error message
    // justifyContent: 'center', // Center the input within the container
  },
  passwordInputContainer: {
    borderColor: '#c5c5c5',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    borderWidth: 1,
    width: 250,
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  inputContainer: {
    borderColor: '#c5c5c5',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    borderWidth: 1,
    width: 250,
    height: 48,
    marginBottom: 22,
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  inputs: {
    height: 45,
    marginLeft: 8,
    fontSize: 14,
    // borderBottomColor: '#FFFFFF',
    flex: 1,
    color: 'black',
    fontSize: 14,
  },
  genderButton: {
    flexDirection: 'row',
    // justifyContent: 'flex-start',
    // alignItems: 'center',
  },
  // buttonContainer: {
  //   // backgroundColor: '#fd9644',
  //   height: 45,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginBottom: 20,
  //   marginLeft: 10,
  //   marginRight: 10,
  //   width: 100,
  //   borderRadius: 30,
  // },
  activeButton: {
    // Added active button style
    backgroundColor: '#3498db',
  },
  inactiveButton: {
    // Added inactive button style
    // backgroundColor: '#bdc3c7',
    backgroundColor: '#fd9644',
  },
  signupButton: {
    backgroundColor: '#c3e7f5',
    height: 70,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    width: 225,
    borderRadius: 6,
  },
  signUpText: {
    color: '#000000',
    fontWeight: 'bold',
    fontSize: 18,
  },
  screenRedirectButton: {paddingBottom: 15, flexDirection: 'row'},
});

export default SignUpStyle;
