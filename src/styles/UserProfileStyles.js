import {StyleSheet, Dimensions} from 'react-native';

const {width, height} = Dimensions.get('window');

export const UserProfileScreenStyle = StyleSheet.create({
  container: {
    // justifyContent: 'center',
    paddingTop: 3.3,
    alignItems: 'center',
  },
  displayPic: {
    width: 100,
    height: 100,
    borderRadius: 100 / 2,
  },
  fullName: {
    fontSize: 30,
  },
  userName: {
    fontSize: 20,
  },
  description: {
    fontSize: 15,
    paddingTop: 10,
    paddingLeft: 40,
    paddingRight: 40,
    textAlign: 'center',
  },
  followContainer: {
    justifyContent: 'space-around',
    flexDirection: 'row',
  },
  countContainer: {
    margin: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  count: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  countDescription: {
    fontSize: 12,
    width: 100,
    textAlign: 'center',
  },
  profileTab: {
    height: 40,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eae5e5',
    borderBottomWidth: 1,
    borderBottomColor: '#eae5e5',
  },
  profileTabText: {
    justifyContent: 'center',
    fontSize: 20,
    margin: 1,
  },
  contentContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageContainer: {
    width: width / 3,
    height: width / 3,
    padding: 2,
  },
  image: {
    flex: 1,
    width: undefined,
    height: undefined,
    backgroundColor: '#000000',
  },
});

export default UserProfileScreenStyle;
