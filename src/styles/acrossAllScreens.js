import {Dimensions, StyleSheet} from 'react-native';

export const acrossAllScreens = StyleSheet.create({
  backImageContainer: {
    paddingRight: 10
  },
  backImage: {
    width: 21,
    height: 21,
  },
  logoImage: {
    width: 125,
    height: 45
  },
  H1: {
    fontSize: 24, //TItle
    fontWeight: 'bold',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  H2: {
    fontSize: 14, //Body
    fontWeight: Platform.select({
      ios: '300', // ios font weights - [100, 200, 300, 400, 500, 600, 700, 800, 900, bold, normal]
      android: '300',
    }),
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  H3: {
    fontSize: 14, //Body Bold
    fontWeight: 'bold',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  SectionHeader: {
    fontSize: 20, //Body Bold
    fontWeight: '800',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  InputTextLarge: {
    fontSize: 24, //Input Text for Titles
    fontWeight: 'bold',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  InputTextRegular: {
    fontSize: 14, //Standard Input Text
    // fontWeight: '300',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  ScreenHeaderText: {
    fontSize: 20, //Screenheader
    fontWeight: '600',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  LandingPageText: {
    fontSize: 20, //Screenheader
    // fontWeight: '600',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  ContactUsText: {
    fontSize: 16, //Screenheader
    // fontWeight: '600',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  Napoz: {
    fontSize: 44,
    lineHeight: 44,
    fontWeight: '600',
    textShadowColor: '#2bcbba',
    fontFamily: 'Helvetica Neue',
  },
  ErrorText: {
    fontSize: 12,
    color: 'red', // Example error color
    position: 'absolute',
    bottom: 5, // Position the error message below the inputContainer
    left: 5, // Align with the text input
    // right: 5,
    fontFamily: 'Helvetica Neue',
  },  H2Italic: {
    fontSize: 14, //Body
    fontWeight: Platform.select({
      ios: '300', // ios font weights - [100, 200, 300, 400, 500, 600, 700, 800, 900, bold, normal]
      android: '300',
    }),
    color: '#000000',
    fontFamily: 'Helvetica Neue',
    fontStyle: 'italic',
  },
  ScreenBorders: {
    marginLeft: 16,
    marginRight: 16,
    marginTop: 16,
    marginBottom: 14,
    width: Dimensions.get('window').width - 32,
    height: '97%'
  },
  ScreenBackground: {
    flex: 1,
    backgroundColor: '#ffffff',
    // backgroundColor: '#c3e7f5',
    // padding: 5,
  },
  ButtonBackground: {backgroundColor: '#c3e7f5'},
});

export default acrossAllScreens;
