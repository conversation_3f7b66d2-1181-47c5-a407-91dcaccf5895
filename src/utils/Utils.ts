import AsyncStorage from '@react-native-async-storage/async-storage';
import {jsonType} from '../types/JSON';
//Localhost
const isProd = true;
export const baseUrl = isProd
  ? 'https://prod.napozapis.com:8444'
  : 'https://dev.napozapis.com:8443';
// export const baseUrl = 'https://prod.napozapis.com:8443';
// https://dev.napozapis.com:8443

export const isVideoLink = (url: string) => {
  const path = url?.split('?')[0];
  return path?.endsWith('.mp4');
};
export const getKeyParamFromUrl = (url: string) => {
  if (isProd) {
    return extractRef(url);
  }
  const queryString = url.split('?')[1];
  if (!queryString) return null;

  const pairs = queryString.split('&');
  for (const pair of pairs) {
    const [key, value] = pair.split('=');
    if (decodeURIComponent(key) === 'key') {
      return decodeURIComponent(value);
    }
  }
  return null;
};
export const extractRef = (url: string) => {
  const match = url.match(/\.net\/([^?]*)\?/);
  return match ? match[1] : null;
};
export const getUserFromLocalStorage = async () => {
  try {
    const value = await AsyncStorage.getItem('user');
    console.log('user is', value);
    return value;
  } catch (e) {
    console.log('error getting user data');
    return null;
  }
};

export const getTokenFromLocalStorage = async () => {
  try {
    const tokenValue = await AsyncStorage.getItem('token');
    return tokenValue;
  } catch (error) {
    console.log('error getting token data');
    return null;
  }
};

export const config = async () => {
  const token = await getTokenFromLocalStorage();
  console.log('Utils line 101: ' + 'Bearer ' + token);
  return {
    headers: {
      Authorization: 'Bearer ' + token?.replace(/"/g, ''),
      // 'Content-Type': 'application/json',
    },
  };
};

export const logoBase64 =
  'iVBORw0KGgoAAAANSUhEUgAAADYAAAA2CAYAAACMRWrdAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMBSURBVHgB7ZmBldMwDECVW4AyAWECjgnIbXAbkE5AmSC5Ca5McMcEsEHLBIUJkg1aJhASca+uK+ciO0l7kP+eXxvHli1LtqUWYGJiYmLiQAKBIOKMPrikpqpOkqSGCEhmasnbkbyfMAasDJUFlRXKbKk8UMkUMjPTZ+uRuaKSw1CQ8E8tg0tsjAV88q7Rv0ASVe8KksB7DKcAeZFCuYc+wMZNYikseQXG8wAx9DSJPQX2K6/VckmLUil9VHDZ3NDJuZZeXLV0ijP3OBS+F6LFXoi19ohW81ksh3BqHsyUGoYnkyp9in2AcOa8gmYVv8DwXEuVPldkN0xBDyt0Y8nhkGsLw8Kh3Fu30mexGYRxZz/QgDv6WMOwiHP1KbYDPWvP0fsDhkWc6xX0x52nfg1noC+LHVmL9tbThjb1IR7QlVqq9Cn2C3Q8WcvcgW648xXkCb0mxfkA+w7h/JYqfYppEjx3b32kkuFxyiJNfG4OF+YzhLPu3BKb5K8rmdUvxSZvYhZWPSeodi63EsZ8xDAySYfEEc5HJ8dfvEf4cn1ubxyl79ikJ6V5dO80ViYzjydhUGAYJ95hJ5DwpbUSC1DgWGvPzHq/8FnLaqO1WrdAnRqWVqcVKEA518qt9zPXdcxipM6zhsw3H8kVSyqvTFXXy/UNHFzQ5pFcZW7JL+m5tJ7ZdVOnDXvKO+jGfn61Ly8TQd0hIrFtkW27bgpKqM83Z6xc0//vpsc4Mo/cwmqjSmqp/a0wji4xNiur+dnNZemRWTntrhVzcvvy/FLQgocTLYRKkFdgOCX0Cca5ZGrJkazVlc4ntSa655OrhjBy6zuHXCnoqc0cOqH6UwKbfcCrFpKI7uPFW9DDEdD72D89WsH4KyCEDMaABspxPHIYk5GUy+EcYPNXUMwd54Nldr7XhlIu5viWqDDksh0Co9wG49lcjFI2eJzPaVnCJYNN+KXZd9xWldSeDey+7y7T9Z4DjzPyE9dD66eDFwc2kUplKcTfM/hXMAqe926amJiY+G/5A3Z8mQ3TYH0fAAAAAElFTkSuQmCC';
export const logoBase641 =
  'iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAACpF6WWAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAETSURBVHgBrZSBEcIgDEU5J3ADGaEjdAQ3sCN0A+oEdQNHcARG6Ai4Qd0ghjZojICI/rvcFZI8SIAqhQIAg9aof8nDYNWEpgtzNNoZzaHNaBZtywMu8CpHCToBbAnE1ckgGRBkI8AtLZoELjuGvBqR0H0AGl+lon6k1IqkgfkcsD4ScJnf4Pio0tqJ8Y19azRLLTH4PdD8NazSw3uvggzbTRvxpw+NVvRJ+1SZsN6KnB6xUaHzREG6EGih5J5ndugr0VSVvxGt+kYRoMuWWAEMmqrAEeD8EzjTQyfmbTWQ+WLgsRoowJOIM9VAFusfivxlHmTQWAoUefzl+YPU3NnA83SLgCy3pzwH8kURuFcVolwdxndFeDslCcOx8QAAAABJRU5ErkJggg==';
