export function getTimeDifference(start, end) {
  const timeDifference = Math.abs(start.getTime() - end.getTime());
  const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

  if (daysDifference === 0) {
    const hoursDifference = Math.floor(timeDifference / (1000 * 60 * 60));
    if (hoursDifference < 1) {
      const minutesDifference = Math.floor(timeDifference / (1000 * 60));
      return `${minutesDifference} minutes`;
    } else {
      return `${hoursDifference} hours`;
    }
  } else {
    return `${daysDifference} days`;
  }
}
