import { useEffect } from "react";
import { Linking } from "react-native";
import { navigate } from "./NavigationService"; // Import global navigation helper

const useDeepLinking = () => {
  useEffect(() => {
    const handleDeepLink = (event: { url: string }) => {
      const url = event.url;
      if (url) {
        navigateToScreen(url);
      }
    };

 // Subscribe to deep links
 const subscription = Linking.addEventListener("url", handleDeepLink);

 // Handle initial deep link when app is launched
 Linking.getInitialURL().then((url) => {
   if (url) navigateToScreen(url);
 });

 // Cleanup: Remove event listener properly
 return () => {
   subscription.remove();
 };
}, []);

  const navigateToScreen = (url: string) => {
    try {
      const parsedUrl = new URL(url);
      const pathSegments = parsedUrl.pathname.split("/");

      if (pathSegments.length >= 4) {
        const feature = pathSegments[2]; // "auth" or "challenge"
        const action = pathSegments[3]; // "reset", "verify", etc.
        const param = pathSegments[4] || null; // Token or ID

        if (feature === "auth" && action === "reset" && param) {
          // Navigate to PasswordResetScreen inside WelcomeStack
          navigate("WelcomeStack", {
            screen: "PasswordResetScreen",
            params: { resetToken: param },
          });
        } else if (feature === "challenge" && param) {
          // Navigate to ChallengeDetailScreen inside ChallengeHomeScreenStack
          navigate("HomeStack", {
            screen: "ChallengeDetailScreen",
            params: { challengeId: param },
          });
        } else {
          console.warn("Unknown deep link:", url);
        }
      }
    } catch (error) {
      console.error("Error parsing deep link:", error);
    }
  };
};

export default useDeepLinking;
